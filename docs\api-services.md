# API 服务层文档

## 概述

Svelte Demo 项目的 API 服务层提供了统一的数据访问接口，采用面向对象的设计模式，封装了 HTTP 请求、错误处理、重试机制等功能。服务层作为前端和后端的桥梁，确保数据的一致性和可靠性。

## 服务架构

### 服务层结构

```
服务层架构
├── 基础服务 (BaseApiService)           # 提供统一的 HTTP 请求功能
│   ├── 请求管理                          # GET, POST, PUT, DELETE 方法
│   ├── 错误处理                          # 统一错误处理和类型定义
│   ├── 重试机制                          # 网络失败自动重试
│   └── 超时控制                          # 请求超时和取消机制
├── 具体 API 服务
│   ├── DashboardApiService              # 仪表板数据服务
│   ├── ChartsApiService                 # 图表数据服务
│   ├── CoinSearchApiService             # 币种搜索服务
│   └── RealTimeMessagesApiService       # 实时消息服务
├── 缓存服务 (CacheService)              # 数据缓存和失效管理
├── 数据处理服务                          # 数据转换和验证
└── Mock 服务 (MSW)                      # 开发环境模拟服务
```

### 设计原则

1. **统一接口**: 所有 API 服务继承自基础服务类
2. **类型安全**: 完整的 TypeScript 类型定义
3. **错误处理**: 统一的错误处理和用户友好的错误消息
4. **可配置**: 支持不同环境的配置和功能开关
5. **可测试**: 面向接口编程，便于单元测试和集成测试

## 基础服务类 (BaseApiService)

### 核心功能

BaseApiService 是所有 API 服务的基类，提供以下核心功能：

#### 1. HTTP 请求封装

```typescript
class BaseApiService {
  // GET 请求
  protected async get<T>(endpoint: string, config?: RequestConfig): Promise<ApiResponse<T>>
  
  // POST 请求
  protected async post<T>(endpoint: string, data?: unknown, config?: RequestConfig): Promise<ApiResponse<T>>
  
  // PUT 请求
  protected async put<T>(endpoint: string, data?: unknown, config?: RequestConfig): Promise<ApiResponse<T>>
  
  // DELETE 请求
  protected async delete<T>(endpoint: string, config?: RequestConfig): Promise<ApiResponse<T>>
}
```

#### 2. 请求配置

```typescript
interface RequestConfig extends RequestInit {
  /** 请求超时时间（毫秒） */
  timeout?: number;
  /** 重试配置 */
  retry?: Partial<RetryConfig>;
  /** 是否跳过错误日志 */
  skipErrorLogging?: boolean;
}

interface RetryConfig {
  /** 最大重试次数 */
  maxRetries: number;
  /** 重试延迟（毫秒） */
  retryDelay: number;
  /** 是否使用指数退避 */
  exponentialBackoff: boolean;
  /** 可重试的状态码 */
  retryableStatusCodes: number[];
}
```

#### 3. 响应格式

```typescript
interface ApiResponse<T> {
  data: T;
  status: 'success' | 'error';
  message?: string;
}
```

### 错误处理系统

#### ApiError 类

```typescript
class ApiError extends Error {
  public endpoint: string;
  public statusCode?: number;
  public response?: Response;

  constructor(message: string, endpoint: string, statusCode?: number, response?: Response);

  // 错误类型判断
  isNetworkError(): boolean;
  isClientError(): boolean;    // 4xx
  isServerError(): boolean;    // 5xx
  isTimeoutError(): boolean;

  // 获取用户友好的错误消息
  getUserFriendlyMessage(): string;
}
```

#### 错误类型映射

| 状态码 | 错误类型 | 用户友好消息 |
|--------|----------|--------------|
| 0 或 undefined | 网络错误 | 网络连接失败，请检查您的网络连接 |
| 401 | 认证错误 | 身份验证失败，请重新登录 |
| 403 | 权限错误 | 您没有权限执行此操作 |
| 404 | 资源不存在 | 请求的资源不存在 |
| 408 | 超时错误 | 请求超时，请稍后重试 |
| 429 | 频率限制 | 请求过于频繁，请稍后重试 |
| 5xx | 服务器错误 | 服务器暂时不可用，请稍后重试 |

### 重试机制

#### 默认重试配置

```typescript
const defaultRetryConfig: RetryConfig = {
  maxRetries: 3,
  retryDelay: 1000,
  exponentialBackoff: true,
  retryableStatusCodes: [408, 429, 500, 502, 503, 504]
};
```

#### 重试策略

1. **指数退避**: 重试延迟按指数增长 (1s, 2s, 4s, ...)
2. **状态码判断**: 只对特定状态码进行重试
3. **网络错误重试**: 网络连接失败时自动重试
4. **最大重试限制**: 避免无限重试

### 使用示例

```typescript
// 创建基础服务实例
const apiService = new BaseApiService('https://api.example.com');

// 发送请求
try {
  const response = await apiService.get<UserData>('/users/123');
  console.log(response.data);
} catch (error) {
  if (error instanceof ApiError) {
    console.error(error.getUserFriendlyMessage());
  }
}

// 自定义配置
const response = await apiService.post('/users', userData, {
  timeout: 5000,
  retry: { maxRetries: 1, exponentialBackoff: false }
});
```

## 具体 API 服务

### DashboardApiService 仪表板服务

负责仪表板相关的数据获取。

#### 接口定义

```typescript
class DashboardApiService extends BaseApiService {
  /**
   * 获取仪表板统计数据
   */
  async getStats(): Promise<ApiResponse<DashboardStats>>;

  /**
   * 获取仪表板概览数据
   */
  async getOverview(): Promise<ApiResponse<unknown>>;
}
```

#### 数据类型

```typescript
interface DashboardStats {
  totalUsers: number;
  monthlyRevenue: number;
  conversionRate: number;
  activeUsers: number;
}
```

#### 使用示例

```typescript
import { dashboardApi } from '$lib/services/api';

// 获取统计数据
async function loadDashboardStats() {
  try {
    const response = await dashboardApi.getStats();
    return response.data;
  } catch (error) {
    console.error('加载仪表板数据失败:', error);
    throw error;
  }
}
```

### ChartsApiService 图表服务

负责各种图表数据的获取。

#### 接口定义

```typescript
class ChartsApiService extends BaseApiService {
  async getBarChartData(): Promise<ApiResponse<BarChartItem[]>>;
  async getLineChartData(): Promise<ApiResponse<LineChartItem[]>>;
  async getPieChartData(): Promise<ApiResponse<PieChartItem[]>>;
  async getScatterChartData(): Promise<ApiResponse<ScatterChartItem[]>>;
  async getAllChartData(): Promise<ApiResponse<ChartData>>;
}
```

#### 数据类型

```typescript
interface BarChartItem {
  name: string;
  value: number;
}

interface LineChartItem {
  name: string;
  value: number;
}

interface PieChartItem {
  name: string;
  value: number;
}

interface ScatterChartItem {
  x: number;
  y: number;
  name?: string;
}

interface ChartData {
  barChartData: BarChartItem[];
  lineChartData: LineChartItem[];
  pieChartData: PieChartItem[];
  scatterChartData: ScatterChartItem[];
}
```

#### 使用示例

```typescript
import { chartsApi } from '$lib/services/api';

// 获取所有图表数据
async function loadAllChartData() {
  try {
    const response = await chartsApi.getAllChartData();
    return response.data;
  } catch (error) {
    console.error('加载图表数据失败:', error);
    throw error;
  }
}

// 获取单个图表数据
async function loadBarChartData() {
  const response = await chartsApi.getBarChartData();
  return response.data;
}
```

### CoinSearchApiService 币种搜索服务

提供币种搜索和查询功能。

#### 接口定义

```typescript
class CoinSearchApiService extends BaseApiService {
  /**
   * 搜索币种
   */
  async searchCoins(query: string): Promise<ApiResponse<CoinInfo[]>>;

  /**
   * 获取币种详情
   */
  async getCoinDetails(coinId: string): Promise<ApiResponse<CoinInfo>>;

  /**
   * 获取热门币种
   */
  async getPopularCoins(limit?: number): Promise<ApiResponse<CoinInfo[]>>;
}
```

#### 使用示例

```typescript
import { coinSearchApiService } from '$lib/services/api';

// 搜索币种
async function searchCoins(query: string) {
  if (query.length < 2) return [];
  
  try {
    const response = await coinSearchApiService.searchCoins(query);
    return response.data;
  } catch (error) {
    console.error('搜索币种失败:', error);
    return [];
  }
}
```

### RealTimeMessagesApiService 实时消息服务

处理实时消息和 WebSocket 连接。

#### 接口定义

```typescript
class RealTimeMessagesApiService extends BaseApiService {
  /**
   * 获取消息历史
   */
  async getMessageHistory(params: MessageHistoryParams): Promise<ApiResponse<MarketEvent[]>>;

  /**
   * 标记消息为已读
   */
  async markAsRead(messageIds: string[]): Promise<ApiResponse<void>>;

  /**
   * 获取未读消息数量
   */
  async getUnreadCount(): Promise<ApiResponse<{ count: number }>>;
}
```

## 缓存服务 (CacheService)

### 功能特性

1. **内存缓存**: 快速访问频繁使用的数据
2. **LRU 策略**: 自动淘汰最久未使用的缓存项
3. **TTL 支持**: 缓存项自动过期机制
4. **分层缓存**: 支持多级缓存策略

### 接口定义

```typescript
class CacheService<T> {
  constructor(maxSize: number = 100, defaultTTL: number = 5 * 60 * 1000);

  /**
   * 设置缓存项
   */
  set(key: string, value: T, ttl?: number): void;

  /**
   * 获取缓存项
   */
  get(key: string): T | null;

  /**
   * 检查缓存项是否存在
   */
  has(key: string): boolean;

  /**
   * 删除缓存项
   */
  delete(key: string): boolean;

  /**
   * 清空缓存
   */
  clear(): void;

  /**
   * 获取缓存统计信息
   */
  getStats(): CacheStats;
}
```

### 使用示例

```typescript
import { CacheService } from '$lib/services/cache';

// 创建缓存实例
const chartDataCache = new CacheService<ChartData>(50, 10 * 60 * 1000); // 50项，10分钟TTL

// 缓存装饰器
function cached<T>(cacheKey: string, ttl?: number) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
      const cached = chartDataCache.get(cacheKey);
      if (cached) {
        return cached;
      }
      
      const result = await originalMethod.apply(this, args);
      chartDataCache.set(cacheKey, result, ttl);
      return result;
    };
    
    return descriptor;
  };
}

// 使用缓存装饰器
class ChartsService {
  @cached('all-chart-data', 5 * 60 * 1000)
  async getAllChartData() {
    return chartsApi.getAllChartData();
  }
}
```

## Mock 服务系统 (MSW)

### Mock 服务架构

项目使用 [MSW (Mock Service Worker)](https://mswjs.io/) 提供开发环境的 API 模拟。

```
Mock 服务结构
├── handlers/                    # API 处理器
│   ├── dashboard.ts            # 仪表板 API mock
│   ├── charts.ts               # 图表 API mock
│   ├── coinSearch.ts           # 币种搜索 API mock
│   └── realTimeMessages.ts     # 实时消息 API mock
├── data/                       # 模拟数据
│   ├── dashboardData.ts        # 仪表板数据
│   ├── chartData.ts            # 图表数据
│   └── coinData.ts             # 币种数据
└── server.ts                   # Mock 服务器配置
```

### Mock 处理器示例

```typescript
// src/mocks/handlers/dashboard.ts
import { http, HttpResponse } from 'msw';
import { generateDashboardStats } from '../data/dashboardData';

export const dashboardHandlers = [
  // 获取仪表板统计数据
  http.get('/api/dashboard/stats', () => {
    const stats = generateDashboardStats();
    return HttpResponse.json({
      data: stats,
      status: 'success'
    });
  }),

  // 模拟网络延迟
  http.get('/api/dashboard/overview', async () => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    return HttpResponse.json({
      data: { overview: 'data' },
      status: 'success'
    });
  })
];
```

### Mock 数据生成

```typescript
// src/mocks/data/dashboardData.ts
export function generateDashboardStats(): DashboardStats {
  return {
    totalUsers: Math.floor(Math.random() * 10000) + 1000,
    monthlyRevenue: Math.floor(Math.random() * 100000) + 10000,
    conversionRate: Math.random() * 10 + 5,
    activeUsers: Math.floor(Math.random() * 5000) + 500
  };
}

export function generateChartData(): ChartData {
  const months = ['1月', '2月', '3月', '4月', '5月', '6月'];
  
  return {
    barChartData: months.map(month => ({
      name: month,
      value: Math.floor(Math.random() * 1000) + 100
    })),
    lineChartData: months.map(month => ({
      name: month,
      value: Math.floor(Math.random() * 500) + 50
    })),
    pieChartData: [
      { name: '类型A', value: Math.floor(Math.random() * 300) + 100 },
      { name: '类型B', value: Math.floor(Math.random() * 300) + 100 },
      { name: '类型C', value: Math.floor(Math.random() * 300) + 100 }
    ],
    scatterChartData: Array.from({ length: 20 }, (_, i) => ({
      x: Math.random() * 100,
      y: Math.random() * 100,
      name: `点${i + 1}`
    }))
  };
}
```

### 错误模拟

Mock 服务支持各种错误场景的模拟：

```typescript
// 模拟网络错误
http.get('/api/error-test', () => {
  return HttpResponse.error();
});

// 模拟超时
http.get('/api/timeout-test', async () => {
  await new Promise(resolve => setTimeout(resolve, 15000));
  return HttpResponse.json({ data: 'too late' });
});

// 模拟服务器错误
http.get('/api/server-error', () => {
  return new HttpResponse(null, { status: 500 });
});

// 模拟随机错误
http.get('/api/random-error', () => {
  if (Math.random() > 0.7) {
    return new HttpResponse(null, { status: 503 });
  }
  return HttpResponse.json({ data: 'success' });
});
```

## 环境配置

### 环境变量

```env
# API 配置
PUBLIC_API_BASE_URL=https://api.example.com
PUBLIC_WS_URL=wss://ws.example.com

# 功能开关
PUBLIC_ENABLE_MOCK_DATA=true
PUBLIC_ENABLE_REAL_TIME=false

# 请求配置
PUBLIC_API_TIMEOUT=10000
PUBLIC_MAX_RETRIES=3
```

### 配置文件

```typescript
// src/lib/config/api.ts
export const apiConfig = {
  baseURL: import.meta.env.PUBLIC_API_BASE_URL || '/api',
  timeout: parseInt(import.meta.env.PUBLIC_API_TIMEOUT || '10000'),
  maxRetries: parseInt(import.meta.env.PUBLIC_MAX_RETRIES || '3'),
  enableMockData: import.meta.env.PUBLIC_ENABLE_MOCK_DATA === 'true',
  enableRealTime: import.meta.env.PUBLIC_ENABLE_REAL_TIME === 'true'
};
```

## 性能优化策略

### 1. 请求优化

#### 请求合并

```typescript
class BatchRequestService {
  private pendingRequests = new Map<string, Promise<any>>();

  async batchGet<T>(endpoints: string[]): Promise<Record<string, T>> {
    const promises = endpoints.map(endpoint => 
      this.pendingRequests.get(endpoint) || this.createRequest<T>(endpoint)
    );
    
    const results = await Promise.allSettled(promises);
    return this.processResults(endpoints, results);
  }

  private createRequest<T>(endpoint: string): Promise<T> {
    const promise = this.apiService.get<T>(endpoint);
    this.pendingRequests.set(endpoint, promise);
    
    // 清理已完成的请求
    promise.finally(() => {
      this.pendingRequests.delete(endpoint);
    });
    
    return promise;
  }
}
```

#### 请求去重

```typescript
class DeduplicationService {
  private cache = new Map<string, Promise<any>>();

  async dedupedRequest<T>(key: string, requestFn: () => Promise<T>): Promise<T> {
    if (this.cache.has(key)) {
      return this.cache.get(key)!;
    }

    const promise = requestFn();
    this.cache.set(key, promise);

    promise.finally(() => {
      this.cache.delete(key);
    });

    return promise;
  }
}
```

### 2. 缓存策略

#### 分层缓存

```typescript
class LayeredCacheService {
  private memoryCache = new CacheService<any>(100, 5 * 60 * 1000);
  private localStorageCache = new LocalStorageCache();

  async get<T>(key: string, fetcher: () => Promise<T>): Promise<T> {
    // 1. 检查内存缓存
    let data = this.memoryCache.get(key);
    if (data) return data;

    // 2. 检查 localStorage 缓存
    data = this.localStorageCache.get(key);
    if (data) {
      this.memoryCache.set(key, data);
      return data;
    }

    // 3. 获取新数据
    data = await fetcher();
    this.memoryCache.set(key, data);
    this.localStorageCache.set(key, data);
    return data;
  }
}
```

#### 预取策略

```typescript
class PrefetchService {
  private prefetchQueue = new Set<string>();

  async prefetch(endpoints: string[]) {
    for (const endpoint of endpoints) {
      if (!this.prefetchQueue.has(endpoint)) {
        this.prefetchQueue.add(endpoint);
        
        // 后台预取数据
        this.apiService.get(endpoint).finally(() => {
          this.prefetchQueue.delete(endpoint);
        });
      }
    }
  }
}
```

## 错误处理最佳实践

### 1. 分层错误处理

```typescript
// 服务层错误处理
class ServiceErrorHandler {
  static handleApiError(error: ApiError, context: string) {
    // 记录错误日志
    console.error(`[${context}] API Error:`, error);

    // 根据错误类型进行处理
    if (error.isNetworkError()) {
      return this.handleNetworkError(error);
    }
    
    if (error.isServerError()) {
      return this.handleServerError(error);
    }
    
    if (error.statusCode === 401) {
      return this.handleAuthError(error);
    }

    return error.getUserFriendlyMessage();
  }

  private static handleNetworkError(error: ApiError) {
    // 可能的网络恢复策略
    return '网络连接失败，请检查网络后重试';
  }

  private static handleServerError(error: ApiError) {
    // 服务器错误的降级处理
    return '服务暂时不可用，正在尝试恢复';
  }

  private static handleAuthError(error: ApiError) {
    // 认证失败处理（如跳转登录）
    return '请重新登录';
  }
}
```

### 2. 用户友好的错误展示

```svelte
<script>
  import { ServiceErrorHandler } from '$lib/services/errorHandler';
  
  async function loadData() {
    try {
      const data = await dashboardApi.getStats();
      return data;
    } catch (error) {
      const message = ServiceErrorHandler.handleApiError(error, 'Dashboard');
      // 显示用户友好的错误消息
      showToast(message, 'error');
      throw error;
    }
  }
</script>
```

## 测试策略

### 1. 单元测试

```typescript
import { describe, it, expect, vi } from 'vitest';
import { DashboardApiService } from '$lib/services/api/dashboard';

describe('DashboardApiService', () => {
  let service: DashboardApiService;

  beforeEach(() => {
    service = new DashboardApiService('http://test-api.com');
    vi.clearAllMocks();
  });

  it('should fetch dashboard stats', async () => {
    const mockStats = { totalUsers: 100, monthlyRevenue: 5000 };
    
    // Mock fetch
    global.fetch = vi.fn().mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ data: mockStats, status: 'success' })
    });

    const response = await service.getStats();
    
    expect(response.data).toEqual(mockStats);
    expect(response.status).toBe('success');
  });

  it('should handle API errors', async () => {
    global.fetch = vi.fn().mockResolvedValue({
      ok: false,
      status: 404,
      statusText: 'Not Found'
    });

    await expect(service.getStats()).rejects.toThrow('HTTP 404: Not Found');
  });
});
```

### 2. 集成测试

```typescript
import { setupServer } from 'msw/node';
import { http, HttpResponse } from 'msw';
import { dashboardApi } from '$lib/services/api';

const server = setupServer(
  http.get('*/dashboard/stats', () => {
    return HttpResponse.json({
      data: { totalUsers: 100, monthlyRevenue: 5000 },
      status: 'success'
    });
  })
);

describe('Dashboard API Integration', () => {
  beforeAll(() => server.listen());
  afterEach(() => server.resetHandlers());
  afterAll(() => server.close());

  it('should fetch real dashboard data', async () => {
    const response = await dashboardApi.getStats();
    
    expect(response.data.totalUsers).toBe(100);
    expect(response.data.monthlyRevenue).toBe(5000);
  });
});
```

## 监控和日志

### 1. API 性能监控

```typescript
class ApiPerformanceMonitor {
  private static metrics = new Map<string, number[]>();

  static recordRequest(endpoint: string, duration: number) {
    if (!this.metrics.has(endpoint)) {
      this.metrics.set(endpoint, []);
    }
    
    const durations = this.metrics.get(endpoint)!;
    durations.push(duration);
    
    // 保持最近 100 次请求的记录
    if (durations.length > 100) {
      durations.shift();
    }
  }

  static getAverageResponseTime(endpoint: string): number {
    const durations = this.metrics.get(endpoint);
    if (!durations || durations.length === 0) return 0;
    
    const sum = durations.reduce((a, b) => a + b, 0);
    return sum / durations.length;
  }

  static getSlowRequests(threshold: number = 2000): Array<{ endpoint: string; avgTime: number }> {
    const slowRequests: Array<{ endpoint: string; avgTime: number }> = [];
    
    for (const [endpoint] of this.metrics) {
      const avgTime = this.getAverageResponseTime(endpoint);
      if (avgTime > threshold) {
        slowRequests.push({ endpoint, avgTime });
      }
    }
    
    return slowRequests;
  }
}
```

### 2. 错误统计

```typescript
class ApiErrorTracker {
  private static errors = new Map<string, number>();

  static recordError(endpoint: string, statusCode?: number) {
    const key = `${endpoint}:${statusCode || 'network'}`;
    this.errors.set(key, (this.errors.get(key) || 0) + 1);
  }

  static getErrorRate(endpoint: string): number {
    let totalErrors = 0;
    let totalRequests = 0;

    for (const [key, count] of this.errors) {
      if (key.startsWith(endpoint)) {
        totalErrors += count;
      }
    }

    // 这里需要结合请求总数来计算错误率
    // 简化实现，实际项目中需要更完善的统计
    return totalErrors;
  }
}
```

## 总结

Svelte Demo 的 API 服务层通过以下设计实现了高质量的数据访问：

### 核心优势

1. **统一接口**: BaseApiService 提供统一的请求处理
2. **类型安全**: 完整的 TypeScript 类型定义和检查
3. **错误处理**: 多层次的错误处理和用户友好的错误消息
4. **性能优化**: 缓存、重试、批处理等性能优化策略
5. **开发友好**: Mock 服务支持和完善的测试覆盖

### 技术特点

- **可扩展性**: 易于添加新的 API 服务
- **可配置性**: 支持不同环境的灵活配置
- **可监控性**: 内置性能监控和错误统计
- **可测试性**: 面向接口设计，便于单元测试和集成测试

### 最佳实践

1. **分层设计**: 基础服务 + 具体业务服务
2. **错误优先**: 完善的错误处理和恢复机制
3. **性能为重**: 缓存、去重、预取等优化策略
4. **监控导向**: 性能监控和错误统计
5. **测试驱动**: 完整的测试覆盖和 Mock 支持

这套 API 服务层架构为项目提供了稳定可靠的数据访问能力，确保了良好的用户体验和开发体验。