<!-- src/lib/components/liquidation/charts/KpiCard.svelte -->
<script lang="ts">
  // shadcn-svelte 组件导入
  import { Card, CardContent } from '$lib/components/ui/card';
  import { formatNumber } from '$lib/utils/formatters';

  // Props - 接收外部传入的数据而不是直接使用 store
  export let data: {
    totalAmount: number;
    totalCount: number;
    longAmount: number;
    shortAmount: number;
    longShortRatio: number;
  } | null = null;

  // 事件回调
  export let onKpiClick: (params: { type: string; value: number }) => void = () => {};

  // 处理卡片点击
  function handleCardClick(type: string, value: number) {
    onKpiClick({ type, value });
  }
</script>

{#if data}
  <div class="flex h-full w-full items-center justify-center">
    <div class="grid w-full grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
      <!-- 总清算金额 -->
      <Card
        class="cursor-pointer transition-all hover:shadow-md"
        role="button"
        tabindex="0"
        on:click={() => handleCardClick('totalAmount', data.totalAmount)}
        on:keydown={(e) => e.key === 'Enter' && handleCardClick('totalAmount', data.totalAmount)}
      >
        <CardContent class="p-4">
          <div class="text-muted-foreground text-xs font-medium">总清算金额</div>
          <div class="text-foreground mt-1 text-2xl font-bold">
            ${formatNumber(data.totalAmount, 2)}
          </div>
          <div class="text-muted-foreground text-xs">USD</div>
        </CardContent>
      </Card>

      <!-- 总清算笔数 -->
      <Card
        class="cursor-pointer transition-all hover:shadow-md"
        role="button"
        tabindex="0"
        on:click={() => handleCardClick('totalCount', data.totalCount)}
        on:keydown={(e) => e.key === 'Enter' && handleCardClick('totalCount', data.totalCount)}
      >
        <CardContent class="p-4">
          <div class="text-muted-foreground text-xs font-medium">总清算笔数</div>
          <div class="text-foreground mt-1 text-2xl font-bold">
            {formatNumber(data.totalCount, 0)}
          </div>
          <div class="text-muted-foreground text-xs">笔交易</div>
        </CardContent>
      </Card>

      <!-- 多头清算金额 -->
      <Card
        class="cursor-pointer transition-all hover:shadow-md"
        role="button"
        tabindex="0"
        on:click={() => handleCardClick('longAmount', data.longAmount)}
        on:keydown={(e) => e.key === 'Enter' && handleCardClick('longAmount', data.longAmount)}
      >
        <CardContent class="p-4">
          <div class="text-muted-foreground text-xs font-medium">多头清算金额</div>
          <div class="mt-1 text-2xl font-bold text-green-600">
            ${formatNumber(data.longAmount, 2)}
          </div>
          <div class="text-muted-foreground text-xs">USD</div>
        </CardContent>
      </Card>

      <!-- 空头清算金额 -->
      <Card
        class="cursor-pointer transition-all hover:shadow-md"
        role="button"
        tabindex="0"
        on:click={() => handleCardClick('shortAmount', data.shortAmount)}
        on:keydown={(e) => e.key === 'Enter' && handleCardClick('shortAmount', data.shortAmount)}
      >
        <CardContent class="p-4">
          <div class="text-muted-foreground text-xs font-medium">空头清算金额</div>
          <div class="mt-1 text-2xl font-bold text-red-600">
            ${formatNumber(data.shortAmount, 2)}
          </div>
          <div class="text-muted-foreground text-xs">USD</div>
        </CardContent>
      </Card>

      <!-- 多空金额比 -->
      <Card
        class="cursor-pointer transition-all hover:shadow-md"
        role="button"
        tabindex="0"
        on:click={() => handleCardClick('longShortRatio', data.longShortRatio)}
        on:keydown={(e) => e.key === 'Enter' && handleCardClick('longShortRatio', data.longShortRatio)}
      >
        <CardContent class="p-4">
          <div class="text-muted-foreground text-xs font-medium">多空金额比</div>
          <div class="mt-1 text-2xl font-bold text-blue-600">
            {formatNumber(data.longShortRatio, 2)}
          </div>
          <div class="text-muted-foreground text-xs">多/空</div>
        </CardContent>
      </Card>
    </div>
  </div>
{:else}
  <div class="flex h-full w-full items-center justify-center">
    <div class="text-muted-foreground text-sm">暂无数据</div>
  </div>
{/if}
