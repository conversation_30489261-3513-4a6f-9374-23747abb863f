
# 事件查询 API

## GET /event/query

### 描述

查询指定日期的市场事件数据。

### 参数

| 参数名            | 类型     | 描述                                                                 | 是否必需 |
| ----------------- | -------- | -------------------------------------------------------------------- | -------- |
| `base`            | `String` | 币种名称列表，例如 `BTC,ETH`                                         | 是       |
| `date`            | `String` | 查询日期 (UTC)，格式：`yyyy-MM-dd`                                   | 是       |
| `marketEventType` | `String` | 市场事件类型列表，逗号分割，例如 `LIQUIDATION_ORDER,VOLUME_INCREASE` | 是       |
| `threshold`       | `Number` | 阈值，枚举值：`0`, `1`, `2`, `4`                                     | 否       |

### 支持的市场事件类型

- `FUNDING_RATE_SWITCH` - 资金费率切换
- `EXTREME_FUNDING_RATE` - 极端资金费率
- `LIQUIDATION_ORDER` - 清算订单
- `VOLUME_INCREASE` - 交易量增长
- `EXCHANGE_TRANSFER` - 交易所转账
- `ORDERBOOK_IMBALANCE` - 订单簿失衡
- `OPEN_INTEREST_VARIATION` - 持仓量变化
- `TWAP` - 时间加权平均价格订单

### 示例请求

```http
GET /event/query?base=FOXY,ENJ,BTC&date=2025-04-24&marketEventType=LIQUIDATION_ORDER,FUNDING_RATE_SWITCH,TWAP&threshold=0
```

### 示例响应

```json
{
  "code": 1000,
  "message": "success",
  "data": {
    "date": "2025-04-24",
    "events": [
      {
        "id": "01JSKY3X725QG8AN81YGTJV7PD",
        "marketEventType": "LIQUIDATION_ORDER",
        "currency": "01HWKTT6BZ7YKZZ545HTPD22N7",
        "date": 1745499583,
        "parameters": [
          {
            "parameterId": "side",
            "value": 1
          },
          {
            "parameterId": "exchange",
            "value": 4096
          },
          {
            "parameterId": "exchangeLabel",
            "value": "OKX"
          },
          {
            "parameterId": "threshold",
            "value": 2
          },
          {
            "parameterId": "base",
            "value": "FOXY"
          },
          {
            "parameterId": "pair",
            "value": "FOXY-USDT-SWAP"
          },
          {
            "parameterId": "priceUsd",
            "value": 0.001887
          },
          {
            "parameterId": "datetime",
            "value": "2025-04-24T12:59:42.539000"
          },
          {
            "parameterId": "amount",
            "value": 672000
          },
          {
            "parameterId": "dailyVolumeRatio",
            "value": 0.00041611941630712
          },
          {
            "parameterId": "amountUsd",
            "value": 1268.064
          }
        ]
      },
      {
        "id": "01JSKSN7BKE30QERCZ0FFNR97H",
        "marketEventType": "FUNDING_RATE_SWITCH",
        "currency": "01HAF64ZT94SKECAMZ9C8TD0J1",
        "date": 1745494908,
        "parameters": [
          {
            "parameterId": "threshold",
            "value": 0
          },
          {
            "parameterId": "exchangeLabel",
            "value": "OKX FUTURES"
          },
          {
            "parameterId": "side",
            "value": 2
          },
          {
            "parameterId": "exchange",
            "value": 4096
          },
          {
            "parameterId": "wasSince",
            "value": "2025-04-20 17:41"
          },
          {
            "parameterId": "daysSince",
            "value": 3
          },
          {
            "parameterId": "datetime",
            "value": "2025-04-24T11:41:48.275561+00:00"
          },
          {
            "parameterId": "pair",
            "value": "ENJ/USDT:USDT"
          },
          {
            "parameterId": "base",
            "value": "ENJ"
          },
          {
            "parameterId": "previousDailyFundingRate",
            "value": -0.05223
          },
          {
            "parameterId": "dailyFundingRate",
            "value": 0.02847
          },
          {
            "parameterId": "aggregated",
            "value": false
          },
          {
            "parameterId": "fundingRate",
            "value": 0.0011863
          },
          {
            "parameterId": "previousFundingRate",
            "value": -0.0021762
          },
          {
            "parameterId": "fundingRateDiff",
            "value": 0.0033625
          }
        ]
      },
      {
          "id": "01K2DHYWX48QXRDBY42AFJYJK9",
          "marketEventType": "TWAP",
          "currency": "01HAF64YM82JQN8XSGDZ0ZJP7A",
          "date": 1754949156,
          "parameters": [
              {
                  "parameterId": "lastUpdated",
                  "value": "1754949196000"
              },
              {
                  "parameterId": "expired",
                  "value": "1754949196000"
              },
              {
                  "parameterId": "amountUsd",
                  "value": "1801097.344"
              },
              {
                  "parameterId": "created",
                  "value": "1754949156000"
              },
              {
                  "parameterId": "delay",
                  "value": 5019
              },
              {
                  "parameterId": "exchange",
                  "value": "1"
              },
              {
                  "parameterId": "exchangeLabel",
                  "value": "Binance"
              },
              {
                  "parameterId": "pair",
                  "value": "BTCUSDT"
              },
              {
                  "parameterId": "base",
                  "value": "BTC"
              },
              {
                  "parameterId": "side",
                  "value": 2
              },
              {
                  "parameterId": "size",
                  "value": "1.6744478728333319"
              },
              {
                  "parameterId": "market",
                  "value": 2
              },
              {
                  "parameterId": "threshold",
                  "value": 4
              }
          ]
      },
    ]
  }
}
```

### 错误响应

```json
{
  "code": 1001,
  "message": "fail"
}
```

## GET /event/query/advance

### 描述

根据更复杂的过滤条件查询指定日期的市场事件数据。

### 参数

| 参数名      | 类型     | 描述                 | 是否必需 |
| ----------- | -------- | -------------------- | -------- |
| `filterObj` | `String` | URL 编码的 JSON 对象 | 是       |

`filterObj` 的 JSON 结构:

```json5
{
  // 查询日期 (UTC)，格式：yyyy-MM-dd
  "date": "2025-04-24",
  // 币种名称列表，例如 BTC,ETH
  "base": "BTC,ETH",
  "filters": [
    {
      // 市场事件类型，例如 TWAP
      "marketEventType": "TWAP",
      // 阈值，枚举值：0, 1, 2, 4
      "threshold": 0
    }
  ]
}
```

### 示例请求

```http
GET /event/query/advance?filterObj=%7B%22date%22%3A%222025-04-24%22%2C%22base%22%3A%22BTC%22%2C%22filters%22%3A%5B%7B%22marketEventType%22%3A%22TWAP%22%2C%22threshold%22%3A4%7D%5D%7D
```

解码后的 `filterObj` 示例:

```json
{
  "date": "2025-04-24",
  "base": "BTC",
  "filters": [
    {
      "marketEventType": "TWAP",
      "threshold": 4
    }
  ]
}
```

### 示例响应

```json
{
  "code": 1000,
  "message": "success",
  "data": {
    "date": "2025-04-24",
    "events": [
      {
          "id": "01K2DHYWX48QXRDBY42AFJYJK9",
          "marketEventType": "TWAP",
          "currency": "01HAF64YM82JQN8XSGDZ0ZJP7A",
          "date": 1754949156,
          "parameters": [
              {
                  "parameterId": "lastUpdated",
                  "value": "1754949196000"
              },
              {
                  "parameterId": "expired",
                  "value": "1754949196000"
              },
              {
                  "parameterId": "amountUsd",
                  "value": "1801097.344"
              },
              {
                  "parameterId": "created",
                  "value": "1754949156000"
              },
              {
                  "parameterId": "delay",
                  "value": 5019
              },
              {
                  "parameterId": "exchange",
                  "value": "1"
              },
              {
                  "parameterId": "exchangeLabel",
                  "value": "Binance"
              },
              {
                  "parameterId": "pair",
                  "value": "BTCUSDT"
              },
              {
                  "parameterId": "base",
                  "value": "BTC"
              },
              {
                  "parameterId": "side",
                  "value": 2
              },
              {
                  "parameterId": "size",
                  "value": "1.6744478728333319"
              },
              {
                  "parameterId": "market",
                  "value": 2
              },
              {
                  "parameterId": "threshold",
                  "value": 4
              }
          ]
      }
    ]
  }
}
```

### 错误响应

```json
{
  "code": 1001,
  "message": "fail"
}
```
