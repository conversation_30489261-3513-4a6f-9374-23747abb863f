// 仪表板数据服务 - 整合原有的 API 和服务功能
import type {
  ApiResponse,
  BarChartItem,
  DashboardStats,
  LineChartItem,
  PieChartItem,
  ScatterChartItem,
} from '$lib/types';

interface AllChartData {
  barChartData: BarChartItem[];
  lineChartData: LineChartItem[];
  pieChartData: PieChartItem[];
  scatterChartData: ScatterChartItem[];
}

/**
 * 仪表板数据服务类
 *
 * 提供仪表板相关的数据获取和处理功能，包括图表数据和统计信息。
 * 使用 MSW (Mock Service Worker) 进行开发环境的数据模拟。
 *
 * @example
 * ```typescript
 * import { dashboardDataService } from '$lib/services/data/dashboard';
 *
 * // 获取所有图表数据
 * const response = await dashboardDataService.fetchAllChartData(
 *   '2024-01-01',
 *   '2024-01-31',
 *   'UTC'
 * );
 *
 * if (response.status === 'success') {
 *   apiLogger.info('仪表板图表数据获取成功', { dataKeys: Object.keys(response.data) });
 * }
 * ```
 *
 * @category Services
 */
export class DashboardDataService {
  private baseURL: string;

  constructor() {
    this.baseURL = import.meta.env.VITE_API_BASE_URL || '/api';
  }

  /**
   * 获取所有图表数据
   *
   * 获取仪表板中所有图表的数据，包括柱状图、折线图、饼图和散点图。
   * 在开发环境中，MSW 会自动拦截请求并返回模拟数据。
   *
   * @param startTime - 开始时间，格式为 ISO 字符串
   * @param endTime - 结束时间，格式为 ISO 字符串
   * @param selectedTimeZone - 时区，如 'UTC', 'Asia/Shanghai'
   * @returns Promise 包含所有图表数据的响应
   *
   * @example
   * ```typescript
   * const response = await dashboardDataService.fetchAllChartData(
   *   '2024-01-01T00:00:00Z',
   *   '2024-01-31T23:59:59Z',
   *   'UTC'
   * );
   *
   * if (response.status === 'success') {
   *   const { barChartData, lineChartData, pieChartData, scatterChartData } = response.data;
   *   // 处理图表数据
   * }
   * ```
   */
  async fetchAllChartData(
    startTime: string,
    endTime: string,
    selectedTimeZone: string
  ): Promise<ApiResponse<AllChartData>> {
    const response = await fetch(`${this.baseURL}/charts/all`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ startTime, endTime, selectedTimeZone }),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    return {
      status: 'success',
      data,
    };
  }

  /**
   * 获取仪表板统计数据
   *
   * 获取仪表板的关键统计指标，如总用户数、月收入、转化率等。
   * 在开发环境中，MSW 会自动拦截请求并返回模拟数据。
   *
   * @param startTime - 统计开始时间
   * @param endTime - 统计结束时间
   * @param selectedTimeZone - 时区设置
   * @returns Promise 包含统计数据的响应
   *
   * @example
   * ```typescript
   * const statsResponse = await dashboardDataService.fetchDashboardStats(
   *   '2024-01-01T00:00:00Z',
   *   '2024-01-31T23:59:59Z',
   *   'UTC'
   * );
   *
   * if (statsResponse.status === 'success') {
   *   const { totalUsers, monthlyRevenue, conversionRate } = statsResponse.data;
   *   console.log(`总用户数: ${totalUsers}`);
   * }
   * ```
   */
  async fetchDashboardStats(
    startTime: string,
    endTime: string,
    selectedTimeZone: string
  ): Promise<ApiResponse<DashboardStats>> {
    const response = await fetch(`${this.baseURL}/dashboard/stats`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ startTime, endTime, selectedTimeZone }),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    return {
      status: 'success',
      data,
    };
  }
}

// 创建默认实例
export const dashboardDataService = new DashboardDataService();
