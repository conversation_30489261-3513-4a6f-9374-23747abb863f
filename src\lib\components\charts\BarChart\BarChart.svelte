<script lang="ts">
  import type { BarSeriesOption, EChartsOption } from 'echarts';
  import * as echarts from 'echarts';
  import { createEventDispatcher, onDestroy, onMount } from 'svelte';

  import type { BarChartItem, LiquidationData } from '$lib/types';
  import { createThemeObserver, getChartColorMap } from '$lib/utils/chartColors';
  import { formatDate, formatNumber } from '$lib/utils/formatters';

  interface BarSeriesOptionWithRawValues extends BarSeriesOption {
    rawValues?: { [key: string]: number[] };
  }

  const dispatch = createEventDispatcher();

  export let options: EChartsOption = {};
  export let data: LiquidationData[] | BarChartItem[] = [];
  export let title: string = 'Monthly Sales Data';
  export let subtitle: string = '';
  export let groupBy: 'side' | 'coinType' | null = null;
  export let stackPercent: boolean = false;
  export let timeFrame: string = '1d'; // 时间粒度参数

  let chartDom: HTMLDivElement;
  let myChart: echarts.ECharts;
  let themeUnsubscribe: (() => void) | null = null;

  // 根据时间粒度生成时间键
  function getTimeKey(datetime: string, timeFrame: string): string {
    const date = new Date(datetime);

    switch (timeFrame) {
      case '1h': {
        // 按小时聚合：YYYY-MM-DD HH:00
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hour = String(date.getHours()).padStart(2, '0');
        return `${year}-${month}-${day} ${hour}:00`;
      }
      case '4h': {
        // 按4小时聚合：YYYY-MM-DD HH:00 (HH为0,4,8,12,16,20)
        const year4 = date.getFullYear();
        const month4 = String(date.getMonth() + 1).padStart(2, '0');
        const day4 = String(date.getDate()).padStart(2, '0');
        const hour4 = String(Math.floor(date.getHours() / 4) * 4).padStart(2, '0');
        return `${year4}-${month4}-${day4} ${hour4}:00`;
      }
      case '1d': {
        // 按天聚合：YYYY-MM-DD
        const yearD = date.getFullYear();
        const monthD = String(date.getMonth() + 1).padStart(2, '0');
        const dayD = String(date.getDate()).padStart(2, '0');
        return `${yearD}-${monthD}-${dayD}`;
      }
      case '1w': {
        // 按周聚合：使用周一作为周的开始
        const yearW = date.getFullYear();
        const startOfYear = new Date(yearW, 0, 1);
        const dayOfYear = Math.floor((date.getTime() - startOfYear.getTime()) / 86400000) + 1;
        const weekNumber = Math.ceil((dayOfYear + startOfYear.getDay()) / 7);
        return `${yearW}-W${String(weekNumber).padStart(2, '0')}`;
      }
      default:
        return formatDate(datetime, 'date');
    }
  }

  function processMultiSeriesData(
    data: LiquidationData[],
    groupBy: 'side' | 'coinType'
  ): {
    times: string[];
    series: { name: string; data: number[] }[];
    rawValues: { [key: string]: number[] };
  } {
    if (!data || data.length === 0) {
      return { times: [], series: [], rawValues: {} };
    }

    const timeMap = new Map<string, Map<string, number>>();
    const groupValues = new Set<string>();

    data.forEach((item) => {
      const timeKey = getTimeKey(item.datetime, timeFrame);
      let groupKey: string;

      if (groupBy === 'side') {
        groupKey = item.side === 1 ? '多头' : '空头';
      } else if (groupBy === 'coinType') {
        // 对于 coinType 分组，我们需要同时考虑币种类型和多空方向
        const coinTypeLabel = item.coinType === 'Major' ? '主流币' : '山寨币';
        const sideLabel = item.side === 1 ? '多头' : '空头';
        groupKey = `${coinTypeLabel}-${sideLabel}`;
      } else {
        groupKey = item.coinType;
      }

      groupValues.add(groupKey);

      if (!timeMap.has(timeKey)) {
        timeMap.set(timeKey, new Map<string, number>());
      }

      const groupMap = timeMap.get(timeKey)!;
      groupMap.set(groupKey, (groupMap.get(groupKey) || 0) + item.amountUsd);
    });

    const times = Array.from(timeMap.keys()).sort();
    const rawValues: { [key: string]: number[] } = {};
    const allGroupValues = Array.from(groupValues);

    const totalsByTime = times.map((time) => {
      const groupMap = timeMap.get(time)!;
      let total = 0;
      allGroupValues.forEach((groupValue) => {
        total += groupMap.get(groupValue) || 0;
      });
      return total;
    });

    const series = allGroupValues.map((groupValue) => {
      const rawData: number[] = [];
      const percentData = times.map((time, index) => {
        const groupMap = timeMap.get(time)!;
        const value = groupMap.get(groupValue) || 0;
        rawData.push(value);
        return totalsByTime[index] === 0 ? 0 : (value / totalsByTime[index]) * 100;
      });

      rawValues[groupValue] = rawData;

      return {
        name: groupValue,
        data: percentData,
      };
    });

    return { times, series, rawValues };
  }

  const baseChartOptions: EChartsOption = {
    title: {
      text: title,
      subtext: subtitle,
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: function (params: any) {
        let result = params[0].name + '<br/>';
        params.forEach((param: any) => {
          let valueDisplay = formatNumber(param.value, 2);
          if (stackPercent) {
            const series = myChart.getOption().series as BarSeriesOptionWithRawValues[];
            if (series && series[param.seriesIndex] && series[param.seriesIndex].rawValues) {
              const typedSeries = series[param.seriesIndex];
              const rawValues = typedSeries.rawValues;
              if (rawValues && rawValues[param.seriesName]) {
                const rawValue = rawValues[param.seriesName][param.dataIndex];
                valueDisplay = `${param.value.toFixed(1)}% ($${formatNumber(rawValue, 2)})`;
              }
            }
          }
          result += `${param.marker} ${param.seriesName}: ${valueDisplay}<br/>`;
        });
        return result;
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '8%',
      top: '15%',
      containLabel: true,
    },
    dataZoom: [
      {
        show: false,
        type: 'inside',
        start: 0,
        end: 100,
      },
    ],
    xAxis: {
      type: 'category',
      data: [],
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: function (value: number) {
          if (stackPercent) {
            return `${value}%`;
          } else if (value >= 1000000) {
            return (value / 1000000).toFixed(1) + 'M';
          } else if (value >= 1000) {
            return (value / 1000).toFixed(0) + 'K';
          }
          return value.toString();
        },
      },
    },
    series: [],
  };

  let chartInitialized = false;

  function initializeChart() {
    if (chartInitialized) return;
    myChart = echarts.init(chartDom);
    updateChart();
    chartInitialized = true;

    myChart.on('click', (params) => {
      dispatch('chartClick', { chartType: 'bar', name: params.seriesName, value: params.value });
    });
  }

  function updateChart() {
    if (!myChart) return;

    let finalSeries: BarSeriesOption[] = [];
    let xAxisData: string[] = [];
    let legendData: string[] = [];
    let processedDataWithRawValues: ReturnType<typeof processMultiSeriesData> | null = null;

    // 使用主题感知的颜色映射
    const colorMap = getChartColorMap();

    if (groupBy && Array.isArray(data) && data.length > 0 && 'datetime' in data[0]) {
      processedDataWithRawValues = processMultiSeriesData(data as LiquidationData[], groupBy);
      xAxisData = processedDataWithRawValues.times;
      legendData = processedDataWithRawValues.series.map((s) => s.name);
      finalSeries = processedDataWithRawValues.series.map((s) => ({
        name: s.name,
        type: 'bar',
        stack: stackPercent ? 'total' : undefined,
        data: s.data,
        color: colorMap[s.name] || undefined,
      }));
    } else if (Array.isArray(data) && data.length > 0 && 'name' in data[0] && 'value' in data[0]) {
      xAxisData = data.map((item) => (item as BarChartItem).name);
      finalSeries = [
        {
          name: title,
          type: 'bar',
          data: data.map((item) => (item as BarChartItem).value),
        },
      ];
    }

    myChart.setOption(
      {
        ...baseChartOptions,
        ...options,
        title: {
          ...baseChartOptions.title,
          text: title,
          subtext: subtitle,
        },
        xAxis: {
          ...baseChartOptions.xAxis,
          data: xAxisData,
        },
        yAxis: {
          ...baseChartOptions.yAxis,
          max: stackPercent ? 100 : undefined,
        },
        legend:
          legendData.length > 0
            ? {
                show: true,
                bottom: 0,
                left: 'center',
                data: legendData,
              }
            : undefined,
        series: finalSeries,
      },
      { notMerge: false }
    );

    if (stackPercent && processedDataWithRawValues) {
      myChart.setOption({
        series: finalSeries.map((s, _i) => ({
          ...s,
          rawValues:
            processedDataWithRawValues!.rawValues[
              s.name as keyof typeof processedDataWithRawValues.rawValues
            ],
        })),
      });
    }
  }

  onMount(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            initializeChart();
            observer.unobserve(chartDom);
          }
        });
      },
      { threshold: 0.1 }
    );

    observer.observe(chartDom);

    const resizeChart = () => {
      if (myChart) {
        myChart.resize();
      }
    };

    // 监听主题变化
    themeUnsubscribe = createThemeObserver(() => {
      if (myChart && chartInitialized) {
        updateChart();
      }
    });

    window.addEventListener('resize', resizeChart);

    onDestroy(() => {
      window.removeEventListener('resize', resizeChart);
      if (themeUnsubscribe) {
        themeUnsubscribe();
      }
      if (updateTimer) {
        clearTimeout(updateTimer);
      }
      if (myChart) {
        myChart.dispose();
      }
    });
  });

  // 优化响应式更新，添加防抖和条件检查
  let updateTimer: number | null = null;
  let lastDataHash = '';

  // 计算数据哈希值，避免不必要的更新
  function getDataHash(
    data: any,
    options: any,
    title: string,
    subtitle: string,
    groupBy: any,
    stackPercent: boolean,
    timeFrame: string
  ): string {
    return JSON.stringify({
      dataLength: Array.isArray(data) ? data.length : 0,
      dataFirst: Array.isArray(data) && data.length > 0 ? data[0] : null,
      options,
      title,
      subtitle,
      groupBy,
      stackPercent,
      timeFrame,
    });
  }

  $: {
    if (myChart && chartInitialized) {
      const currentHash = getDataHash(
        data,
        options,
        title,
        subtitle,
        groupBy,
        stackPercent,
        timeFrame
      );

      // 只有当数据真正发生变化时才更新
      if (currentHash !== lastDataHash) {
        if (updateTimer) {
          clearTimeout(updateTimer);
        }

        updateTimer = window.setTimeout(() => {
          try {
            updateChart();
            lastDataHash = currentHash;
          } catch (error) {
            console.error('BarChart update error:', error);
          }
          updateTimer = null;
        }, 50); // 50ms 防抖
      }
    }
  }
</script>

<div bind:this={chartDom} class="h-full w-full" aria-label="{title} - {subtitle}"></div>
