<script lang="ts">
  import type { Snippet } from 'svelte';

  import { Card } from '$lib/components/ui/card';

  import type { BaseEventCardProps } from '../utils';
  import EventCardContent from './EventCardContent.svelte';
  import EventCardFooter from './EventCardFooter.svelte';
  import EventCardHeader from './EventCardHeader.svelte';

  interface Props extends BaseEventCardProps {
    customHeader?: boolean;
    customContent?: boolean;
    customFooter?: boolean;
    showHeader?: boolean;
    showFooter?: boolean;
    keyFields?: string[];
    header?: Snippet;
    content?: Snippet;
    footer?: Snippet;
    children?: Snippet;
  }

  const {
    event,
    isHighlighted = false,
    size = 'md',
    variant = 'default',
    showActions = false,
    onCardClick,
    customHeader = false,
    customContent = false,
    customFooter = false,
    showHeader = true,
    showFooter = true,
    keyFields = [],
    header,
    content,
    footer,
    children,
  }: Props = $props();

  // 卡片容器样式
  const cardClasses = `
    transition-all duration-200 w-full
    ${isHighlighted ? 'ring-primary border-primary/50 shadow-lg ring-2' : 'hover:shadow-md'}
    ${onCardClick ? 'cursor-pointer' : ''}
  `;

  // 处理卡片点击
  function handleCardClick() {
    onCardClick?.(event);
  }
</script>

<Card
  class={cardClasses}
  onclick={onCardClick ? handleCardClick : undefined}
  role={onCardClick ? 'button' : undefined}
  tabindex={onCardClick ? 0 : undefined}
>
  <!-- 卡片头部 -->
  {#if showHeader}
    {#if customHeader && header}
      {@render header()}
    {:else}
      <EventCardHeader {event} {size} />
    {/if}
  {/if}

  <!-- 卡片内容 -->
  {#if customContent && content}
    {@render content()}
  {:else}
    <EventCardContent {event} {size} {variant} {keyFields} {children} />
  {/if}

  <!-- 卡片底部 -->
  {#if showFooter}
    {#if customFooter && footer}
      {@render footer()}
    {:else}
      <EventCardFooter {event} {size} {isHighlighted} {showActions} {onCardClick} />
    {/if}
  {/if}
</Card>
