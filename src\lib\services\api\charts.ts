// src/lib/services/api/charts.ts
import type {
  ApiResponse,
  BarChartItem,
  ChartData,
  LineChartItem,
  PieChartItem,
  ScatterChartItem,
} from '$lib/types';

import { BaseApiService } from './base';
/**
 * 图表数据 API 服务
 */
export class ChartsApiService extends BaseApiService {
  /**
   * 获取柱状图数据
   */
  async getBarChartData(): Promise<ApiResponse<BarChartItem[]>> {
    return this.get<BarChartItem[]>('/charts/bar');
  }

  /**
   * 获取折线图数据
   */
  async getLineChartData(): Promise<ApiResponse<LineChartItem[]>> {
    return this.get<LineChartItem[]>('/charts/line');
  }

  /**
   * 获取饼图数据
   */
  async getPieChartData(): Promise<ApiResponse<PieChartItem[]>> {
    return this.get<PieChartItem[]>('/charts/pie');
  }

  /**
   * 获取散点图数据
   */
  async getScatterChartData(): Promise<ApiResponse<ScatterChartItem[]>> {
    return this.get<ScatterChartItem[]>('/charts/scatter');
  }

  /**
   * 获取所有图表数据
   */
  async getAllChartData(): Promise<ApiResponse<ChartData>> {
    return this.get<ChartData>('/charts/all');
  }
}

// 创建默认实例
export const chartsApi = new ChartsApiService();
