# 常量管理文档

本项目采用集中化的常量管理方案，将所有配置参数、常量值和枚举类型统一组织在 `src/lib/constants/` 目录下。

## 🎯 设计目标

### 核心原则

1. **集中管理**：所有常量统一存放，避免散落在各个文件中
2. **类型安全**：使用 TypeScript 的 `as const` 确保类型安全
3. **分类组织**：按功能模块分类，便于维护和查找
4. **易于扩展**：支持新增常量类型和配置项

### 优势

- **维护性**：修改常量值只需在一个地方进行
- **一致性**：确保整个应用使用相同的常量值
- **类型安全**：编译时检查，避免运行时错误
- **智能提示**：IDE 提供完整的自动补全

## 📁 目录结构

```text
src/lib/constants/
├── api.ts            # API 相关常量
├── ui.ts             # UI 相关常量
├── charts.ts         # 图表相关常量
├── time.ts           # 时间相关常量
└── index.ts          # 统一导出
```

## 🔧 常量定义

### API 常量 (`api.ts`)

```typescript
// API 端点配置
export const API_ENDPOINTS = {
  CHARTS: {
    BAR: '/api/charts/bar',
    LINE: '/api/charts/line',
    PIE: '/api/charts/pie',
    SCATTER: '/api/charts/scatter',
    ALL: '/api/charts/all',
  },
  DASHBOARD: {
    STATS: '/api/dashboard/stats',
    OVERVIEW: '/api/dashboard/overview',
  },
  LIQUIDATION: {
    SNAPSHOT: '/api/liquidation/snapshot',
    TREND: '/api/liquidation/trend',
  },
} as const;

// HTTP 配置
export const HTTP_CONFIG = {
  TIMEOUT: 10000,           // 请求超时时间 (ms)
  RETRY_ATTEMPTS: 3,        // 重试次数
  RETRY_DELAY: 1000,        // 重试延迟 (ms)
} as const;

// 响应状态
export const API_STATUS = {
  SUCCESS: 'success',
  ERROR: 'error',
  LOADING: 'loading',
} as const;
```

### UI 常量 (`ui.ts`)

```typescript
// 断点配置
export const BREAKPOINTS = {
  SM: 640,
  MD: 768,
  LG: 1024,
  XL: 1280,
  '2XL': 1536,
} as const;

// Z-Index 层级
export const Z_INDEX = {
  DROPDOWN: 1000,
  STICKY: 1020,
  FIXED: 1030,
  MODAL_BACKDROP: 1040,
  MODAL: 1050,
  POPOVER: 1060,
  TOOLTIP: 1070,
  TOAST: 1080,
} as const;

// 动画时长
export const ANIMATION_DURATION = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
} as const;

// 颜色主题
export const COLORS = {
  PRIMARY: {
    50: '#eff6ff',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
  },
  SUCCESS: {
    50: '#f0fdf4',
    500: '#10b981',
    600: '#059669',
  },
  ERROR: {
    50: '#fef2f2',
    500: '#ef4444',
    600: '#dc2626',
  },
} as const;
```

### 图表常量 (`charts.ts`)

```typescript
// 图表默认配置
export const CHART_DEFAULTS = {
  HEIGHT: 400,
  ANIMATION_DURATION: 300,
  GRID: {
    LEFT: '3%',
    RIGHT: '4%',
    BOTTOM: '3%',
    CONTAIN_LABEL: true,
  },
} as const;

// 图表颜色配置
export const CHART_COLORS = {
  // 爆仓分析专用颜色
  LIQUIDATION: {
    LONG: '#10B981',      // 多头 - 绿色
    SHORT: '#EF4444',     // 空头 - 红色
    MAJOR: '#3B82F6',     // 主流币 - 蓝色
    ALTCOIN: '#8B5CF6',   // 山寨币 - 紫色
  },
  // 通用图表颜色
  PALETTE: [
    '#3B82F6', '#10B981', '#F59E0B', '#EF4444',
    '#8B5CF6', '#06B6D4', '#84CC16', '#F97316',
  ],
} as const;

// ECharts 主题配置
export const ECHARTS_THEME = {
  BACKGROUND_COLOR: 'transparent',
  TEXT_STYLE: {
    FONT_FAMILY: 'Inter, system-ui, sans-serif',
    FONT_SIZE: 12,
    COLOR: '#374151',
  },
  TOOLTIP: {
    BACKGROUND_COLOR: 'rgba(0, 0, 0, 0.8)',
    BORDER_WIDTH: 0,
    TEXT_STYLE: {
      COLOR: '#ffffff',
    },
  },
} as const;
```

### 时间常量 (`time.ts`)

```typescript
// 时间格式
export const TIME_FORMATS = {
  DATE: 'YYYY-MM-DD',
  TIME: 'HH:mm:ss',
  DATETIME: 'YYYY-MM-DD HH:mm:ss',
  ISO: 'YYYY-MM-DDTHH:mm:ss.SSSZ',
} as const;

// 时区配置
export const TIMEZONES = {
  UTC: 'UTC',
  BEIJING: 'Asia/Shanghai',
  NEW_YORK: 'America/New_York',
  LONDON: 'Europe/London',
  TOKYO: 'Asia/Tokyo',
} as const;

// 刷新间隔 (毫秒)
export const REFRESH_INTERVALS = {
  REAL_TIME: 5000,      // 5秒
  FREQUENT: 30000,      // 30秒
  NORMAL: 60000,        // 1分钟
  SLOW: 300000,         // 5分钟
} as const;

// 爆仓分析时间范围
export const LIQUIDATION_TIME_RANGES = {
  SNAPSHOT: {
    '1H': '1h',
    '4H': '4h',
    '12H': '12h',
    '24H': '24h',
  },
  TREND_FRAME: {
    '15M': '15m',
    '1H': '1h',
    '4H': '4h',
    '1D': '1d',
  },
  TREND_DURATION: {
    '1H': '1h',
    '4H': '4h',
    '12H': '12h',
    '1D': '1d',
    '3D': '3d',
    '7D': '7d',
    '30D': '30d',
  },
} as const;
```

## 📝 使用指南

### 导入常量

```typescript
// 导入特定常量
import { API_ENDPOINTS, HTTP_CONFIG } from '$lib/constants/api';
import { BREAKPOINTS, Z_INDEX } from '$lib/constants/ui';

// 导入所有常量
import * as Constants from '$lib/constants';
```

### 在组件中使用

```svelte
<script lang="ts">
  import { CHART_COLORS, CHART_DEFAULTS } from '$lib/constants/charts';
  import { ANIMATION_DURATION } from '$lib/constants/ui';

  // 使用图表颜色
  const chartOptions = {
    color: CHART_COLORS.PALETTE,
    animationDuration: CHART_DEFAULTS.ANIMATION_DURATION,
  };
</script>

<style>
  .modal {
    z-index: {Z_INDEX.MODAL};
    transition-duration: {ANIMATION_DURATION.NORMAL}ms;
  }
</style>
```

### 在服务中使用

```typescript
import { API_ENDPOINTS, HTTP_CONFIG } from '$lib/constants/api';

export class ApiService {
  async fetchData() {
    const response = await fetch(API_ENDPOINTS.CHARTS.ALL, {
      timeout: HTTP_CONFIG.TIMEOUT,
    });
    return response.json();
  }
}
```

## 🔧 类型安全

### 使用 `as const`

```typescript
// ✅ 正确：使用 as const 确保类型安全
export const STATUS = {
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error',
} as const;

// ❌ 错误：没有 as const，类型为 string
export const STATUS_BAD = {
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error',
};
```

### 类型提取

```typescript
// 从常量中提取类型
type ApiStatus = typeof API_STATUS[keyof typeof API_STATUS];
// 结果：'success' | 'error' | 'loading'

type ChartColor = typeof CHART_COLORS.LIQUIDATION[keyof typeof CHART_COLORS.LIQUIDATION];
// 结果：'#10B981' | '#EF4444' | '#3B82F6' | '#8B5CF6'
```

## 🎨 最佳实践

### 1. 命名规范

```typescript
// ✅ 使用 UPPER_SNAKE_CASE
export const API_ENDPOINTS = { ... };

// ✅ 嵌套对象使用 PascalCase
export const CHART_COLORS = {
  LIQUIDATION: { ... },
  PALETTE: [ ... ],
};

// ❌ 避免使用 camelCase
export const apiEndpoints = { ... };
```

### 2. 分组组织

```typescript
// ✅ 按功能分组
export const LIQUIDATION_CONFIG = {
  TIME_RANGES: { ... },
  COLORS: { ... },
  DEFAULTS: { ... },
};

// ❌ 避免扁平化结构
export const LIQUIDATION_TIME_RANGES = { ... };
export const LIQUIDATION_COLORS = { ... };
export const LIQUIDATION_DEFAULTS = { ... };
```

### 3. 文档注释

```typescript
/**
 * 爆仓分析图表颜色配置
 * 
 * @description 定义爆仓分析功能中使用的标准颜色
 * @example
 * ```typescript
 * const longColor = CHART_COLORS.LIQUIDATION.LONG; // '#10B981'
 * ```
 */
export const CHART_COLORS = {
  LIQUIDATION: {
    /** 多头持仓颜色 - 绿色 */
    LONG: '#10B981',
    /** 空头持仓颜色 - 红色 */
    SHORT: '#EF4444',
  },
} as const;
```

## 🔄 维护和更新

### 版本控制

```typescript
// 为重要配置添加版本信息
export const CONFIG_VERSION = '1.0.0' as const;

export const API_CONFIG = {
  VERSION: CONFIG_VERSION,
  ENDPOINTS: { ... },
} as const;
```

### 废弃处理

```typescript
/**
 * @deprecated 使用 NEW_ENDPOINT 替代
 * @see API_ENDPOINTS.NEW_ENDPOINT
 */
export const OLD_ENDPOINT = '/api/old' as const;

export const NEW_ENDPOINT = '/api/new' as const;
```

## 🧪 测试

### 常量测试

```typescript
import { API_ENDPOINTS, HTTP_CONFIG } from '$lib/constants/api';

describe('API Constants', () => {
  test('should have valid endpoints', () => {
    expect(API_ENDPOINTS.CHARTS.BAR).toBe('/api/charts/bar');
    expect(typeof HTTP_CONFIG.TIMEOUT).toBe('number');
  });

  test('should be immutable', () => {
    expect(() => {
      // @ts-expect-error - 应该是只读的
      API_ENDPOINTS.CHARTS.BAR = '/new/path';
    }).toThrow();
  });
});
```

## 🔗 相关文档

- [TypeScript 配置](./typescript.md)
- [代码规范](./coding-standards.md)
- [API 文档](../api/README.md)
- [组件开发指南](../components/README.md)
