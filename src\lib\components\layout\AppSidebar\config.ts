// src/lib/components/layout/AppSidebar/config.ts

import ActivityIcon from '@lucide/svelte/icons/activity';
import BarChart3Icon from '@lucide/svelte/icons/bar-chart-3';
import LayoutDashboardIcon from '@lucide/svelte/icons/layout-dashboard';
import ListOrderedIcon from '@lucide/svelte/icons/list-ordered'; // 导入 ListOrderedIcon
import MessageSquareIcon from '@lucide/svelte/icons/message-square';
import PieChartIcon from '@lucide/svelte/icons/pie-chart';
import SearchIcon from '@lucide/svelte/icons/search';
import SettingsIcon from '@lucide/svelte/icons/settings';
import TestTubeDiagonalIcon from '@lucide/svelte/icons/test-tube-diagonal'; // 更改导入路径
import TrendingUpIcon from '@lucide/svelte/icons/trending-up';

import type { SidebarConfig } from './types.js';

/**
 * 项目专用的侧边栏配置
 * 专注于加密货币清算数据可视化分析系统
 */
export const sidebarConfig: SidebarConfig = {
  // 用户信息配置
  user: {
    name: '数据分析师',
    email: '<EMAIL>',
    // avatar: '/avatars/analyst.jpg',
    role: '高级分析师',
    status: 'online',
  },

  // 团队信息配置（可选）
  teams: [
    {
      name: '加密货币监控中心',
      logo: TrendingUpIcon,
      plan: '专业版',
      description: '实时监控加密货币市场数据',
    },
    {
      name: '数据分析团队',
      logo: BarChart3Icon,
      plan: '企业版',
      description: '深度数据分析和可视化',
    },
  ],

  // 主导航配置
  navMain: [
    {
      title: '仪表板',
      icon: LayoutDashboardIcon, // 将 DashboardIcon 替换为 LayoutDashboardIcon
      isActive: true,
      defaultOpen: true,
      items: [
        {
          title: '总览',
          url: '/',
          description: '系统总体数据概览',
        },
        {
          title: '清算监控',
          url: '/liquidation',
          description: '加密货币清算数据实时监控',
        },
        {
          title: '市场分析',
          url: '/dashboard',
          description: '市场趋势和数据分析',
        },
      ],
    },
    {
      title: '排行',
      icon: ListOrderedIcon, // 将 LayoutDashboardIcon 替换为 ListOrderedIcon
      isActive: true,
      items: [
        {
          title: '总览',
          url: '/rank/overview',
          description: '排行总览',
        },
        {
          title: '清算排行',
          url: '/rank/liquidation',
          description: '清算排行',
        },
      ],
    },
    {
      title: '数据查询',
      icon: SearchIcon, // 将 DataQueryIcon 替换为 SearchIcon
      items: [
        {
          title: '历史数据统计',
          url: '/query/historical',
          description: '查询历史清算数据统计',
        },
        {
          title: '实时数据',
          url: '/query/realtime',
          description: '查询实时市场数据',
        },
        {
          title: '自定义查询',
          url: '/query/custom',
          description: '自定义数据查询条件',
        },
        {
          title: '实时消息',
          url: '/query/real-time-messages',
          description: '实时系统通知和市场消息',
        },
      ],
    },
    {
      title: '系统设置',
      url: '/settings',
      icon: SettingsIcon,
      items: [

      ],
    },
    {
      title: '测试页面',
      icon: TestTubeDiagonalIcon,
      items: [
        {
          title: '卡片样式',
          url: '/test/cards',
          description: '自定义查询卡片样式',
        },
        {
          title: '图标缓存',
          url: '/test/cache',
          description: '图标缓存测试页面',
        },
        {
          title: '时间线',
          url: '/test/timeline',
          description: '时间线测试页面',
        },
      ],
    },
  ],

  // 项目快捷访问配置
  projects: [
    {
      name: '清算数据分析',
      url: '/liquidation',
      icon: TrendingUpIcon,
      description: '加密货币清算数据深度分析',
      status: 'active',
    },
    {
      name: '市场趋势监控',
      url: '/dashboard',
      icon: BarChart3Icon,
      description: '实时市场趋势监控面板',
      status: 'active',
    },
    {
      name: '数据可视化',
      url: '/',
      icon: PieChartIcon,
      description: '多维度数据可视化展示',
      status: 'active',
    },
    {
      name: '系统监控',
      url: '/settings',
      icon: ActivityIcon,
      description: '系统性能和状态监控',
      status: 'development',
    },
  ],

  // 功能开关
  showSystemStatus: true,
  showThemeToggle: true,
};

/**
 * 根据当前路径获取对应的标签页信息
 * 用于与现有 TabBar 系统集成
 */
export function getTabInfoFromPath(pathname: string): {
  href: string;
  label: string;
  icon?: any;
} {
  // 移除查询参数，只保留路径
  const cleanPath = pathname.split('?')[0];

  switch (cleanPath) {
    case '/':
      return { href: '/', label: '总览', icon: LayoutDashboardIcon }; // 将 DashboardIcon 替换为 LayoutDashboardIcon
    case '/liquidation':
      return { href: '/liquidation', label: '清算监控', icon: TrendingUpIcon };
    case '/dashboard':
      return { href: '/dashboard', label: '市场分析', icon: BarChart3Icon };
    case '/query':
      return { href: '/query', label: '数据查询', icon: SearchIcon }; // 将 DataQueryIcon 替换为 SearchIcon
    case '/query/historical':
      return { href: '/query/historical', label: '历史数据', icon: SearchIcon };
    case '/query/realtime':
      return { href: '/query/realtime', label: '实时数据', icon: SearchIcon };
    case '/query/custom':
      return { href: '/query/custom', label: '自定义查询', icon: SearchIcon };
    case '/query/real-time-messages':
      return { href: '/query/real-time-messages', label: '实时消息', icon: MessageSquareIcon };
    case '/settings':
      return { href: '/settings', label: '系统设置', icon: SettingsIcon };
    default:
      return { href: cleanPath, label: cleanPath.replace('/', '') || '首页', icon: undefined };
  }
}
