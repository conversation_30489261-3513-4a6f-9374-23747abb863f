/**
 * 应用配置管理
 *
 * 统一管理应用的所有配置项，包括环境变量、常量、主题等
 *
 * @category Config
 */

import { browser } from '$app/environment';
import { createModuleLogger } from '$lib/utils/logger';

const configLogger = createModuleLogger('config');

/**
 * 环境类型
 */
export type Environment = 'development' | 'production' | 'test';

/**
 * 应用配置接口
 */
export interface AppConfig {
  /** 环境 */
  env: Environment;
  /** 是否为开发环境 */
  isDev: boolean;
  /** 是否为生产环境 */
  isProd: boolean;
  /** 是否为测试环境 */
  isTest: boolean;
  /** API 配置 */
  api: ApiConfig;
  /** 图表配置 */
  charts: ChartConfig;
  /** 性能配置 */
  performance: PerformanceConfig;
  /** 功能开关 */
  features: FeatureFlags;
  /** 主题配置 */
  theme: ThemeConfig;
  /** 日志配置 */
  logging: LoggingConfig;
  /** 错误处理配置 */
  errorHandling: ErrorHandlingConfig;
  /** 存储配置 */
  storage: StorageConfig;
}

/**
 * API 配置
 */
export interface ApiConfig {
  /** 基础 URL */
  baseUrl: string;
  /** 超时时间 */
  timeout: number;
  /** 重试次数 */
  retryCount: number;
  /** 重试延迟 */
  retryDelay: number;
  /** 是否启用缓存 */
  enableCache: boolean;
  /** 缓存时间 */
  cacheTime: number;
  /** 是否启用 MSW */
  enableMSW: boolean;
}

/**
 * 图表配置
 */
export interface ChartConfig {
  /** 默认主题 */
  defaultTheme: string;
  /** 动画持续时间 */
  animationDuration: number;
  /** 是否启用动画 */
  enableAnimation: boolean;
  /** 防抖延迟 */
  debounceDelay: number;
  /** 最大数据点数 */
  maxDataPoints: number;
  /** 是否启用虚拟化 */
  enableVirtualization: boolean;
  /** 虚拟化阈值 */
  virtualizationThreshold: number;
  /** 默认颜色 */
  defaultColors: string[];
}

/**
 * 性能配置
 */
export interface PerformanceConfig {
  /** 是否启用性能监控 */
  enableMonitoring: boolean;
  /** 性能预算 */
  performanceBudget: {
    /** 首屏加载时间 */
    firstContentfulPaint: number;
    /** 最大输入延迟 */
    firstInputDelay: number;
    /** 累积布局偏移 */
    cumulativeLayoutShift: number;
  };
  /** 是否启用懒加载 */
  enableLazyLoading: boolean;
  /** 是否启用代码分割 */
  enableCodeSplitting: boolean;
}

/**
 * 功能开关
 */
export interface FeatureFlags {
  /** 是否启用实时数据 */
  enableRealTimeData: boolean;
  /** 是否启用数据导出 */
  enableDataExport: boolean;
  /** 是否启用高级图表 */
  enableAdvancedCharts: boolean;
  /** 是否启用主题切换 */
  enableThemeToggle: boolean;
  /** 是否启用键盘快捷键 */
  enableKeyboardShortcuts: boolean;
  /** 是否启用通知 */
  enableNotifications: boolean;
  /** 是否启用错误报告 */
  enableErrorReporting: boolean;
}

/**
 * 主题配置
 */
export interface ThemeConfig {
  /** 默认主题 */
  defaultTheme: 'light' | 'dark' | 'system';
  /** 是否启用主题切换 */
  enableThemeToggle: boolean;
  /** 主题存储键 */
  storageKey: string;
  /** 主题变化动画时间 */
  transitionDuration: number;
}

/**
 * 日志配置
 */
export interface LoggingConfig {
  /** 日志级别 */
  level: 'debug' | 'info' | 'warn' | 'error';
  /** 是否启用控制台输出 */
  enableConsole: boolean;
  /** 是否启用远程日志 */
  enableRemote: boolean;
  /** 远程日志端点 */
  remoteEndpoint?: string;
  /** 最大日志缓存数量 */
  maxCacheSize: number;
}

/**
 * 错误处理配置
 */
export interface ErrorHandlingConfig {
  /** 是否启用全局错误捕获 */
  enableGlobalCapture: boolean;
  /** 是否启用错误报告 */
  enableReporting: boolean;
  /** 错误报告端点 */
  reportingEndpoint?: string;
  /** 错误采样率 */
  sampleRate: number;
  /** 是否在开发环境显示详细错误 */
  showDetailsInDev: boolean;
}

/**
 * 存储配置
 */
export interface StorageConfig {
  /** 本地存储前缀 */
  prefix: string;
  /** 是否启用加密 */
  enableEncryption: boolean;
  /** 存储版本 */
  version: string;
  /** 过期时间（毫秒） */
  expireTime: number;
}

/**
 * 获取环境变量值
 */
function getEnvVar(key: string, defaultValue: string = ''): string {
  if (browser) {
    return (window as any).__ENV__?.[key] || defaultValue;
  }
  return import.meta.env[key] || defaultValue;
}

/**
 * 获取环境变量布尔值
 */
function getEnvBool(key: string, defaultValue: boolean = false): boolean {
  const value = getEnvVar(key);
  if (!value) return defaultValue;
  return value.toLowerCase() === 'true' || value === '1';
}

/**
 * 获取环境变量数字值
 */
function getEnvNumber(key: string, defaultValue: number = 0): number {
  const value = getEnvVar(key);
  if (!value) return defaultValue;
  const num = Number(value);
  return isNaN(num) ? defaultValue : num;
}

/**
 * 获取当前环境
 */
function getCurrentEnvironment(): Environment {
  const mode = getEnvVar('MODE', 'development');
  const nodeEnv = getEnvVar('NODE_ENV', 'development');

  if (mode === 'test' || nodeEnv === 'test') return 'test';
  if (mode === 'production' || nodeEnv === 'production') return 'production';
  return 'development';
}

/**
 * 创建应用配置
 */
function createAppConfig(): AppConfig {
  const env = getCurrentEnvironment();
  const isDev = env === 'development';
  const isProd = env === 'production';
  const isTest = env === 'test';

  const config: AppConfig = {
    env,
    isDev,
    isProd,
    isTest,

    api: {
      baseUrl: getEnvVar('VITE_API_BASE_URL', '/api'),
      timeout: getEnvNumber('VITE_API_TIMEOUT', 10000),
      retryCount: getEnvNumber('VITE_API_RETRY_COUNT', 3),
      retryDelay: getEnvNumber('VITE_API_RETRY_DELAY', 1000),
      enableCache: getEnvBool('VITE_API_ENABLE_CACHE', true),
      cacheTime: getEnvNumber('VITE_API_CACHE_TIME', 300000), // 5分钟
      enableMSW: getEnvBool('VITE_USE_MSW', isDev),
    },

    charts: {
      defaultTheme: getEnvVar('VITE_CHART_DEFAULT_THEME', 'light'),
      animationDuration: getEnvNumber('VITE_CHART_ANIMATION_DURATION', 1000),
      enableAnimation: getEnvBool('VITE_CHART_ENABLE_ANIMATION', true),
      debounceDelay: getEnvNumber('VITE_CHART_DEBOUNCE_DELAY', 100),
      maxDataPoints: getEnvNumber('VITE_CHART_MAX_DATA_POINTS', 1000),
      enableVirtualization: getEnvBool('VITE_CHART_ENABLE_VIRTUALIZATION', true),
      virtualizationThreshold: getEnvNumber('VITE_CHART_VIRTUALIZATION_THRESHOLD', 500),
      defaultColors: [
        '#3b82f6',
        '#ef4444',
        '#10b981',
        '#f59e0b',
        '#8b5cf6',
        '#06b6d4',
        '#f97316',
        '#84cc16',
        '#ec4899',
        '#6366f1',
      ],
    },

    performance: {
      enableMonitoring: getEnvBool('VITE_PERFORMANCE_MONITORING', isProd),
      performanceBudget: {
        firstContentfulPaint: getEnvNumber('VITE_PERF_FCP_BUDGET', 2000),
        firstInputDelay: getEnvNumber('VITE_PERF_FID_BUDGET', 100),
        cumulativeLayoutShift: getEnvNumber('VITE_PERF_CLS_BUDGET', 0.1),
      },
      enableLazyLoading: getEnvBool('VITE_ENABLE_LAZY_LOADING', true),
      enableCodeSplitting: getEnvBool('VITE_ENABLE_CODE_SPLITTING', true),
    },

    features: {
      enableRealTimeData: getEnvBool('VITE_FEATURE_REAL_TIME_DATA', true),
      enableDataExport: getEnvBool('VITE_FEATURE_DATA_EXPORT', true),
      enableAdvancedCharts: getEnvBool('VITE_FEATURE_ADVANCED_CHARTS', true),
      enableThemeToggle: getEnvBool('VITE_FEATURE_THEME_TOGGLE', true),
      enableKeyboardShortcuts: getEnvBool('VITE_FEATURE_KEYBOARD_SHORTCUTS', true),
      enableNotifications: getEnvBool('VITE_FEATURE_NOTIFICATIONS', true),
      enableErrorReporting: getEnvBool('VITE_FEATURE_ERROR_REPORTING', isProd),
    },

    theme: {
      defaultTheme: getEnvVar('VITE_DEFAULT_THEME', 'system') as 'light' | 'dark' | 'system',
      enableThemeToggle: getEnvBool('VITE_ENABLE_THEME_TOGGLE', true),
      storageKey: getEnvVar('VITE_THEME_STORAGE_KEY', 'app-theme'),
      transitionDuration: getEnvNumber('VITE_THEME_TRANSITION_DURATION', 200),
    },

    logging: {
      level: getEnvVar('VITE_LOG_LEVEL', isDev ? 'debug' : 'warn') as LoggingConfig['level'],
      enableConsole: getEnvBool('VITE_LOG_ENABLE_CONSOLE', true),
      enableRemote: getEnvBool('VITE_LOG_ENABLE_REMOTE', isProd),
      remoteEndpoint: getEnvVar('VITE_LOG_REMOTE_ENDPOINT'),
      maxCacheSize: getEnvNumber('VITE_LOG_MAX_CACHE_SIZE', 1000),
    },

    errorHandling: {
      enableGlobalCapture: getEnvBool('VITE_ERROR_GLOBAL_CAPTURE', true),
      enableReporting: getEnvBool('VITE_ERROR_REPORTING', isProd),
      reportingEndpoint: getEnvVar('VITE_ERROR_REPORTING_ENDPOINT'),
      sampleRate: getEnvNumber('VITE_ERROR_SAMPLE_RATE', isProd ? 0.1 : 1.0),
      showDetailsInDev: getEnvBool('VITE_ERROR_SHOW_DETAILS_IN_DEV', isDev),
    },

    storage: {
      prefix: getEnvVar('VITE_STORAGE_PREFIX', 'svelte-demo'),
      enableEncryption: getEnvBool('VITE_STORAGE_ENCRYPTION', false),
      version: getEnvVar('VITE_STORAGE_VERSION', '1.0.0'),
      expireTime: getEnvNumber('VITE_STORAGE_EXPIRE_TIME', 7 * 24 * 60 * 60 * 1000), // 7天
    },
  };

  configLogger.info('Application config initialized', {
    env: config.env,
    features: Object.entries(config.features)
      .filter(([, enabled]) => enabled)
      .map(([feature]) => feature),
  });

  return config;
}

/**
 * 应用配置实例
 */
export const appConfig = createAppConfig();

/**
 * 获取配置值的辅助函数
 */
export function getConfig<K extends keyof AppConfig>(key: K): AppConfig[K] {
  return appConfig[key];
}

/**
 * 检查功能是否启用
 */
export function isFeatureEnabled(feature: keyof FeatureFlags): boolean {
  return appConfig.features[feature];
}

/**
 * 获取 API 配置
 */
export function getApiConfig(): ApiConfig {
  return appConfig.api;
}

/**
 * 获取图表配置
 */
export function getChartConfig(): ChartConfig {
  return appConfig.charts;
}

/**
 * 获取性能配置
 */
export function getPerformanceConfig(): PerformanceConfig {
  return appConfig.performance;
}

/**
 * 获取主题配置
 */
export function getThemeConfig(): ThemeConfig {
  return appConfig.theme;
}

/**
 * 获取日志配置
 */
export function getLoggingConfig(): LoggingConfig {
  return appConfig.logging;
}

/**
 * 获取错误处理配置
 */
export function getErrorHandlingConfig(): ErrorHandlingConfig {
  return appConfig.errorHandling;
}

/**
 * 获取存储配置
 */
export function getStorageConfig(): StorageConfig {
  return appConfig.storage;
}

/**
 * 运行时配置更新（仅限开发环境）
 */
export function updateConfig(updates: Partial<AppConfig>): void {
  if (!appConfig.isDev) {
    configLogger.warn('Config updates are only allowed in development environment');
    return;
  }

  Object.assign(appConfig, updates);
  configLogger.info('Config updated', { updates });
}

/**
 * 验证配置
 */
export function validateConfig(): boolean {
  const errors: string[] = [];

  // 验证 API 配置
  if (!appConfig.api.baseUrl) {
    errors.push('API base URL is required');
  }

  if (appConfig.api.timeout <= 0) {
    errors.push('API timeout must be positive');
  }

  // 验证图表配置
  if (appConfig.charts.maxDataPoints <= 0) {
    errors.push('Chart max data points must be positive');
  }

  // 验证性能预算
  const budget = appConfig.performance.performanceBudget;
  if (budget.firstContentfulPaint <= 0 || budget.firstInputDelay <= 0) {
    errors.push('Performance budget values must be positive');
  }

  if (errors.length > 0) {
    configLogger.error('Config validation failed', { errors });
    return false;
  }

  configLogger.debug('Config validation passed');
  return true;
}

// 在模块加载时验证配置
if (browser) {
  validateConfig();
}
