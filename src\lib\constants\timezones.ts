/**
 * 时区配置常量
 *
 * 提供标准化的时区选项，支持搜索和分类显示
 *
 * @category Constants
 */

/**
 * 时区选项接口
 */
export interface TimezoneOption {
  /** 时区标识符 */
  id: string;
  /** 显示名称 */
  displayName: string;
  /** UTC 偏移量 */
  offset: string;
  /** 描述信息 */
  description: string;
  /** 分类 */
  category?: 'special' | 'africa' | 'america' | 'asia' | 'europe';
  /** 是否为常用时区 */
  isCommon?: boolean;
}

/**
 * 特殊时区选项（默认、浏览器时间等）
 */
export const SPECIAL_TIMEZONES: TimezoneOption[] = [
  {
    id: 'Default',
    displayName: 'Default',
    offset: 'UTC+08:00',
    description: 'China, CST',
    category: 'special',
    isCommon: true,
  },
  {
    id: 'Browser Time',
    displayName: 'Browser Time',
    offset: 'UTC+08:00',
    description: 'China, CST',
    category: 'special',
    isCommon: true,
  },
  {
    id: 'UTC',
    displayName: 'Coordinated Universal Time',
    offset: 'UTC+00:00',
    description: 'UTC, GMT',
    category: 'special',
    isCommon: true,
  },
];

/**
 * 非洲时区
 */
export const AFRICA_TIMEZONES: TimezoneOption[] = [
  {
    id: 'Africa/Abidjan',
    displayName: 'Africa/Abidjan',
    offset: 'UTC+00:00',
    description: 'Burkina Faso, GMT',
    category: 'africa',
  },
  {
    id: 'Africa/Accra',
    displayName: 'Africa/Accra',
    offset: 'UTC+00:00',
    description: 'Ghana, GMT',
    category: 'africa',
  },
  {
    id: 'Africa/Addis_Ababa',
    displayName: 'Africa/Addis_Ababa',
    offset: 'UTC+03:00',
    description: 'Ethiopia, EAT',
    category: 'africa',
  },
  {
    id: 'Africa/Algiers',
    displayName: 'Africa/Algiers',
    offset: 'UTC+01:00',
    description: 'Algeria, CET',
    category: 'africa',
  },
  {
    id: 'Africa/Asmara',
    displayName: 'Africa/Asmara',
    offset: 'UTC+03:00',
    description: 'Eritrea, EAT',
    category: 'africa',
  },
];

/**
 * 美洲时区
 */
export const AMERICA_TIMEZONES: TimezoneOption[] = [
  {
    id: 'America/New_York',
    displayName: 'America/New_York',
    offset: 'UTC-05:00',
    description: 'Eastern Time',
    category: 'america',
    isCommon: true,
  },
  {
    id: 'America/Los_Angeles',
    displayName: 'America/Los_Angeles',
    offset: 'UTC-08:00',
    description: 'Pacific Time',
    category: 'america',
    isCommon: true,
  },
];

/**
 * 亚洲时区
 */
export const ASIA_TIMEZONES: TimezoneOption[] = [
  {
    id: 'Asia/Shanghai',
    displayName: 'Asia/Shanghai',
    offset: 'UTC+08:00',
    description: 'China, CST',
    category: 'asia',
    isCommon: true,
  },
  {
    id: 'Asia/Tokyo',
    displayName: 'Asia/Tokyo',
    offset: 'UTC+09:00',
    description: 'Japan Standard Time',
    category: 'asia',
    isCommon: true,
  },
];

/**
 * 欧洲时区
 */
export const EUROPE_TIMEZONES: TimezoneOption[] = [
  {
    id: 'Europe/London',
    displayName: 'Europe/London',
    offset: 'UTC+00:00',
    description: 'Greenwich Mean Time',
    category: 'europe',
    isCommon: true,
  },
  {
    id: 'Europe/Paris',
    displayName: 'Europe/Paris',
    offset: 'UTC+01:00',
    description: 'Central European Time',
    category: 'europe',
    isCommon: true,
  },
];

/**
 * 所有时区选项（合并后的完整列表）
 */
export const ALL_TIMEZONES: TimezoneOption[] = [
  ...SPECIAL_TIMEZONES,
  ...AFRICA_TIMEZONES,
  ...AMERICA_TIMEZONES,
  ...ASIA_TIMEZONES,
  ...EUROPE_TIMEZONES,
];

/**
 * 常用时区选项
 */
export const COMMON_TIMEZONES: TimezoneOption[] = ALL_TIMEZONES.filter(
  (timezone) => timezone.isCommon
);

/**
 * 按分类组织的时区选项
 */
export const TIMEZONES_BY_CATEGORY = {
  special: SPECIAL_TIMEZONES,
  africa: AFRICA_TIMEZONES,
  america: AMERICA_TIMEZONES,
  asia: ASIA_TIMEZONES,
  europe: EUROPE_TIMEZONES,
} as const;

/**
 * 时区分类标签映射
 */
export const TIMEZONE_CATEGORY_LABELS = {
  special: '特殊选项',
  africa: 'Africa',
  america: 'America',
  asia: 'Asia',
  europe: 'Europe',
} as const;

/**
 * 根据 ID 查找时区选项
 */
export function findTimezoneById(id: string): TimezoneOption | undefined {
  return ALL_TIMEZONES.find((timezone) => timezone.id === id);
}

/**
 * 搜索时区选项
 */
export function searchTimezones(query: string): TimezoneOption[] {
  if (!query.trim()) {
    return ALL_TIMEZONES;
  }

  const lowerQuery = query.toLowerCase();
  return ALL_TIMEZONES.filter(
    (timezone) =>
      timezone.displayName.toLowerCase().includes(lowerQuery) ||
      timezone.description.toLowerCase().includes(lowerQuery) ||
      timezone.offset.toLowerCase().includes(lowerQuery)
  );
}

/**
 * 获取时区的显示文本
 */
export function getTimezoneDisplayText(timezone: TimezoneOption): string {
  return `${timezone.displayName} (${timezone.offset})`;
}

/**
 * 获取默认时区
 */
export function getDefaultTimezone(): TimezoneOption {
  return SPECIAL_TIMEZONES[0]; // Default
}
