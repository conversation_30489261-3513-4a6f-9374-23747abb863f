<script lang="ts">
  import { Badge } from '$lib/components/ui/badge';
  import { CardHeader, CardTitle } from '$lib/components/ui/card';
  import type { MarketEvent, MarketEventType } from '$lib/types';
  import { MARKET_EVENT_TYPE_LABELS } from '$lib/types';

  // 本地实现 getEventTypeLabel 函数
  function getEventTypeLabel(eventType: MarketEventType): string {
    return (
      MARKET_EVENT_TYPE_LABELS[eventType as keyof typeof MARKET_EVENT_TYPE_LABELS] || eventType
    );
  }
  import {
    type CardSize,
    getEventDisplayName,
    getEventTypeBadgeVariant,
    getEventTypeColorClass,
    isAggregatedEvent,
  } from '../utils';

  interface Props {
    event: MarketEvent;
    size?: CardSize;
    showBadge?: boolean;
    customTitle?: string;
  }

  const { event, size = 'md', showBadge = true, customTitle }: Props = $props();

  // 获取标题文本
  const titleText = customTitle || getEventDisplayName(event);

  // 获取事件类型标签
  const eventTypeLabel = getEventTypeLabel(event.marketEventType);

  // 获取Badge变体
  const badgeVariant = getEventTypeBadgeVariant(event.marketEventType);

  // 获取颜色类
  const colorClass = getEventTypeColorClass(event.marketEventType);

  // 检查是否为聚合数据
  const isAggregated = isAggregatedEvent(event);

  // 根据尺寸设置样式
  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
  };

  const badgeSizeClasses = {
    sm: 'text-xs',
    md: 'text-xs',
    lg: 'text-sm',
  };
</script>

<CardHeader class="pb-3">
  <CardTitle class="flex items-center justify-between {sizeClasses[size]}">
    <div class="flex min-w-0 flex-1 items-center space-x-2">
      <span class="truncate font-semibold" title={titleText}>
        {titleText}
      </span>

      {#if isAggregated}
        <Badge variant="outline" class="flex-shrink-0 text-xs">聚合</Badge>
      {/if}
    </div>

    {#if showBadge}
      <Badge variant={badgeVariant} class="{badgeSizeClasses[size]} ml-2 flex-shrink-0">
        {eventTypeLabel}
      </Badge>
    {/if}
  </CardTitle>
</CardHeader>
