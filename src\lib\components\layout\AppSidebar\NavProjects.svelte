<!-- src/lib/components/layout/AppSidebar/NavProjects.svelte -->
<script lang="ts">
  import FolderIcon from '@lucide/svelte/icons/folder';
  import MoreHorizontalIcon from '@lucide/svelte/icons/more-horizontal';

  import { goto } from '$app/navigation';
  import { page } from '$app/stores';
  // shadcn-svelte 组件导入
  import * as Sidebar from '$lib/components/ui/sidebar/index.js';
  import { openTab } from '$lib/stores/ui';

  import { getTabInfoFromPath } from './config.js';
  import type { ProjectItem } from './types.js';

  interface Props {
    projects: ProjectItem[];
  }

  const { projects }: Props = $props();

  /**
   * 检查项目是否为当前活跃页面
   */
  function isProjectActive(project: ProjectItem): boolean {
    return $page.url.pathname === project.url;
  }

  /**
   * 处理项目导航点击事件
   */
  async function handleProjectNavigation(project: ProjectItem) {
    try {
      // 获取标签页信息并创建/激活标签页
      const tabInfo = getTabInfoFromPath(project.url);
      openTab(tabInfo);

      // 导航到目标页面
      await goto(project.url);
    } catch (error) {
      console.error('Project navigation failed:', error);
    }
  }

  /**
   * 获取项目状态显示文本
   */
  function getStatusText(status?: string): string {
    switch (status) {
      case 'active':
        return '运行中';
      case 'inactive':
        return '已停用';
      case 'development':
        return '开发中';
      default:
        return '';
    }
  }

  /**
   * 获取项目状态样式类
   */
  function getStatusClass(status?: string): string {
    switch (status) {
      case 'active':
        return 'text-green-600 dark:text-green-400';
      case 'inactive':
        return 'text-gray-500 dark:text-gray-400';
      case 'development':
        return 'text-yellow-600 dark:text-yellow-400';
      default:
        return '';
    }
  }
</script>

{#if projects && projects.length > 0}
  <Sidebar.Group class="group-data-[collapsible=icon]:hidden">
    <Sidebar.GroupLabel>项目快捷访问</Sidebar.GroupLabel>
    <Sidebar.Menu>
      {#each projects as project (project.name)}
        <Sidebar.MenuItem>
          <Sidebar.MenuButton
            tooltipContent={project.description || project.name}
            data-active={isProjectActive(project)}
            onclick={() => handleProjectNavigation(project)}
          >
            {#if project.icon}
              {@const IconComponent = project.icon}
              <IconComponent />
            {:else}
              <FolderIcon />
            {/if}
            <span class="truncate">{project.name}</span>
            {#if project.status}
              <span
                class="ml-auto text-xs {getStatusClass(project.status)}"
                title={getStatusText(project.status)}
              >
                {getStatusText(project.status)}
              </span>
            {/if}
          </Sidebar.MenuButton>

          <!-- 项目操作菜单（可选） -->
          <Sidebar.MenuAction
            onclick={(e) => {
              e.stopPropagation();
              // 这里可以添加项目操作菜单逻辑
              console.log('Project action clicked for:', project.name);
            }}
            title="项目操作"
          >
            <MoreHorizontalIcon />
            <span class="sr-only">更多操作</span>
          </Sidebar.MenuAction>
        </Sidebar.MenuItem>
      {/each}
    </Sidebar.Menu>
  </Sidebar.Group>
{/if}
