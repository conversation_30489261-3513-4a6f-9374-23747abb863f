<!-- src/lib/components/liquidation/charts/DualAxisLineChart.svelte -->
<script lang="ts">
  import type { EChartsOption } from 'echarts';
  import * as echarts from 'echarts';
  import { createEventDispatcher, onDestroy, onMount } from 'svelte';

  import type { LiquidationData } from '$lib/types';
  import { formatDate, formatNumber } from '$lib/utils/formatters';

  const dispatch = createEventDispatcher();

  export let options: EChartsOption = {};
  export let data: LiquidationData[] = [];
  export let title: string = '主流币 vs 山寨币 多/空清算金额(USD)';
  export let subtitle: string = '';
  export let timeFrame: string = '1d'; // 时间粒度参数

  let chartDom: HTMLDivElement;
  let myChart: echarts.ECharts;

  // 根据时间粒度生成时间键
  function getTimeKey(datetime: string, timeFrame: string): string {
    const date = new Date(datetime);

    switch (timeFrame) {
      case '1h': {
        // 按小时聚合：YYYY-MM-DD HH:00
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hour = String(date.getHours()).padStart(2, '0');
        return `${year}-${month}-${day} ${hour}:00`;
      }
      case '4h': {
        // 按4小时聚合：YYYY-MM-DD HH:00 (HH为0,4,8,12,16,20)
        const year4 = date.getFullYear();
        const month4 = String(date.getMonth() + 1).padStart(2, '0');
        const day4 = String(date.getDate()).padStart(2, '0');
        const hour4 = String(Math.floor(date.getHours() / 4) * 4).padStart(2, '0');
        return `${year4}-${month4}-${day4} ${hour4}:00`;
      }
      case '1d': {
        // 按天聚合：YYYY-MM-DD
        const yearD = date.getFullYear();
        const monthD = String(date.getMonth() + 1).padStart(2, '0');
        const dayD = String(date.getDate()).padStart(2, '0');
        return `${yearD}-${monthD}-${dayD}`;
      }
      case '1w': {
        // 按周聚合：使用周一作为周的开始
        const yearW = date.getFullYear();
        const startOfYear = new Date(yearW, 0, 1);
        const dayOfYear = Math.floor((date.getTime() - startOfYear.getTime()) / 86400000) + 1;
        const weekNumber = Math.ceil((dayOfYear + startOfYear.getDay()) / 7);
        return `${yearW}-W${String(weekNumber).padStart(2, '0')}`;
      }
      default:
        return formatDate(datetime, 'date');
    }
  }

  // 处理数据，按时间、币种类型和多空方向分组
  function processData(data: LiquidationData[]): {
    times: string[];
    majorSeries: { long: number[]; short: number[] };
    altcoinSeries: { long: number[]; short: number[] };
  } {
    if (!data || data.length === 0) {
      return {
        times: [],
        majorSeries: { long: [], short: [] },
        altcoinSeries: { long: [], short: [] },
      };
    }

    const timeMap = new Map<
      string,
      {
        majorLong: number;
        majorShort: number;
        altcoinLong: number;
        altcoinShort: number;
      }
    >();

    data.forEach((item) => {
      const timeKey = getTimeKey(item.datetime, timeFrame);

      if (!timeMap.has(timeKey)) {
        timeMap.set(timeKey, {
          majorLong: 0,
          majorShort: 0,
          altcoinLong: 0,
          altcoinShort: 0,
        });
      }

      const timeData = timeMap.get(timeKey)!;

      if (item.coinType === 'Major') {
        if (item.side === 1) {
          timeData.majorLong += item.amountUsd;
        } else {
          timeData.majorShort += item.amountUsd;
        }
      } else if (item.coinType === 'Altcoin') {
        if (item.side === 1) {
          timeData.altcoinLong += item.amountUsd;
        } else {
          timeData.altcoinShort += item.amountUsd;
        }
      }
    });

    const times = Array.from(timeMap.keys()).sort();

    const majorSeries = {
      long: times.map((time) => timeMap.get(time)!.majorLong),
      short: times.map((time) => timeMap.get(time)!.majorShort),
    };

    const altcoinSeries = {
      long: times.map((time) => timeMap.get(time)!.altcoinLong),
      short: times.map((time) => timeMap.get(time)!.altcoinShort),
    };

    return { times, majorSeries, altcoinSeries };
  }

  const baseChartOptions: EChartsOption = {
    title: {
      text: title,
      subtext: subtitle,
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
      formatter: function (params: any) {
        let result = params[0].name + '<br/>';

        params.forEach((param: any) => {
          const yAxisIndex = param.seriesName.includes('主流币') ? '左轴' : '右轴';
          result += `${param.marker} ${param.seriesName} (${yAxisIndex}): $${formatNumber(param.value, 2)}<br/>`;
        });

        return result;
      },
    },
    legend: {
      bottom: '6%', // 调整图例位置，避免与数据缩放滑块重叠
      data: ['主流币-多头', '主流币-空头', '山寨币-多头', '山寨币-空头'],
    },
    grid: {
      left: '8%',
      right: '8%',
      bottom: '25%',
      top: '15%',
      containLabel: true,
    },
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100,
        zoomOnMouseWheel: true,
        moveOnMouseMove: true,
        moveOnMouseWheel: false,
      },
      {
        type: 'slider',
        start: 0,
        end: 100,
        bottom: '2%',
        height: 20,
        handleIcon:
          'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23.1h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
        handleSize: '80%',
      },
    ],
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: [],
    },
    yAxis: [
      {
        type: 'value',
        name: '主流币清算金额 (USD)',
        position: 'left',
        axisLabel: {
          formatter: function (value: number) {
            if (value >= 1000000) {
              return (value / 1000000).toFixed(1) + 'M';
            } else if (value >= 1000) {
              return (value / 1000).toFixed(0) + 'K';
            }
            return value.toString();
          },
        },
        nameTextStyle: {
          color: '#1890ff',
        },
        axisLine: {
          lineStyle: {
            color: '#1890ff',
          },
        },
      },
      {
        type: 'value',
        name: '山寨币清算金额 (USD)',
        position: 'right',
        axisLabel: {
          formatter: function (value: number) {
            if (value >= 1000000) {
              return (value / 1000000).toFixed(1) + 'M';
            } else if (value >= 1000) {
              return (value / 1000).toFixed(0) + 'K';
            }
            return value.toString();
          },
        },
        nameTextStyle: {
          color: '#52c41a',
        },
        axisLine: {
          lineStyle: {
            color: '#52c41a',
          },
        },
      },
    ],
    series: [],
  };

  let chartInitialized = false;

  function initializeChart() {
    if (chartInitialized) return;
    myChart = echarts.init(chartDom);
    updateChart();
    chartInitialized = true;

    myChart.on('click', (params) => {
      dispatch('chartClick', {
        chartType: 'dualAxisLine',
        name: params.seriesName,
        value: params.value,
        dataIndex: params.dataIndex,
      });
    });
  }

  function updateChart() {
    if (!myChart) return;

    const processedData = processData(data);

    const seriesConfig = [
      {
        name: '主流币-多头',
        type: 'line',
        yAxisIndex: 0,
        data: processedData.majorSeries.long,
        color: '#1890ff',
        lineStyle: {
          width: 2,
          type: 'solid',
        },
        symbol: 'circle',
        symbolSize: 4,
        smooth: true,
      },
      {
        name: '主流币-空头',
        type: 'line',
        yAxisIndex: 0,
        data: processedData.majorSeries.short,
        color: '#722ed1',
        lineStyle: {
          width: 2,
          type: 'dashed',
        },
        symbol: 'triangle',
        symbolSize: 4,
        smooth: true,
      },
      {
        name: '山寨币-多头',
        type: 'line',
        yAxisIndex: 1,
        data: processedData.altcoinSeries.long,
        color: '#52c41a',
        lineStyle: {
          width: 2,
          type: 'solid',
        },
        symbol: 'circle',
        symbolSize: 4,
        smooth: true,
      },
      {
        name: '山寨币-空头',
        type: 'line',
        yAxisIndex: 1,
        data: processedData.altcoinSeries.short,
        color: '#f5222d',
        lineStyle: {
          width: 2,
          type: 'dashed',
        },
        symbol: 'triangle',
        symbolSize: 4,
        smooth: true,
      },
    ];

    myChart.setOption(
      {
        ...baseChartOptions,
        ...options,
        title: {
          ...baseChartOptions.title,
          text: title,
          subtext: subtitle,
        },
        xAxis: {
          ...baseChartOptions.xAxis,
          data: processedData.times,
        },
        series: seriesConfig,
      },
      { notMerge: false }
    );
  }

  onMount(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            initializeChart();
            observer.unobserve(chartDom);
          }
        });
      },
      { threshold: 0.1 }
    );

    observer.observe(chartDom);

    const resizeChart = () => {
      if (myChart) {
        myChart.resize();
      }
    };

    window.addEventListener('resize', resizeChart);

    onDestroy(() => {
      window.removeEventListener('resize', resizeChart);
      if (myChart) {
        myChart.dispose();
      }
    });
  });

  $: if (myChart && chartInitialized && (data || options || title || subtitle || timeFrame)) {
    updateChart();
  }
</script>

<div
  bind:this={chartDom}
  style="width: 100%; height: 100%;"
  aria-label="{title} - {subtitle}"
></div>
