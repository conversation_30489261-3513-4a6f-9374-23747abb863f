<script lang="ts">
  import { ArrowDown, ArrowUp, ArrowUpDown, TrendingDown,TrendingUp } from '@lucide/svelte';

  import { Badge } from '$lib/components/ui/badge';
  import { Button } from '$lib/components/ui/button';
  import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
  import { Skeleton } from '$lib/components/ui/skeleton';
  import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
  } from '$lib/components/ui/table';
  import type { HistoricalTableRow, MarketEventType } from '$lib/types';
  import { isDirectionalEventType } from '$lib/types/business/market';
  import { formatNumber } from '$lib/utils/formatters';
  import { createModuleLogger } from '$lib/utils/logger';

  const historicalTableLogger = createModuleLogger('historical-data-table');

  // 组件 Props 接口定义
  interface Props {
    tableRows?: HistoricalTableRow[];
    selectedCoins?: string[];
    loading?: boolean;
    total?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    dataType?: MarketEventType;
    onSort?: (detail: { field: string; order: 'asc' | 'desc' }) => void;
  }

  // 组件属性 - Svelte 5 语法
  let {
    tableRows = [],
    selectedCoins = [],
    loading = false,
    total = 0,
    sortBy = 'date',
    sortOrder = 'desc',
    dataType,
    onSort
  }: Props = $props();

  // 内部状态 - Svelte 5 语法
  let hoveredRow = $state<string | null>(null);

  // 响应式变量 - Svelte 5 语法
  let isDirectional = $derived(dataType && isDirectionalEventType(dataType));
  let directionalLabel = $derived(getDirectionalLabel(dataType));

  // 处理排序
  function handleSort(field: string) {
    const newOrder = sortBy === field && sortOrder === 'desc' ? 'asc' : 'desc';
    historicalTableLogger.debug('Sorting table', { field, order: newOrder });
    onSort?.({ field, order: newOrder });
  }

  // 获取排序图标组件 - Svelte 5 语法
  let sortIcon = $derived(sortBy !== 'date' ? ArrowUpDown : (sortOrder === 'asc' ? ArrowUp : ArrowDown));

  // 格式化变化百分比
  function formatChangePercent(value: number): string {
    const formatted = formatNumber(Math.abs(value), 2);
    return value >= 0 ? `+${formatted}%` : `-${formatted}%`;
  }

  // 获取变化百分比的样式类
  function getChangePercentClass(value: number): string {
    if (value > 0) return 'text-green-600';
    if (value < 0) return 'text-red-600';
    return 'text-muted-foreground';
  }

  // 处理行悬停
  function handleRowHover(date: string | null) {
    hoveredRow = date;
  }

  // 获取方向性数据标签
  function getDirectionalLabel(eventType: MarketEventType | undefined): {
    long: string;
    short: string;
  } {
    switch (eventType) {
      case 'LIQUIDATION_ORDER':
        return { long: '空头清算', short: '多头清算' };
      default:
        return { long: '买入', short: '卖出' };
    }
  }
</script>

<Card>
  <CardHeader class="pb-4">
    <CardTitle class="text-xl font-semibold">历史数据查询结果</CardTitle>
    {#if total > 0}
      <p class="text-muted-foreground text-sm">
        共 {total} 条记录
      </p>
    {/if}
  </CardHeader>
  <CardContent class="pt-0">
    <div class="w-full overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <!-- 日期列 -->
            <TableHead class="min-w-[120px]">
              <Button
                variant="ghost"
                size="sm"
                onclick={() => handleSort('date')}
                class="h-auto p-0 font-semibold hover:bg-transparent"
              >
                日期
                <sortIcon class="ml-1 h-4 w-4"></sortIcon>
              </Button>
            </TableHead>

            <!-- 动态币种列 -->
            {#each selectedCoins as coin}
              {#if isDirectional}
                <!-- 方向性数据：分离的多头/空头列 -->
                <TableHead class="min-w-[120px] text-center">
                  <div class="flex flex-col items-center">
                    <span class="font-semibold">{coin}</span>
                    <span class="text-xs text-red-600">{directionalLabel.short}</span>
                  </div>
                </TableHead>
                <TableHead class="min-w-[120px] text-center">
                  <div class="flex flex-col items-center">
                    <span class="font-semibold">{coin}</span>
                    <span class="text-xs text-green-600">{directionalLabel.long}</span>
                  </div>
                </TableHead>
              {:else}
                <!-- 非方向性数据：常规列 -->
                <TableHead class="min-w-[150px] text-center">
                  <div class="flex flex-col items-center">
                    <span class="font-semibold">{coin}</span>
                    <span class="text-muted-foreground text-xs">金额 (USD)</span>
                  </div>
                </TableHead>
              {/if}
            {/each}
          </TableRow>
        </TableHeader>
        <TableBody>
          {#if loading}
            <!-- 加载状态 -->
            {#each Array(5) as _}
              <TableRow>
                <TableCell><Skeleton class="h-4 w-20" /></TableCell>
                {#each selectedCoins as _}
                  {#if isDirectional}
                    <!-- 方向性数据：两列骨架屏 -->
                    <TableCell class="text-center"><Skeleton class="mx-auto h-4 w-20" /></TableCell>
                    <TableCell class="text-center"><Skeleton class="mx-auto h-4 w-20" /></TableCell>
                  {:else}
                    <!-- 非方向性数据：单列骨架屏 -->
                    <TableCell class="text-center"><Skeleton class="mx-auto h-4 w-24" /></TableCell>
                  {/if}
                {/each}
              </TableRow>
            {/each}
          {:else if tableRows.length === 0}
            <!-- 空状态 -->
            <TableRow>
              <TableCell
                colspan={isDirectional ? selectedCoins.length * 2 + 1 : selectedCoins.length + 1}
                class="py-8 text-center"
              >
                <div class="text-muted-foreground">
                  <p class="text-lg font-medium">暂无数据</p>
                  <p class="text-sm">请调整查询条件后重新查询</p>
                </div>
              </TableCell>
            </TableRow>
          {:else}
            <!-- 数据行 -->
            {#each tableRows as row}
              <TableRow
                class="hover:bg-muted/50 transition-colors"
                onmouseenter={() => handleRowHover(row.date)}
                onmouseleave={() => handleRowHover(null)}
              >
                <!-- 日期列 -->
                <TableCell class="font-medium">
                  <span>{row.date}</span>
                </TableCell>

                <!-- 币种数据列 -->
                {#each selectedCoins as coin}
                  {#if isDirectional && row.coinData[coin]?.directional}
                    <!-- 方向性数据：多头列 -->
                    <TableCell class="text-center">
                      <div class="flex items-center justify-center space-x-1">
                        <TrendingDown class="h-3 w-3 text-red-600" />
                        <span class="font-medium text-red-600">
                          {row.coinData[coin].directional.formattedShortAmount}
                        </span>
                      </div>
                    </TableCell>
                    <!-- 方向性数据：空头列 -->
                    <TableCell class="text-center">
                      <div class="flex items-center justify-center space-x-1">
                        <TrendingUp class="h-3 w-3 text-green-600" />
                        <span class="font-medium text-green-600">
                          {row.coinData[coin].directional.formattedLongAmount}
                        </span>
                      </div>
                    </TableCell>
                  {:else if !isDirectional && row.coinData[coin]}
                    <!-- 非方向性数据：常规列 -->
                    <TableCell class="text-center">
                      <div class="flex flex-col items-center space-y-1">
                        <span class="font-medium">
                          {row.coinData[coin].formattedAmount}
                        </span>
                        <div class="flex items-center space-x-2">
                          <Badge
                            variant="outline"
                            class={getChangePercentClass(row.coinData[coin].changePercent)}
                          >
                            {formatChangePercent(row.coinData[coin].changePercent)}
                          </Badge>
                        </div>
                        <span class="text-muted-foreground text-xs">
                          交易量: {row.coinData[coin].formattedVolume}
                        </span>
                      </div>
                    </TableCell>
                  {:else}
                    <!-- 无数据状态 -->
                    {#if isDirectional}
                      <TableCell class="text-center">
                        <span class="text-muted-foreground">-</span>
                      </TableCell>
                      <TableCell class="text-center">
                        <span class="text-muted-foreground">-</span>
                      </TableCell>
                    {:else}
                      <TableCell class="text-center">
                        <span class="text-muted-foreground">-</span>
                      </TableCell>
                    {/if}
                  {/if}
                {/each}
              </TableRow>
            {/each}
          {/if}
        </TableBody>
      </Table>
    </div>
  </CardContent>
</Card>

<style>
  /* 确保表格在小屏幕上可以水平滚动 */
  :global(.table-container) {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* 优化表格行的悬停效果 */
  :global(.table-row-hover) {
    background-color: hsl(var(--muted) / 0.5);
  }
</style>
