<script lang="ts">
  import { Portal } from 'bits-ui';

  // shadcn-svelte 组件导入
  import { Button } from '$lib/components/ui/button';
  import { Card, CardContent } from '$lib/components/ui/card';
  import { Checkbox } from '$lib/components/ui/checkbox';
  import { Label } from '$lib/components/ui/label';
  import { Select, SelectContent, SelectItem, SelectTrigger } from '$lib/components/ui/select';
  import { dashboardStore } from '$lib/stores/features/dashboard';

  import TimeRangeSelector from './TimeRangeSelector.svelte'; // 引入新的组件

  // 从dashboardStore获取状态
  const { loading } = dashboardStore;

  // 刷新间隔选项配置
  const refreshIntervalOptions = [
    { value: 'null', label: '手动刷新' },
    { value: '5000', label: '每5秒' },
    { value: '10000', label: '每10秒' },
    { value: '30000', label: '每30秒' },
  ];

  // 当前选中的刷新间隔值
  let selectedRefreshInterval = $state('null');

  // 刷新数据
  function refreshData() {
    dashboardStore.loadDashboardData();
  }

  // 处理刷新间隔变化
  function handleRefreshIntervalChange(value: string) {
    selectedRefreshInterval = value;
    const intervalValue = value === 'null' ? null : Number(value);
    dashboardStore.setRefreshInterval(intervalValue);
  }

  // 处理时间范围变化
  function handleTimeRangeChange(params: {
    selectedTimeRange: string;
    customStartTime: string;
    customEndTime: string;
    selectedTimeZone: string;
  }) {
    const { selectedTimeRange, customStartTime, customEndTime, selectedTimeZone } = params;
    dashboardStore.setTimeRange({
      selectedTimeRange,
      customStartTime,
      customEndTime,
      selectedTimeZone,
    });
    // 可以在这里处理自定义时间范围和时区，例如更新dashboardStore
    console.log('Selected Time Range:', selectedTimeRange);
    console.log('Custom Start Time:', customStartTime);
    console.log('Custom End Time:', customEndTime);
    console.log('Selected Time Zone:', selectedTimeZone);
  }

  // 切换图表可见性
  function toggleChartVisibility(chartId: string) {
    dashboardStore.toggleChartVisibility(chartId);
  }

  // 重置图表配置
  function resetChartConfig() {
    dashboardStore.reset();
  }
</script>

<!-- 控制面板 -->
<div
  class="flex flex-col items-center justify-between space-y-3 sm:flex-row sm:space-y-0 sm:space-x-4"
>
  <!-- 数据控制区域 -->
  <div class="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-2">
    <Button onclick={refreshData} disabled={$loading} size="sm" class="flex-shrink-0 gap-1">
      {#if $loading}
        刷新中...
      {:else}
        刷新数据 🔄
      {/if}
    </Button>
    <!-- 数据刷新间隔设置 -->
    <Select
      type="single"
      bind:value={selectedRefreshInterval}
      onValueChange={handleRefreshIntervalChange}
    >
      <SelectTrigger class="w-fit flex-shrink-0 text-sm" size="sm">
        {refreshIntervalOptions.find((option) => option.value === selectedRefreshInterval)?.label ||
          '手动刷新'}
      </SelectTrigger>
      <Portal>
        <SelectContent>
          {#each refreshIntervalOptions as option (option.value)}
            <SelectItem value={option.value} label={option.label}>
              {option.label}
            </SelectItem>
          {/each}
        </SelectContent>
      </Portal>
    </Select>
  </div>

  <!-- 时间范围选择器 -->
  <div class="flex-shrink-0">
    <TimeRangeSelector
      selectedTimeRange={$dashboardStore.selectedTimeRange}
      customStartTime={$dashboardStore.customStartTime}
      customEndTime={$dashboardStore.customEndTime}
      selectedTimeZone={$dashboardStore.selectedTimeZone}
      onTimeRangeChange={handleTimeRangeChange}
    />
  </div>
</div>

<!-- 图表配置控制 -->
<div class="flex min-h-[2.5rem] items-center justify-between gap-2">
  <div class="flex flex-1 items-center gap-2">
    <Label class="text-sm font-medium whitespace-nowrap">显示图表:</Label>
    <div class="flex flex-wrap items-center gap-1.5">
      {#each $dashboardStore.chartConfigs as chart (chart.id)}
        <label class="inline-flex cursor-pointer items-center gap-1.5 whitespace-nowrap">
          <Checkbox
            checked={chart.visible}
            onCheckedChange={() => toggleChartVisibility(chart.id)}
            class="size-4"
          />
          <span class="text-muted-foreground text-xs">
            {chart.title}
          </span>
        </label>
      {/each}
    </div>
  </div>
  <Button onclick={resetChartConfig} variant="destructive" size="sm" class="flex-shrink-0 text-xs">
    重置布局
  </Button>
</div>
