/**
 * 图表相关类型定义
 *
 * @category UI Types
 */

import type { Component } from 'svelte';

/**
 * 图表点击事件的数据类型
 */
export interface ChartClickEvent {
  /** 图表类型 */
  chartType: string;
  /** 数据项名称 */
  name: string;
  /** 数据值 */
  value: any;
}

/**
 * 柱状图数据项
 */
export interface BarChartItem {
  /** 数据项名称，通常用作 X 轴标签 */
  name: string;
  /** 数据值，用作柱状图的高度 */
  value: number;
}

/**
 * 折线图数据项
 */
export interface LineChartItem {
  /** 数据点名称，通常用作 X 轴标签 */
  name: string;
  /** 数据值，用作 Y 轴坐标 */
  value: number;
}

/**
 * 饼图数据项
 */
export interface PieChartItem {
  /** 扇形区域名称 */
  name: string;
  /** 数据值，决定扇形区域大小 */
  value: number;
}

/**
 * 散点图数据项
 */
export interface ScatterChartItem {
  /** 数据点名称 */
  name: string;
  /** X 轴坐标值 */
  x: number;
  /** Y 轴坐标值 */
  y: number;
  /** 数据点分类，用于区分不同系列 */
  category: string;
}

/**
 * 图表数据集合
 */
export interface ChartData {
  /** 柱状图数据数组 */
  barChartData: BarChartItem[];
  /** 折线图数据数组 */
  lineChartData: LineChartItem[];
  /** 饼图数据数组 */
  pieChartData: PieChartItem[];
  /** 散点图数据数组 */
  scatterChartData: ScatterChartItem[];
}

/**
 * 图表配置信息
 */
export interface ChartConfig {
  /** 图表唯一标识符 */
  id: string;
  /** 图表 Svelte 组件 */
  component: Component;
  /** 是否可见 */
  visible: boolean;
  /** 显示顺序 */
  order: number;
  /** 图表标题 */
  title: string;
  /** 图表副标题 */
  subTitle: string;
}

/**
 * 图表主题配置
 */
export interface ChartTheme {
  /** 主题名称 */
  name: string;
  /** 背景色 */
  backgroundColor: string;
  /** 文字颜色 */
  textColor: string;
  /** 网格线颜色 */
  gridColor: string;
  /** 坐标轴颜色 */
  axisColor: string;
  /** 系列颜色数组 */
  seriesColors: string[];
}

/**
 * 图表尺寸
 */
export interface ChartSize {
  /** 宽度 */
  width: number | string;
  /** 高度 */
  height: number | string;
}

/**
 * 图表选项
 */
export interface ChartOptions {
  /** 图表尺寸 */
  size?: ChartSize;
  /** 主题配置 */
  theme?: ChartTheme;
  /** 是否显示图例 */
  showLegend?: boolean;
  /** 是否显示工具栏 */
  showToolbox?: boolean;
  /** 是否启用数据缩放 */
  enableDataZoom?: boolean;
  /** 是否启用动画 */
  enableAnimation?: boolean;
  /** 自定义配置 */
  customOptions?: Record<string, any>;
}

/**
 * 图表系列数据
 */
export interface ChartSeries {
  /** 系列名称 */
  name: string;
  /** 系列类型 */
  type: 'line' | 'bar' | 'pie' | 'scatter' | 'area';
  /** 系列数据 */
  data: any[];
  /** 系列颜色 */
  color?: string;
  /** 是否显示 */
  visible?: boolean;
  /** Y轴索引（用于双轴图表） */
  yAxisIndex?: number;
}

/**
 * 图表轴配置
 */
export interface ChartAxis {
  /** 轴类型 */
  type: 'category' | 'value' | 'time' | 'log';
  /** 轴名称 */
  name?: string;
  /** 轴位置 */
  position?: 'left' | 'right' | 'top' | 'bottom';
  /** 最小值 */
  min?: number | string;
  /** 最大值 */
  max?: number | string;
  /** 是否显示 */
  show?: boolean;
}

/**
 * 图表状态
 */
export interface ChartState {
  /** 是否正在加载 */
  loading: boolean;
  /** 错误信息 */
  error: string | null;
  /** 是否有数据 */
  hasData: boolean;
  /** 最后更新时间 */
  lastUpdated: number;
}
