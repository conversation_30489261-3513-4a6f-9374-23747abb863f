<script lang="ts">
  import CalendarIcon from '@lucide/svelte/icons/calendar';
  import ChevronDownIcon from '@lucide/svelte/icons/chevron-down';
  import DollarSignIcon from '@lucide/svelte/icons/dollar-sign';
  import FilterIcon from '@lucide/svelte/icons/filter';
  import RefreshCwIcon from '@lucide/svelte/icons/refresh-cw';
  // 图标导入
  import SearchIcon from '@lucide/svelte/icons/search';
  import SettingsIcon from '@lucide/svelte/icons/settings';
  import { createEventDispatcher } from 'svelte';

  import { Badge } from '$lib/components/ui/badge';
  // shadcn-svelte 组件导入
  import { Button } from '$lib/components/ui/button';
  import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
  import { Input } from '$lib/components/ui/input';
  import { Label } from '$lib/components/ui/label';
  import { Select, SelectContent, SelectItem, SelectTrigger } from '$lib/components/ui/select';
  import { Separator } from '$lib/components/ui/separator';
  import { Switch } from '$lib/components/ui/switch';
  import type {
    DataType,
    DataTypeOption,
    MessageType,
    MessageTypeOption,
    QueryFormData,
    QueryFormState,
    SortField,
    SortOrder,
  } from '$lib/types';

  // 事件分发器
  const dispatch = createEventDispatcher<{
    query: QueryFormData;
    reset: void;
  }>();

  // 组件属性
  export let loading = false;
  export let showAdvanced = false;

  // 展开/收起状态（替代原有的showAdvanced逻辑）
  let isExpanded = true;

  // 表单数据状态
  let formData: QueryFormData = {
    timeRange: {
      startTime: '',
      endTime: '',
      timezone: 'UTC',
    },
    dataType: 'all',
    messageType: 'all',
    amountRange: {
      min: null,
      max: null,
      currency: 'USD',
    },
    keyword: '',
    sortField: 'timestamp',
    sortOrder: 'desc',
    unreadOnly: false,
    priority: 'all',
  };

  // 数据类型选项
  const dataTypeOptions: DataTypeOption[] = [
    { value: 'all', label: '全部数据', description: '显示所有类型的数据' },
    { value: 'liquidation', label: '清算数据', description: '加密货币清算相关数据' },
    { value: 'trade', label: '交易数据', description: '交易执行和订单数据' },
    { value: 'market', label: '市场数据', description: '价格和市场指标数据' },
    { value: 'notification', label: '通知数据', description: '系统通知和提醒' },
    { value: 'system', label: '系统数据', description: '系统状态和日志数据' },
  ];

  // 消息类型选项
  const messageTypeOptions: MessageTypeOption[] = [
    { value: 'all', label: '全部类型', description: '显示所有消息类型', color: 'gray' },
    { value: 'info', label: '信息', description: '一般信息消息', color: 'blue' },
    { value: 'success', label: '成功', description: '操作成功消息', color: 'green' },
    { value: 'warning', label: '警告', description: '警告提示消息', color: 'yellow' },
    { value: 'error', label: '错误', description: '错误和异常消息', color: 'red' },
    { value: 'notification', label: '通知', description: '系统通知消息', color: 'purple' },
  ];

  // 排序字段选项
  const sortFieldOptions = [
    { value: 'timestamp', label: '时间' },
    { value: 'amount', label: '金额' },
    { value: 'type', label: '类型' },
    { value: 'priority', label: '优先级' },
  ];

  // 优先级选项
  const priorityOptions = [
    { value: 'all', label: '全部优先级' },
    { value: 'high', label: '高优先级' },
    { value: 'medium', label: '中优先级' },
    { value: 'low', label: '低优先级' },
  ];

  // 货币选项
  const currencyOptions = [
    { value: 'USD', label: 'USD' },
    { value: 'BTC', label: 'BTC' },
    { value: 'ETH', label: 'ETH' },
  ];

  // 处理查询提交
  function handleQuery() {
    dispatch('query', formData);
  }

  // 处理重置表单
  function handleReset() {
    formData = {
      timeRange: {
        startTime: '',
        endTime: '',
        timezone: 'UTC',
      },
      dataType: 'all',
      messageType: 'all',
      amountRange: {
        min: null,
        max: null,
        currency: 'USD',
      },
      keyword: '',
      sortField: 'timestamp',
      sortOrder: 'desc',
      unreadOnly: false,
      priority: 'all',
    };
    dispatch('reset');
  }

  // 切换展开/收起状态
  function toggleExpanded() {
    isExpanded = !isExpanded;
  }

  // 切换高级选项显示（保留原有功能）
  function toggleAdvanced() {
    showAdvanced = !showAdvanced;
  }

  // 生成查询条件摘要
  function generateQuerySummary(data: QueryFormData): string {
    const summaryParts: string[] = [];

    // 时间范围摘要
    if (
      typeof data.timeRange === 'object' &&
      'startTime' in data.timeRange &&
      data.timeRange.startTime &&
      data.timeRange.endTime
    ) {
      const startDate = new Date(data.timeRange.startTime).toLocaleDateString('zh-CN');
      const endDate = new Date(data.timeRange.endTime).toLocaleDateString('zh-CN');
      summaryParts.push(`时间: ${startDate} - ${endDate}`);
    }

    // 数据类型摘要
    if (data.dataType !== 'all') {
      const dataTypeLabel =
        dataTypeOptions.find((opt) => opt.value === data.dataType)?.label || data.dataType;
      summaryParts.push(`数据: ${dataTypeLabel}`);
    }

    // 消息类型摘要
    if (data.messageType !== 'all') {
      const messageTypeLabel =
        messageTypeOptions.find((opt) => opt.value === data.messageType)?.label || data.messageType;
      summaryParts.push(`类型: ${messageTypeLabel}`);
    }

    // 关键词摘要
    if (data.keyword && data.keyword.trim()) {
      summaryParts.push(`关键词: "${data.keyword}"`);
    }

    // 金额范围摘要
    if (data.amountRange.min !== null || data.amountRange.max !== null) {
      const min = data.amountRange.min ? `${data.amountRange.min}` : '0';
      const max = data.amountRange.max ? `${data.amountRange.max}` : '∞';
      summaryParts.push(`金额: ${min}-${max} ${data.amountRange.currency}`);
    }

    // 其他条件摘要
    if (data.unreadOnly) {
      summaryParts.push('仅未读');
    }

    if (data.priority !== 'all') {
      const priorityLabel =
        priorityOptions.find((opt) => opt.value === data.priority)?.label || data.priority;
      summaryParts.push(`优先级: ${priorityLabel}`);
    }

    return summaryParts.length > 0 ? summaryParts.join(' | ') : '无筛选条件';
  }

  // 键盘快捷键处理
  function handleKeydown(event: KeyboardEvent) {
    // Enter 键提交查询
    if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
      event.preventDefault();
      handleQuery();
    }
    // Escape 键重置条件
    else if (event.key === 'Escape') {
      event.preventDefault();
      handleReset();
    }
    // Space 键切换展开/收起（当焦点在展开/收起按钮上时）
    else if (event.key === ' ' && event.target === document.activeElement) {
      const target = event.target as HTMLElement;
      if (target.textContent?.includes('展开') || target.textContent?.includes('收起')) {
        event.preventDefault();
        toggleExpanded();
      }
    }
  }

  // 获取当前时间的ISO字符串（用于datetime-local输入框）
  function getCurrentDateTime(): string {
    const now = new Date();
    now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
    return now.toISOString().slice(0, 16);
  }

  // 设置默认时间范围（最近24小时）
  function setDefaultTimeRange() {
    const now = new Date();
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
    yesterday.setMinutes(yesterday.getMinutes() - yesterday.getTimezoneOffset());

    formData.timeRange.endTime = now.toISOString().slice(0, 16);
    formData.timeRange.startTime = yesterday.toISOString().slice(0, 16);
  }

  // 组件挂载时设置默认时间范围
  import { onMount } from 'svelte';

  onMount(() => {
    setDefaultTimeRange();
  });
</script>

<Card
  onkeydown={handleKeydown}
  tabindex={0}
  class="focus:ring-ring focus:ring-2 focus:ring-offset-2 focus:outline-none"
>
  <CardHeader>
    <CardTitle class="flex items-center justify-between">
      <div class="flex items-center space-x-2">
        <FilterIcon class="h-5 w-5" />
        <span>复合条件查询</span>
        <Badge variant="outline">
          {showAdvanced ? '高级模式' : '简单模式'}
        </Badge>
      </div>

      <!-- 展开/收起按钮 -->
      <Button
        variant="ghost"
        size="sm"
        onclick={toggleExpanded}
        class="hover:bg-accent hover:text-accent-foreground flex items-center space-x-1 rounded-md px-3 py-2 transition-all duration-200"
      >
        <span class="text-sm font-medium">{isExpanded ? '收起' : '展开'}</span>
        <div
          class="transition-transform duration-300 ease-in-out {isExpanded
            ? 'rotate-180'
            : 'rotate-0'}"
        >
          <ChevronDownIcon class="h-4 w-4" />
        </div>
      </Button>
    </CardTitle>

    <!-- 收起状态下的查询条件摘要 -->
    {#if !isExpanded}
      <div class="animate-in slide-in-from-top-2 mt-4 duration-300 ease-out">
        <div class="bg-accent/50 border-border rounded-lg border p-4 shadow-sm">
          <div class="flex items-center justify-between gap-4">
            <div class="min-w-0 flex-1">
              <div class="mb-2 flex items-center justify-between">
                <div class="flex items-center space-x-2">
                  <FilterIcon class="text-primary h-4 w-4" />
                  <span class="text-foreground text-sm font-medium">当前查询条件</span>
                </div>
                <div class="text-muted-foreground text-xs opacity-75">
                  Ctrl+Enter 查询 | Esc 重置
                </div>
              </div>
              <p class="text-muted-foreground text-sm leading-relaxed">
                {generateQuerySummary(formData)}
              </p>
            </div>
            <div class="flex flex-shrink-0 items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onclick={handleReset}
                disabled={loading}
                class="h-8 px-3 text-xs transition-colors duration-200"
              >
                重置
              </Button>
              <Button
                onclick={handleQuery}
                disabled={loading}
                size="sm"
                class="relative h-8 overflow-hidden px-4 text-xs transition-colors duration-200"
              >
                {#if loading}
                  <div class="bg-primary/20 absolute inset-0 animate-pulse"></div>
                  <RefreshCwIcon class="relative z-10 mr-1 h-3 w-3 animate-spin" />
                  <span class="relative z-10">查询中</span>
                {:else}
                  <SearchIcon class="mr-1 h-3 w-3" />
                  查询
                {/if}
              </Button>
            </div>
          </div>
        </div>
      </div>
    {/if}
  </CardHeader>

  <!-- 展开状态下的完整表单 -->
  {#if isExpanded}
    <CardContent class="animate-in slide-in-from-top-2 space-y-4 duration-300 ease-out">
      <!-- 基础查询条件 -->
      <div class="bg-muted/50 border-border rounded-lg border p-4">
        <h3 class="text-foreground mb-3 flex items-center text-sm font-semibold">
          <SearchIcon class="mr-2 h-4 w-4" />
          基础查询条件
        </h3>
        <div class="space-y-4">
          <!-- 关键词搜索 -->
          <div class="flex items-center space-x-4">
            <Label for="keyword" class="w-24 flex-shrink-0 text-sm font-medium">关键词搜索</Label>
            <div class="relative flex-1">
              <SearchIcon
                class="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform"
              />
              <Input
                id="keyword"
                type="text"
                placeholder="输入搜索关键词..."
                bind:value={formData.keyword}
                class="pl-10"
              />
            </div>
          </div>

          <!-- 数据类型选择 -->
          <div class="flex items-center space-x-4">
            <Label class="w-24 flex-shrink-0 text-sm font-medium">数据类型</Label>
            <div class="flex-1">
              <Select
                type="single"
                bind:value={formData.dataType}
                onValueChange={(value) => (formData.dataType = value as DataType)}
              >
                <SelectTrigger>
                  {dataTypeOptions.find((opt) => opt.value === formData.dataType)?.label ||
                    '选择数据类型'}
                </SelectTrigger>
                <SelectContent>
                  {#each dataTypeOptions as option (option.value)}
                    <SelectItem value={option.value}>
                      <div class="flex flex-col">
                        <span>{option.label}</span>
                        <span class="text-muted-foreground text-xs">{option.description}</span>
                      </div>
                    </SelectItem>
                  {/each}
                </SelectContent>
              </Select>
            </div>
          </div>

          <!-- 消息类型选择 -->
          <div class="flex items-center space-x-4">
            <Label class="w-24 flex-shrink-0 text-sm font-medium">消息类型</Label>
            <div class="flex-1">
              <Select
                type="single"
                bind:value={formData.messageType}
                onValueChange={(value) => (formData.messageType = value as MessageType)}
              >
                <SelectTrigger>
                  {messageTypeOptions.find((opt) => opt.value === formData.messageType)?.label ||
                    '选择消息类型'}
                </SelectTrigger>
                <SelectContent>
                  {#each messageTypeOptions as option (option.value)}
                    <SelectItem value={option.value}>
                      <div class="flex items-center space-x-2">
                        <div
                          class="h-2 w-2 rounded-full {option.color === 'gray'
                            ? 'bg-muted-foreground'
                            : option.color === 'blue'
                              ? 'bg-blue-500 dark:bg-blue-400'
                              : option.color === 'green'
                                ? 'bg-success'
                                : option.color === 'yellow'
                                  ? 'bg-yellow-500 dark:bg-yellow-400'
                                  : option.color === 'red'
                                    ? 'bg-destructive'
                                    : option.color === 'purple'
                                      ? 'bg-purple-500 dark:bg-purple-400'
                                      : 'bg-muted-foreground'}"
                        ></div>
                        <div class="flex flex-col">
                          <span>{option.label}</span>
                          <span class="text-muted-foreground text-xs">{option.description}</span>
                        </div>
                      </div>
                    </SelectItem>
                  {/each}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </div>

      <!-- 时间范围选择 -->
      <div class="bg-muted/50 border-border rounded-lg border p-4">
        <h3 class="text-foreground mb-3 flex items-center text-sm font-semibold">
          <CalendarIcon class="mr-2 h-4 w-4" />
          时间范围
        </h3>
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div class="space-y-1">
            <Label for="startTime" class="text-muted-foreground text-xs">开始时间</Label>
            <Input
              id="startTime"
              type="datetime-local"
              bind:value={formData.timeRange.startTime}
              class="text-sm"
            />
          </div>
          <div class="space-y-1">
            <Label for="endTime" class="text-muted-foreground text-xs">结束时间</Label>
            <Input
              id="endTime"
              type="datetime-local"
              bind:value={formData.timeRange.endTime}
              class="text-sm"
            />
          </div>
        </div>
      </div>

      <!-- 高级选项切换 -->
      <div class="flex items-center justify-between">
        <Button variant="ghost" size="sm" onclick={toggleAdvanced} class="text-sm">
          <SettingsIcon class="mr-2 h-4 w-4" />
          {showAdvanced ? '隐藏高级选项' : '显示高级选项'}
        </Button>

        <div class="flex items-center space-x-2">
          <Switch bind:checked={formData.unreadOnly} />
          <Label class="text-sm">仅显示未读</Label>
        </div>
      </div>

      <!-- 高级选项 -->
      {#if showAdvanced}
        <Separator />
        <div class="bg-secondary/50 border-border space-y-4 rounded-lg border p-4">
          <h3 class="text-secondary-foreground mb-3 flex items-center text-sm font-semibold">
            <SettingsIcon class="mr-2 h-4 w-4" />
            高级选项
          </h3>
          <!-- 金额范围 -->
          <div class="space-y-2">
            <Label class="flex items-center space-x-2 text-sm font-medium">
              <DollarSignIcon class="h-4 w-4" />
              <span>金额范围</span>
            </Label>
            <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
              <div class="space-y-1">
                <Label for="minAmount" class="text-muted-foreground text-xs">最小金额</Label>
                <Input
                  id="minAmount"
                  type="number"
                  placeholder="0"
                  bind:value={formData.amountRange.min}
                  class="text-sm"
                />
              </div>
              <div class="space-y-1">
                <Label for="maxAmount" class="text-muted-foreground text-xs">最大金额</Label>
                <Input
                  id="maxAmount"
                  type="number"
                  placeholder="无限制"
                  bind:value={formData.amountRange.max}
                  class="text-sm"
                />
              </div>
              <div class="space-y-1">
                <Label class="text-muted-foreground text-xs">货币单位</Label>
                <Select
                  type="single"
                  bind:value={formData.amountRange.currency}
                  onValueChange={(value) =>
                    (formData.amountRange.currency = value as 'USD' | 'BTC' | 'ETH')}
                >
                  <SelectTrigger class="text-sm">
                    {formData.amountRange.currency}
                  </SelectTrigger>
                  <SelectContent>
                    {#each currencyOptions as option (option.value)}
                      <SelectItem value={option.value}>{option.label}</SelectItem>
                    {/each}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <!-- 排序和优先级 -->
          <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
            <div class="space-y-2">
              <Label class="text-sm font-medium">排序字段</Label>
              <Select
                type="single"
                bind:value={formData.sortField}
                onValueChange={(value) => (formData.sortField = value as SortField)}
              >
                <SelectTrigger class="text-sm">
                  {sortFieldOptions.find((opt) => opt.value === formData.sortField)?.label ||
                    '选择排序字段'}
                </SelectTrigger>
                <SelectContent>
                  {#each sortFieldOptions as option (option.value)}
                    <SelectItem value={option.value}>{option.label}</SelectItem>
                  {/each}
                </SelectContent>
              </Select>
            </div>

            <div class="space-y-2">
              <Label class="text-sm font-medium">排序方向</Label>
              <Select
                type="single"
                bind:value={formData.sortOrder}
                onValueChange={(value) => (formData.sortOrder = value as SortOrder)}
              >
                <SelectTrigger class="text-sm">
                  {formData.sortOrder === 'desc' ? '降序' : '升序'}
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="desc">降序</SelectItem>
                  <SelectItem value="asc">升序</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div class="space-y-2">
              <Label class="text-sm font-medium">优先级</Label>
              <Select
                type="single"
                bind:value={formData.priority}
                onValueChange={(value) =>
                  (formData.priority = value as 'all' | 'high' | 'medium' | 'low')}
              >
                <SelectTrigger class="text-sm">
                  {priorityOptions.find((opt) => opt.value === formData.priority)?.label ||
                    '选择优先级'}
                </SelectTrigger>
                <SelectContent>
                  {#each priorityOptions as option (option.value)}
                    <SelectItem value={option.value}>{option.label}</SelectItem>
                  {/each}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      {/if}

      <!-- 操作按钮 -->
      <Separator />
      <div class="flex items-center justify-between">
        <Button variant="outline" size="sm" onclick={handleReset} disabled={loading}>
          重置条件
        </Button>

        <Button
          onclick={handleQuery}
          disabled={loading}
          size="sm"
          class="relative min-w-[100px] overflow-hidden"
        >
          {#if loading}
            <div class="bg-primary/20 absolute inset-0 animate-pulse"></div>
            <RefreshCwIcon class="relative z-10 mr-2 h-4 w-4 animate-spin" />
            <span class="relative z-10">查询中...</span>
          {:else}
            <SearchIcon class="mr-2 h-4 w-4" />
            开始查询
          {/if}
        </Button>
      </div>
    </CardContent>
  {/if}
</Card>
