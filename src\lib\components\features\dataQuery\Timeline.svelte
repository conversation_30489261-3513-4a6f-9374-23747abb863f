<script lang="ts">
  import { Separator } from '$lib/components/ui/separator';
  import { EventCardFactory } from '$lib/components/features/dataQuery/cards';
  import EventCardDetailModal from '$lib/components/features/dataQuery/cards/EventCardDetailModal.svelte';
  import { cardInteractionStore } from '$lib/stores/features/cardInteraction';
  import type { MarketEvent, MarketEventType } from '$lib/types';
  import { MARKET_EVENT_TYPE_LABELS } from '$lib/types';
  import Clock from '@lucide/svelte/icons/clock';

  interface Props {
    items: MarketEvent[];
  }

  const { items = [] }: Props = $props();

  // 拖动配置接口
  interface DragOptions {
    sensitivity?: number;
    minDistance?: number;
    scrollAmount?: number;
    onDragStart?: () => void;
    onDragEnd?: () => void;
  }

  // 获取所有唯一的市场事件类型
  const uniqueTypes: MarketEventType[] = $derived(
    [...new Set(items.map((item) => item.marketEventType))].sort() as MarketEventType[]
  );

  // 卡片组件最小宽度配置映射
  const CARD_MIN_WIDTHS: Record<MarketEventType, string> = {
    LIQUIDATION_ORDER: 'min-w-36', // 9rem
    TWAP: 'min-w-36', // 9rem
    EXCHANGE_TRANSFER: 'min-w-30', // 7.5rem
    VOLUME_INCREASE: 'min-w-36', // 9rem
    EXTREME_FUNDING_RATE: 'min-w-30', // 7.5rem
    FUNDING_RATE_SWITCH: 'min-w-30', // 7.5rem
    OPEN_INTEREST_VARIATION: 'min-w-48', // 12rem
    ORDERBOOK_IMBALANCE: 'min-w-72', // 18rem
  };

  // 获取事件类型对应的最小宽度类名
  function getColumnMinWidth(type: MarketEventType): string {
    return CARD_MIN_WIDTHS[type] || 'min-w-30'; // 默认最小宽度
  }

  // 拖动状态管理（使用 Svelte 5 $state）
  let isDragging = $state(false);
  let timelineContainer: HTMLElement = $state()!;

  // 滚动位置状态（使用 Svelte 绑定优化 DOM 操作）
  let scrollLeft = $state(0);
  let scrollWidth = $state(0);
  let clientWidth = $state(0);

  // 滚动相关的派生状态
  const maxScrollLeft = $derived(Math.max(0, scrollWidth - clientWidth));
  const canScroll = $derived(scrollWidth > clientWidth);

  // 默认拖动配置（使用 Svelte 5 状态更新）
  const defaultDragOptions: DragOptions = {
    sensitivity: 1,
    minDistance: 3,
    scrollAmount: 100,
    onDragStart: () => {
      isDragging = true;
    },
    onDragEnd: () => {
      isDragging = false;
    },
  };

  // 拖动指令函数
  function dragAction(node: HTMLElement, options: DragOptions = {}) {
    const config = { ...defaultDragOptions, ...options };

    // 局部状态管理
    let localIsDragging = false;
    let dragStartX = 0;
    let dragStartScrollLeft = 0;
    let animationFrameId: number | null = null;
    let pendingScrollLeft: number | null = null;
    let cachedElements: HTMLElement[] = [];

    // 缓存子元素（优化版本）
    function cacheChildElements() {
      cachedElements = Array.from(node.querySelectorAll('*')) as HTMLElement[];
    }

    // 设置指针事件状态
    function setPointerEvents(disabled: boolean) {
      // 对于需要直接控制的元素，使用 DOM 操作
      if (cachedElements.length === 0) {
        cacheChildElements();
      }
      cachedElements.forEach((el) => {
        el.style.pointerEvents = disabled ? 'none' : '';
      });
    }

    // 滚动更新函数（使用响应式状态）
    function updateScroll() {
      if (pendingScrollLeft !== null) {
        // 使用响应式状态更新滚动位置
        scrollLeft = pendingScrollLeft;
        pendingScrollLeft = null;
      }
      animationFrameId = null;
    }

    // 拖动开始处理
    function handleMouseDown(event: MouseEvent) {
      // 只响应左键点击
      if (event.button !== 0) return;

      // 检查是否点击在可拖动区域（避免与卡片交互冲突）
      const target = event.target as HTMLElement;
      if (target.closest('.event-card') || target.closest('button') || target.closest('input')) {
        return;
      }

      // 检查容器是否可滚动（使用响应式状态）
      if (!canScroll) {
        return; // 如果内容不超出容器，不需要拖动
      }

      localIsDragging = true;
      dragStartX = event.clientX;
      dragStartScrollLeft = scrollLeft;

      // 触发外部回调
      config.onDragStart?.();

      // 动态设置拖动时的指针事件处理
      setPointerEvents(true);

      // 添加全局事件监听器
      document.addEventListener('mousemove', handleMouseMove, { passive: false });
      document.addEventListener('mouseup', handleMouseUp);

      // 防止页面滚动
      document.body.style.overflow = 'hidden';
    }

    // 拖动过程处理
    function handleMouseMove(event: MouseEvent) {
      if (!localIsDragging) return;

      const deltaX = event.clientX - dragStartX;

      // 检查是否达到最小拖动距离
      if (Math.abs(deltaX) < (config.minDistance || 3)) {
        return;
      }

      // 应用拖动敏感度
      const adjustedDelta = deltaX * (config.sensitivity || 1);
      const newScrollLeft = dragStartScrollLeft - adjustedDelta;

      // 边界检测（使用响应式状态）
      const clampedScrollLeft = Math.max(0, Math.min(newScrollLeft, maxScrollLeft));

      // 使用 requestAnimationFrame 节流，避免过度频繁更新
      pendingScrollLeft = clampedScrollLeft;

      if (animationFrameId === null) {
        animationFrameId = requestAnimationFrame(updateScroll);
      }
    }

    // 拖动结束处理
    function handleMouseUp() {
      if (!localIsDragging) return;

      localIsDragging = false;

      // 清理动画帧
      if (animationFrameId !== null) {
        cancelAnimationFrame(animationFrameId);
        animationFrameId = null;
      }

      // 执行最后一次滚动更新（如果有待处理的）
      if (pendingScrollLeft !== null) {
        updateScroll();
      }

      // 恢复指针事件
      setPointerEvents(false);

      // 恢复页面滚动
      document.body.style.overflow = '';

      // 移除全局事件监听器
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);

      // 触发外部回调
      config.onDragEnd?.();
    }

    // 键盘导航处理（使用响应式状态）
    function handleKeyDown(event: KeyboardEvent) {
      const scrollAmount = config.scrollAmount || 100;

      switch (event.key) {
        case 'ArrowLeft':
          scrollLeft = Math.max(0, scrollLeft - scrollAmount);
          break;
        case 'ArrowRight':
          scrollLeft = Math.min(maxScrollLeft, scrollLeft + scrollAmount);
          break;
        case 'Home':
          scrollLeft = 0;
          break;
        case 'End':
          scrollLeft = maxScrollLeft;
          break;
      }
    }

    // 绑定事件监听器
    node.addEventListener('mousedown', handleMouseDown);
    node.addEventListener('keydown', handleKeyDown);

    // 返回销毁函数
    return {
      destroy() {
        // 清理拖动状态
        if (localIsDragging) {
          handleMouseUp();
        }

        // 清理动画帧
        if (animationFrameId !== null) {
          cancelAnimationFrame(animationFrameId);
        }

        // 恢复指针事件
        setPointerEvents(false);

        // 恢复页面滚动
        document.body.style.overflow = '';

        // 移除事件监听器
        node.removeEventListener('mousedown', handleMouseDown);
        node.removeEventListener('keydown', handleKeyDown);
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      },
    };
  }

  // 同步 DOM 属性和响应式状态（DOM 操作优化）
  $effect(() => {
    if (timelineContainer) {
      // 监听滚动事件，同步滚动位置
      const handleScroll = () => {
        scrollLeft = timelineContainer.scrollLeft;
        scrollWidth = timelineContainer.scrollWidth;
        clientWidth = timelineContainer.clientWidth;
      };

      // 初始化值
      handleScroll();

      // 监听滚动事件
      timelineContainer.addEventListener('scroll', handleScroll, { passive: true });

      // 监听尺寸变化 - 使用防抖避免 ResizeObserver 循环
      let resizeTimeout: ReturnType<typeof setTimeout>;
      const debouncedHandleResize = () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
          try {
            handleScroll();
          } catch (error) {
            // 忽略 ResizeObserver 循环错误
            if (error instanceof Error && !error.message.includes('ResizeObserver loop')) {
              throw error;
            }
          }
        }, 16); // 约 60fps
      };

      const resizeObserver = new ResizeObserver(debouncedHandleResize);
      resizeObserver.observe(timelineContainer);

      // 清理函数
      return () => {
        clearTimeout(resizeTimeout);
        timelineContainer.removeEventListener('scroll', handleScroll);
        resizeObserver.disconnect();
      };
    }
  });

  // 响应式更新滚动位置（当 scrollLeft 状态变化时）
  $effect(() => {
    if (timelineContainer && timelineContainer.scrollLeft !== scrollLeft) {
      timelineContainer.scrollLeft = scrollLeft;
    }
  });

  // 事件卡片点击处理
  function handleCardClick(event: MarketEvent) {
    cardInteractionStore.showDetailModal(event);
  }

  // 模态框关闭处理
  function handleModalClose() {
    cardInteractionStore.hideDetailModal();
  }

  // 将Unix时间戳归并到15分钟间隔
  function roundToFifteenMinutes(timestamp: number): string {
    const date = new Date(timestamp * 1000);
    const minutes = date.getMinutes();
    const roundedMinutes = Math.floor(minutes / 15) * 15;

    const roundedDate = new Date(date);
    roundedDate.setMinutes(roundedMinutes, 0, 0);

    return roundedDate.toISOString();
  }

  // 按15分钟间隔分组数据（使用过滤后的数据，使用 Svelte 5 $derived）
  const itemsByTimeSlot = $derived(
    items.reduce(
      (acc, item) => {
        const timeSlot = roundToFifteenMinutes(item.date);
        if (!acc[timeSlot]) {
          acc[timeSlot] = [];
        }
        acc[timeSlot].push(item);
        return acc;
      },
      {} as Record<string, MarketEvent[]>
    )
  );

  // 获取所有15分钟时间间隔并排序（使用 Svelte 5 $derived）
  const allTimeSlots = $derived(
    Object.keys(itemsByTimeSlot).sort((a, b) => new Date(b).getTime() - new Date(a).getTime())
  );

  // 格式化时间显示
  function formatTime(dateString: string): string {
    const date = new Date(dateString);
    const endDate = new Date(date.getTime() + 15 * 60 * 1000);

    const startTime = date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });

    const endTime = endDate.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });

    return `${startTime}-${endTime}`;
  }

  // 格式化日期显示
  function formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  }
</script>

<div class="w-full">
  {#if uniqueTypes.length === 0}
    <!-- 空状态 -->
    <div class="flex flex-col items-center justify-center py-12 text-center">
      <Clock class="text-muted-foreground h-6 w-6" />
      <p class="text-muted-foreground text-sm">暂无时间轴数据</p>
    </div>
  {:else}
    <!-- svelte-ignore a11y_no_noninteractive_element_interactions -->
    <!-- svelte-ignore a11y_no_noninteractive_tabindex -->
    <section
      class="flex h-full overflow-x-auto scroll-auto p-4"
      class:select-none={isDragging}
      class:transition-none={isDragging}
      class:shadow-lg={isDragging}
      class:cursor-grab={!isDragging}
      class:cursor-grabbing={isDragging}
      bind:this={timelineContainer}
      use:dragAction={defaultDragOptions}
      aria-label="时间线拖动区域"
      aria-describedby="timeline-instructions"
      tabindex="0"
    >
      <div id="timeline-instructions" class="sr-only">
        使用鼠标拖动或方向键来平移视图。按Home键回到开始，按End键到达结尾。
      </div>
      <div class="w-full">
        <!-- 表头：类型列标题 -->
        <div class="mb-6 flex">
          <!-- 固定区域：时间列和时间轴占位 -->
          <div class="flex flex-shrink-0">
            <div class="w-24"></div>
            <!-- 时间列占位 -->
            <div class="w-1"></div>
            <!-- 时间轴占位 -->
          </div>

          <!-- 表头内容 -->
          <div class="flex-1 pl-4">
            <div class="flex gap-3 pt-2">
              {#each uniqueTypes as type (type)}
                <div class="flex-1 text-center {getColumnMinWidth(type)}">
                  <h3
                    class="text-foreground bg-accent/30 border-border/20 rounded-lg border px-3 py-2 text-sm font-semibold whitespace-nowrap"
                  >
                    {MARKET_EVENT_TYPE_LABELS[type] || type}
                  </h3>
                </div>
              {/each}
            </div>
          </div>
        </div>

        <!-- 时间行 -->
        {#each allTimeSlots as timeSlot (timeSlot)}
          {@const timeSlotItems = itemsByTimeSlot[timeSlot] || []}
          {@const totalItemsInSlot = timeSlotItems.length}
          <div class="relative mb-6 flex">
            <!-- 时间显示（左侧） -->
            <div class="flex w-24 flex-shrink-0 flex-col justify-start pr-4 text-right">
              <div
                class="text-foreground bg-accent/20 mb-1 rounded px-2 py-1 font-mono text-xs font-medium"
              >
                {formatTime(timeSlot)}
              </div>
              <div class="text-muted-foreground mb-1 font-mono text-xs">
                {formatDate(timeSlot)}
              </div>
              {#if totalItemsInSlot > 0}
                <div class="text-muted-foreground text-xs">
                  {totalItemsInSlot} 条记录
                </div>
              {/if}
            </div>

            <!-- 时间轴节点 -->
            <div class="flex w-1 flex-shrink-0 items-start justify-center pt-2">
              <div
                class="border-background dark:border-card flex size-4 items-center justify-center rounded-full border-2 shadow-sm"
                class:bg-primary={totalItemsInSlot > 0}
                class:bg-muted={totalItemsInSlot === 0}
              ></div>
            </div>

            <!-- 类型列 -->
            <div class="flex-1 pl-4">
              <div class="flex items-start gap-3 pt-2">
                {#each uniqueTypes as type (type)}
                  {@const typeItems = (itemsByTimeSlot[timeSlot] || []).filter(
                    (item) => item.marketEventType === type
                  )}
                  <div class="flex flex-1 flex-col space-y-2 {getColumnMinWidth(type)}">
                    {#if typeItems.length > 0}
                      {#each typeItems as item (item.id)}
                        <div class="event-card">
                          <EventCardFactory
                            event={item}
                            size="sm"
                            variant="compact"
                            useSpecificCard={true}
                            isHighlighted={$cardInteractionStore.selectedEventId === item.id}
                            onCardClick={handleCardClick}
                          />
                        </div>
                      {/each}
                    {/if}
                  </div>
                {/each}
              </div>
            </div>
          </div>
        {/each}
      </div>
    </section>
  {/if}
</div>

<!-- 详细卡片模态框 -->
<EventCardDetailModal
  open={$cardInteractionStore.showDetailModal}
  event={$cardInteractionStore.selectedEvent}
  onClose={handleModalClose}
/>
