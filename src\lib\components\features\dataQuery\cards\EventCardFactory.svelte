<script lang="ts">
  import BaseEventCard from './base/BaseEventCard.svelte';
  import ExchangeTransferCard from './types/ExchangeTransferCard.svelte';
  import ExtremeFundingRateCard from './types/ExtremeFundingRateCard.svelte';
  import FundingRateSwitchCard from './types/FundingRateSwitchCard.svelte';
  // 导入特定类型卡片组件
  import LiquidationOrderCard from './types/LiquidationOrderCard.svelte';
  import OpenInterestVariationCard from './types/OpenInterestVariationCard.svelte';
  import OrderbookImbalanceCard from './types/OrderbookImbalanceCard.svelte';
  import TwapDetectionCard from './types/TwapDetectionCard.svelte';
  import VolumeIncreaseCard from './types/VolumeIncreaseCard.svelte';
  import type { BaseEventCardProps } from './utils';

  interface Props extends BaseEventCardProps {
    useSpecificCard?: boolean;
  }

  const {
    event,
    isHighlighted = false,
    size = 'md',
    variant = 'default',
    showActions = false,
    onCardClick,
    useSpecificCard = true,
  }: Props = $props();

  // 根据事件类型选择合适的卡片组件
  const CardComponent = $derived.by(() => {
    if (!useSpecificCard) {
      return BaseEventCard;
    }

    switch (event.marketEventType) {
      case 'LIQUIDATION_ORDER':
        return LiquidationOrderCard;
      case 'TWAP':
        return TwapDetectionCard;
      case 'EXCHANGE_TRANSFER':
        return ExchangeTransferCard;
      case 'ORDERBOOK_IMBALANCE':
        return OrderbookImbalanceCard;
      case 'FUNDING_RATE_SWITCH':
        return FundingRateSwitchCard;
      case 'EXTREME_FUNDING_RATE':
        return ExtremeFundingRateCard;
      case 'VOLUME_INCREASE':
        return VolumeIncreaseCard;
      case 'OPEN_INTEREST_VARIATION':
        return OpenInterestVariationCard;
      default:
        return BaseEventCard;
    }
  });
</script>

<!-- 动态渲染对应的卡片组件 -->
<CardComponent {event} {isHighlighted} {size} {variant} {showActions} {onCardClick} />
