import { browser } from '$app/environment';

// MSW 初始化状态管理
let mockInitialized = false;
let initializationPromise: Promise<void> | null = null;

// 优化的同步初始化 MSW - 确保首页数据加载正常
async function initializeMocks(): Promise<void> {
  // 如果已经初始化完成，直接返回
  if (mockInitialized) {
    return;
  }

  // 如果正在初始化，等待现有的初始化完成
  if (initializationPromise) {
    return initializationPromise;
  }

  // 只在浏览器环境中初始化
  if (!browser) {
    return;
  }

  // 创建初始化 Promise，避免重复初始化
  initializationPromise = (async () => {
    try {
      console.log('MSW: 开始同步初始化，确保首页数据加载...');

      const { default: initMocks } = await import('../mocks');
      await initMocks();

      mockInitialized = true;
      console.log('MSW: 同步初始化成功，API 拦截已就绪，首页数据可正常加载');
    } catch (error) {
      console.error('MSW: 同步初始化失败:', error);
      // 重置状态，允许重试
      initializationPromise = null;
      throw error;
    }
  })();

  return initializationPromise;
}

// 页面加载前同步等待 MSW 初始化完成，确保首页能正常加载数据
export async function load() {
  try {
    // 确保 MSW 在页面渲染前完全初始化，保证首页数据加载正常
    await initializeMocks();
    console.log('MSW: 页面加载前初始化验证完成，首页数据加载已就绪');
  } catch (error) {
    console.error('MSW: 页面加载前初始化失败，首页数据加载可能受影响:', error);
    // 即使 MSW 初始化失败，也允许页面继续加载
    // 但会在控制台明确提示可能影响数据加载
  }

  return {};
}

export const prerender = true;
export const ssr = false;
