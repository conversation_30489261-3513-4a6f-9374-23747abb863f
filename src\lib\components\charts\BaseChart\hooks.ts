/**
 * 图表相关的 Svelte 钩子函数
 *
 * @category Hooks
 */

import { onDestroy, onMount } from 'svelte';
import { derived, type Writable, writable } from 'svelte/store';

import { chartUpdateManager } from '$lib/utils/chartUpdateManager';
import { createModuleLogger } from '$lib/utils/logger';

import type {
  ChartExportConfig,
  ChartHookReturn,
  ChartInteractionConfig,
  ChartPerformanceConfig,
  ChartState,
  ChartType,
} from './types';

const hookLogger = createModuleLogger('chart-hooks');

/**
 * 图表状态管理钩子
 */
export function useChartState(chartId: string): {
  state: Writable<ChartState>;
  updateState: (updates: Partial<ChartState>) => void;
  resetState: () => void;
} {
  const initialState: ChartState = {
    initialized: false,
    loading: false,
    hasError: false,
    lastUpdated: undefined,
    dataHash: undefined,
  };

  const state = writable<ChartState>(initialState);

  function updateState(updates: Partial<ChartState>) {
    state.update((current) => ({
      ...current,
      ...updates,
      lastUpdated: new Date(),
    }));
  }

  function resetState() {
    state.set(initialState);
  }

  return { state, updateState, resetState };
}

/**
 * 图表实例管理钩子
 */
export function useChart(
  chartType: ChartType,
  chartId: string,
  options: {
    enablePerformanceMonitoring?: boolean;
    enableAutoResize?: boolean;
    enableThemeWatch?: boolean;
  } = {}
): ChartHookReturn {
  const { state, updateState, resetState } = useChartState(chartId);

  let chartInstance: any = null; // echarts.ECharts
  let resizeObserver: ResizeObserver | null = null;
  let themeUnsubscribe: (() => void) | null = null;

  const {
    enablePerformanceMonitoring = true,
    enableAutoResize = true,
    enableThemeWatch = true,
  } = options;

  /**
   * 初始化图表
   */
  function initChart(container: HTMLElement): any {
    try {
      // 这里需要动态导入 echarts
      import('echarts').then((echarts) => {
        chartInstance = echarts.init(container);

        updateState({
          initialized: true,
          loading: false,
          hasError: false,
        });

        hookLogger.debug('Chart initialized', { chartType, chartId });
      });
    } catch (error) {
      const chartError = error instanceof Error ? error : new Error(String(error));
      updateState({
        hasError: true,
        error: chartError,
        loading: false,
      });
      hookLogger.error('Chart initialization failed', {
        chartType,
        chartId,
        error: chartError.message,
      });
    }

    return chartInstance;
  }

  /**
   * 强制更新图表
   */
  function forceUpdate(): void {
    if (chartInstance) {
      chartUpdateManager.forceUpdate(chartId);
    }
  }

  /**
   * 调整图表大小
   */
  function resize(): void {
    if (chartInstance) {
      chartInstance.resize();
    }
  }

  /**
   * 导出图表
   */
  async function exportChart(
    config: ChartExportConfig = {
      format: 'png',
      quality: 1,
      backgroundColor: '#fff',
      pixelRatio: 1,
    }
  ): Promise<string> {
    if (!chartInstance) {
      throw new Error('Chart not initialized');
    }

    try {
      const dataURL = chartInstance.getDataURL({
        type: config.format,
        pixelRatio: config.pixelRatio,
        backgroundColor: config.backgroundColor,
      });

      hookLogger.debug('Chart exported', { chartType, chartId, format: config.format });
      return dataURL;
    } catch (error) {
      const exportError = error instanceof Error ? error : new Error(String(error));
      hookLogger.error('Chart export failed', {
        chartType,
        chartId,
        error: exportError.message,
      });
      throw exportError;
    }
  }

  /**
   * 获取图表数据URL
   */
  function getDataURL(
    config: ChartExportConfig = {
      format: 'png',
      quality: 1,
      backgroundColor: '#fff',
      pixelRatio: 1,
    }
  ): string {
    if (!chartInstance) {
      throw new Error('Chart not initialized');
    }

    return chartInstance.getDataURL({
      type: config.format,
      pixelRatio: config.pixelRatio,
      backgroundColor: config.backgroundColor,
    });
  }

  /**
   * 设置自动调整大小
   */
  function setupAutoResize(container: HTMLElement): void {
    if (!enableAutoResize) return;

    // 窗口大小变化监听
    const handleResize = () => resize();
    window.addEventListener('resize', handleResize);

    // 容器大小变化监听
    if (typeof ResizeObserver !== 'undefined') {
      resizeObserver = new ResizeObserver(() => {
        resize();
      });
      resizeObserver.observe(container);
    }

    onDestroy(() => {
      window.removeEventListener('resize', handleResize);
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
    });
  }

  /**
   * 设置主题监听
   */
  function setupThemeWatch(): void {
    if (!enableThemeWatch) return;

    // 这里需要导入主题监听函数
    import('$lib/utils/chartColors').then(({ createThemeObserver }) => {
      themeUnsubscribe = createThemeObserver(() => {
        if (chartInstance) {
          forceUpdate();
        }
      });
    });

    onDestroy(() => {
      if (themeUnsubscribe) {
        themeUnsubscribe();
      }
    });
  }

  /**
   * 清理资源
   */
  function cleanup(): void {
    if (chartInstance) {
      chartInstance.dispose();
      chartInstance = null;
    }

    chartUpdateManager.cancelUpdate(chartId);
    resetState();

    hookLogger.debug('Chart cleaned up', { chartType, chartId });
  }

  // 生命周期管理
  onDestroy(cleanup);

  return {
    chart: chartInstance,
    state,
    forceUpdate,
    resize,
    exportChart,
    getDataURL,
  };
}

/**
 * 图表性能监控钩子
 */
export function useChartPerformance(chartId: string) {
  const performanceMetrics = writable<{
    renderTime: number[];
    updateTime: number[];
    memoryUsage: number[];
  }>({
    renderTime: [],
    updateTime: [],
    memoryUsage: [],
  });

  const averageMetrics = derived(performanceMetrics, ($metrics) => ({
    avgRenderTime:
      $metrics.renderTime.length > 0
        ? $metrics.renderTime.reduce((a, b) => a + b, 0) / $metrics.renderTime.length
        : 0,
    avgUpdateTime:
      $metrics.updateTime.length > 0
        ? $metrics.updateTime.reduce((a, b) => a + b, 0) / $metrics.updateTime.length
        : 0,
    avgMemoryUsage:
      $metrics.memoryUsage.length > 0
        ? $metrics.memoryUsage.reduce((a, b) => a + b, 0) / $metrics.memoryUsage.length
        : 0,
  }));

  function recordRenderTime(time: number): void {
    performanceMetrics.update((metrics) => ({
      ...metrics,
      renderTime: [...metrics.renderTime.slice(-49), time], // 保留最近50次记录
    }));
  }

  function recordUpdateTime(time: number): void {
    performanceMetrics.update((metrics) => ({
      ...metrics,
      updateTime: [...metrics.updateTime.slice(-49), time],
    }));
  }

  function recordMemoryUsage(): void {
    if (typeof performance !== 'undefined' && 'memory' in performance) {
      const memory = (performance as any).memory;
      const usage = memory.usedJSHeapSize / 1024 / 1024; // MB

      performanceMetrics.update((metrics) => ({
        ...metrics,
        memoryUsage: [...metrics.memoryUsage.slice(-49), usage],
      }));
    }
  }

  function getPerformanceReport(): any {
    return chartUpdateManager.getPerformanceStats(chartId);
  }

  return {
    performanceMetrics,
    averageMetrics,
    recordRenderTime,
    recordUpdateTime,
    recordMemoryUsage,
    getPerformanceReport,
  };
}

/**
 * 图表交互钩子
 */
export function useChartInteraction(chartInstance: any, config: ChartInteractionConfig) {
  const interactions = writable<{
    isZooming: boolean;
    isBrushing: boolean;
    selectedData: any[];
  }>({
    isZooming: false,
    isBrushing: false,
    selectedData: [],
  });

  function enableZoom(): void {
    if (!chartInstance || !config.enableZoom) return;

    chartInstance.setOption({
      dataZoom: [
        {
          type: 'inside',
          start: 0,
          end: 100,
        },
        {
          type: 'slider',
          start: 0,
          end: 100,
        },
      ],
    });
  }

  function enableBrush(): void {
    if (!chartInstance || !config.enableBrush) return;

    chartInstance.setOption({
      brush: {
        toolbox: ['rect', 'polygon', 'lineX', 'lineY', 'keep', 'clear'],
      },
    });

    chartInstance.on('brushSelected', (params: any) => {
      interactions.update((state) => ({
        ...state,
        selectedData: params.batch[0].selected,
      }));
    });
  }

  function resetInteractions(): void {
    if (!chartInstance) return;

    chartInstance.dispatchAction({
      type: 'dataZoom',
      start: 0,
      end: 100,
    });

    chartInstance.dispatchAction({
      type: 'brush',
      command: 'clear',
    });

    interactions.update((state) => ({
      ...state,
      selectedData: [],
    }));
  }

  return {
    interactions,
    enableZoom,
    enableBrush,
    resetInteractions,
  };
}
