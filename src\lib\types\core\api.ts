/**
 * 核心 API 相关类型定义
 *
 * @category Core Types
 */

/**
 * 统一的 API 响应格式
 *
 * @template T 响应数据类型
 */
export interface ApiResponse<T = any> {
  /** 响应数据 */
  data: T;
  /** 响应状态 */
  status: 'success' | 'error';
  /** 响应消息 */
  message?: string;
  /** 错误信息（仅在 status 为 error 时存在） */
  error?: string;
}

/**
 * API 错误信息
 */
export interface ApiErrorInfo {
  /** 错误代码 */
  code: string;
  /** 错误消息 */
  message: string;
  /** 详细信息 */
  details?: unknown;
}

/**
 * API 请求配置
 */
export interface ApiRequestConfig {
  /** 请求超时时间（毫秒） */
  timeout?: number;
  /** 重试次数 */
  retries?: number;
  /** 是否显示加载状态 */
  showLoading?: boolean;
  /** 是否显示错误提示 */
  showError?: boolean;
}

/**
 * API 响应状态类型
 */
export type ApiStatus = 'idle' | 'loading' | 'success' | 'error';

/**
 * 基础查询参数接口
 */
export interface BaseQueryParams {
  /** 开始日期 */
  startDate?: string;
  /** 结束日期 */
  endDate?: string;
  /** 额外过滤条件 */
  filters?: Record<string, unknown>;
}
