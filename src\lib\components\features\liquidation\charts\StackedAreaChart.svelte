<!-- src/lib/components/liquidation/charts/StackedAreaChart.svelte -->
<script lang="ts">
  import type { EChartsOption } from 'echarts';
  import * as echarts from 'echarts';
  import { createEventDispatcher, onDestroy, onMount } from 'svelte';

  import type { LiquidationData } from '$lib/types';
  import { formatDate, formatNumber } from '$lib/utils/formatters';

  const dispatch = createEventDispatcher();

  export let options: EChartsOption = {};
  export let data: LiquidationData[] = [];
  export let title: string = '多/空总清算金额(USD)趋势';
  export let subtitle: string = '';

  let chartDom: HTMLDivElement;
  let myChart: echarts.ECharts;

  // 处理数据，按时间和分组维度聚合
  function processData(data: LiquidationData[]): {
    times: string[];
    series: { name: string; data: number[] }[];
  } {
    if (!data || data.length === 0) {
      return { times: [], series: [] };
    }

    const timeFormat = data.length > 50 ? 'date' : 'datetime';
    const timeMap = new Map<string, Map<string, number>>();

    // Define all possible combined group values
    const groupValues = new Set<string>();
    const coinTypes = new Set<string>();
    data.forEach((item) => coinTypes.add(item.coinType));

    // Generate all combinations of coinType and side
    coinTypes.forEach((coinType) => {
      groupValues.add(`${coinType}-多头`);
      groupValues.add(`${coinType}-空头`);
    });

    data.forEach((item) => {
      const timeKey = formatDate(item.datetime, timeFormat);
      const sideLabel = item.side === 1 ? '多头' : '空头';
      const groupKey = `${item.coinType}-${sideLabel}`;

      if (!timeMap.has(timeKey)) {
        timeMap.set(timeKey, new Map<string, number>());
      }

      const groupMap = timeMap.get(timeKey)!;
      groupMap.set(groupKey, (groupMap.get(groupKey) || 0) + item.amountUsd);
    });

    const times = Array.from(timeMap.keys()).sort();

    const allGroupValues = Array.from(groupValues).sort(); // Sort to ensure consistent legend order
    const series = allGroupValues.map((groupValue) => {
      const seriesData = times.map((time) => {
        const groupMap = timeMap.get(time)!;
        return groupMap.get(groupValue) || 0;
      });

      return {
        name: groupValue,
        data: seriesData,
      };
    });

    return { times, series };
  }

  const baseChartOptions: EChartsOption = {
    title: {
      text: title,
      subtext: subtitle,
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
      formatter: function (params: any) {
        let result = params[0].name + '<br/>';

        // 计算总和
        let total = 0;
        params.forEach((param: any) => {
          total += param.value;
        });

        // 显示各系列及总和
        params.forEach((param: any) => {
          const percent = ((param.value / total) * 100).toFixed(1);
          result += `${param.marker} ${param.seriesName}: $${formatNumber(param.value, 2)} (${percent}%)<br/>`;
        });

        result += `<b>总计: $${formatNumber(total, 2)}</b>`;
        return result;
      },
    },
    legend: {
      bottom: '0%',
      data: [],
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '16%',
      top: '15%',
      containLabel: true,
    },
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100,
        zoomOnMouseWheel: true,
        moveOnMouseMove: true,
        moveOnMouseWheel: false,
      },
      {
        type: 'slider',
        start: 0,
        end: 100,
        bottom: '2%',
        height: 20,
        handleIcon:
          'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23.1h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
        handleSize: '80%',
      },
    ],
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: [],
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: function (value: number) {
          if (value >= 1000000) {
            return (value / 1000000).toFixed(1) + 'M';
          } else if (value >= 1000) {
            return (value / 1000).toFixed(0) + 'K';
          }
          return value.toString();
        },
      },
    },
    series: [],
  };

  let chartInitialized = false;

  function initializeChart() {
    if (chartInitialized) return;
    myChart = echarts.init(chartDom);
    updateChart();
    chartInitialized = true;

    myChart.on('click', (params) => {
      dispatch('chartClick', {
        chartType: 'stackedArea',
        name: params.seriesName,
        value: params.value,
        dataIndex: params.dataIndex,
      });
    });
  }

  function updateChart() {
    if (!myChart) return;

    const processedData = processData(data);

    // 多头/空头颜色配置 (updated for combined series names)
    const colorMap: Record<string, string> = {
      '主流币-多头': '#1890ff',
      '主流币-空头': '#722ed1',
      '山寨币-多头': '#52c41a',
      '山寨币-空头': '#f5222d',
    };

    const seriesConfig = processedData.series.map((s) => {
      return {
        name: s.name,
        type: 'line',
        stack: 'Total',
        areaStyle: {},
        emphasis: {
          focus: 'series',
        },
        data: s.data,
        color: colorMap[s.name as keyof typeof colorMap],
        smooth: true,
      };
    });

    myChart.setOption(
      {
        ...baseChartOptions,
        ...options,
        title: {
          ...baseChartOptions.title,
          text: title,
          subtext: subtitle,
        },
        xAxis: {
          ...baseChartOptions.xAxis,
          data: processedData.times,
        },
        legend: {
          ...baseChartOptions.legend,
          data: processedData.series.map((s) => s.name),
        },
        series: seriesConfig,
      },
      { notMerge: false }
    );
  }

  onMount(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            initializeChart();
            observer.unobserve(chartDom);
          }
        });
      },
      { threshold: 0.1 }
    );

    observer.observe(chartDom);

    const resizeChart = () => {
      if (myChart) {
        myChart.resize();
      }
    };

    window.addEventListener('resize', resizeChart);

    onDestroy(() => {
      window.removeEventListener('resize', resizeChart);
      if (myChart) {
        myChart.dispose();
      }
    });
  });

  $: if (myChart && chartInitialized && (data || options || title || subtitle)) {
    updateChart();
  }
</script>

<div
  bind:this={chartDom}
  style="width: 100%; height: 100%;"
  aria-label="{title} - {subtitle}"
></div>
