/**
 * API 相关常量定义
 *
 * @category Constants
 */

/**
 * API 端点常量
 *
 * 定义了应用中所有 API 端点的路径常量，便于统一管理和维护。
 *
 * @example
 * ```typescript
 * import { API_ENDPOINTS } from '$lib/constants/api';
 *
 * // 构建完整的 API URL
 * const url = `${API_CONFIG.BASE_URL}${API_ENDPOINTS.DASHBOARD_STATS}`;
 *
 * // 发起请求
 * const response = await fetch(url);
 * ```
 */
export const API_ENDPOINTS = {
  // 仪表板相关
  DASHBOARD_STATS: '/dashboard/stats',
  DASHBOARD_OVERVIEW: '/dashboard/overview',

  // 图表数据相关
  CHARTS_ALL: '/charts/all',
  CHARTS_BAR: '/charts/bar',
  CHARTS_LINE: '/charts/line',
  CHARTS_PIE: '/charts/pie',
  CHARTS_SCATTER: '/charts/scatter',

  // 数据查询相关
  DATA_QUERY: '/data/query',
  DATA_EXPORT: '/data/export',

  // 爆仓数据相关
  LIQUIDATION_SNAPSHOT: '/liquidation/snapshot',
  LIQUIDATION_TREND: '/liquidation/trend',
  LIQUIDATION_TREND_DETAILED: '/liquidation/trend-detailed',
} as const;

/**
 * API 配置常量
 *
 * @deprecated 请使用 appConfig.api 替代
 * @see {@link appConfig} from '$lib/config'
 */
export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_BASE_URL || '/api',
  TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
} as const;

export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
} as const;

export const CONTENT_TYPES = {
  JSON: 'application/json',
  FORM_DATA: 'multipart/form-data',
  URL_ENCODED: 'application/x-www-form-urlencoded',
  CSV: 'text/csv',
  EXCEL: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  PDF: 'application/pdf',
  XML: 'application/xml',
  TEXT: 'text/plain',
} as const;

/**
 * HTTP 方法常量
 */
export const HTTP_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  DELETE: 'DELETE',
  PATCH: 'PATCH',
  HEAD: 'HEAD',
  OPTIONS: 'OPTIONS',
} as const;

/**
 * API 错误代码
 */
export const API_ERROR_CODES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  ABORT_ERROR: 'ABORT_ERROR',
  PARSE_ERROR: 'PARSE_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  RATE_LIMIT_ERROR: 'RATE_LIMIT_ERROR',
  MAINTENANCE_ERROR: 'MAINTENANCE_ERROR',
} as const;

/**
 * 缓存策略常量
 */
export const CACHE_STRATEGIES = {
  NO_CACHE: 'no-cache',
  CACHE_FIRST: 'cache-first',
  NETWORK_FIRST: 'network-first',
  CACHE_ONLY: 'cache-only',
  NETWORK_ONLY: 'network-only',
  STALE_WHILE_REVALIDATE: 'stale-while-revalidate',
} as const;

/**
 * 请求头常量
 */
export const REQUEST_HEADERS = {
  AUTHORIZATION: 'Authorization',
  CONTENT_TYPE: 'Content-Type',
  ACCEPT: 'Accept',
  USER_AGENT: 'User-Agent',
  X_REQUESTED_WITH: 'X-Requested-With',
  X_API_KEY: 'X-API-Key',
  X_CLIENT_VERSION: 'X-Client-Version',
  X_REQUEST_ID: 'X-Request-ID',
} as const;
