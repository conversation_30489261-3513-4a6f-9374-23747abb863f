// 统一导出所有 stores
export * from './features';
export * from './ui';

// 便捷的 store 实例导出
export { dashboardStore } from './features/dashboard';
export { dataQueryStore } from './features/dataQuery';
export { liquidationStore } from './features/liquidation'; // 使用统一实现

// 向后兼容的导出（逐步废弃）
// @deprecated dashboard/store 已移除，请使用 features/dashboard 中的 dashboardStore
// @deprecated dataQuery/store 不存在，请使用 features/dataQuery 中的 dataQueryStore
// @deprecated liquidation 已统一到 liquidation/store 实现
