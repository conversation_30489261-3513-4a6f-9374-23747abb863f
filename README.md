# Svelte Demo - 数据可视化仪表板

一个基于 SvelteKit 构建的现代化数据可视化仪表板应用，展示了最佳的前端架构实践和代码组织模式。

> 🎉 **项目已完成全面重构**：采用了统一的服务层架构、规范化的状态管理和完整的类型安全设计。

## ✨ 特性

- 🎨 **现代化 UI**：使用 Tailwind CSS 构建的响应式界面
- 📊 **数据可视化**：集成 ECharts 的多种图表类型
- 🏗️ **优秀架构**：分层的组件和服务架构
- 🔄 **状态管理**：基于 Svelte stores 的响应式状态管理
- 🧪 **测试覆盖**：完整的单元测试和集成测试
- 🚀 **开发体验**：热重载、TypeScript、ESLint、Prettier
- 📱 **响应式设计**：支持桌面和移动设备

## 🛠️ 技术栈

### 核心框架

- **SvelteKit** - 全栈 Web 框架
- **TypeScript** - 类型安全的 JavaScript
- **Vite** - 快速的构建工具

### UI 和样式

- **Tailwind CSS** - 实用优先的 CSS 框架
- **ECharts** - 强大的数据可视化库
- **Svelte ECharts** - Svelte 的 ECharts 集成

### 开发工具

- **Vitest** - 快速的单元测试框架
- **Testing Library** - 简单而完整的测试工具
- **ESLint** - 代码质量检查
- **Prettier** - 代码格式化
- **MSW** - API 模拟服务
- **Pino** - 高性能结构化日志系统 🆕

## 📁 项目结构

```
src/
├── lib/
│   ├── components/          # 组件库
│   │   ├── ui/             # 基础 UI 组件 (Button, Input, Modal 等)
│   │   ├── charts/         # 图表组件 (ECharts 封装)
│   │   ├── layout/         # 布局组件 (Header, Sidebar, TabBar 等)
│   │   └── features/       # 业务功能组件 (Dashboard, DataQuery 等)
│   ├── services/           # 统一服务层 🆕
│   │   ├── api/           # API 基础服务
│   │   └── data/          # 数据服务 (dashboard, dataQuery, liquidation)
│   ├── stores/             # 状态管理
│   │   ├── ui/            # UI 状态 (sidebar, tabs)
│   │   └── features/      # 业务状态 (dashboard, dataQuery, liquidation)
│   ├── types/              # TypeScript 类型定义
│   ├── constants/          # 常量管理 🆕
│   │   ├── api.ts         # API 相关常量
│   │   ├── ui.ts          # UI 相关常量
│   │   ├── charts.ts      # 图表相关常量
│   │   └── time.ts        # 时间相关常量
│   ├── utils/              # 工具函数 🆕
│   │   ├── logger.ts      # 统一日志系统
│   │   ├── formatters.ts  # 数据格式化
│   │   ├── timezone.ts    # 时区处理
│   │   └── chartColors.ts # 图表颜色管理
│   └── index.ts           # 统一导出
├── routes/                 # SvelteKit 页面路由
├── tests/                  # 测试文件和配置
└── mocks/                  # MSW Mock 数据
```

### 🆕 重构亮点

- **统一服务层**：合并了重复的 API 和服务代码
- **规范化状态管理**：按功能模块组织 stores
- **常量管理**：集中管理配置和常量
- **统一日志系统**：基于 Pino 的结构化日志记录 🆕
- **类型安全**：完整的 TypeScript 类型定义
- **零重复**：消除了所有重复代码和文件

## 🚀 快速开始

### 环境要求

- Node.js 18.0+
- npm 或 yarn

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

应用将在 http://localhost:5174 启动

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 🧪 测试

### 运行所有测试

```bash
npm run test
```

### 运行测试并生成覆盖率报告

```bash
npm run test:coverage
```

### 监听模式运行测试

```bash
npm run test:watch
```

## 📊 功能模块

### 仪表板 (Dashboard)

- 实时数据统计卡片
- 多种图表类型展示
- 时间范围选择器
- 图表配置管理

### 数据查询 (Data Query)

- 灵活的查询表单
- 分页数据表格
- 导出功能
- 响应式设计

### 爆仓分析 (Liquidation)

- 专业的金融数据可视化
- 多维度数据分析
- 实时数据更新

### 主题切换 (Theme Switching) 🆕

- shadcn-svelte 标准主题管理
- 浅色/深色模式自动切换
- 用户偏好持久化存储
- ECharts 图表主题兼容

### 时区设置 (Timezone Settings) 🆕

- 全球主要时区支持 (20+ 时区)
- 智能时区选择器 (搜索、筛选、分组)
- 浏览器本地时区自动检测
- 时区感知的时间显示
- 用户偏好持久化存储
- 所有图表和数据的时区同步

### 设置 (Settings)

- 用户偏好配置
- 时区设置管理
- 通知和隐私设置

### 日志系统 (Logging System) 🆕

- **统一日志接口**：替换所有 console.\* 调用
- **结构化日志**：JSON 格式，便于分析和监控
- **多级别支持**：TRACE、DEBUG、INFO、WARN、ERROR、FATAL
- **环境适配**：开发环境美化输出，生产环境优化性能
- **模块化 Logger**：为不同模块提供专用日志记录器
- **配置灵活**：支持环境变量和运行时配置

## 🏗️ 架构设计

### 分层架构

#### 组件层 (Components)

- **UI 组件**：纯展示组件，无业务逻辑 (Button, Input, Modal)
- **图表组件**：ECharts 封装组件，支持多种图表类型
- **布局组件**：页面布局和导航 (Header, Sidebar, TabBar)
- **功能组件**：包含业务逻辑的组件 (Dashboard, DataQuery)

#### 服务层 (Services) 🆕

- **API 服务**：统一的 HTTP 请求封装和错误处理
- **数据服务**：业务数据获取和处理逻辑
  - `dashboardDataService` - 仪表板数据服务
  - `dataQueryService` - 数据查询服务
  - `liquidationDataService` - 爆仓数据服务

#### 状态管理层 (Stores)

- **UI 状态**：界面交互状态 (sidebar, tabs)
- **业务状态**：数据和业务逻辑状态 (dashboard, dataQuery, liquidation)
- **响应式更新**：基于 Svelte stores 的自动更新
- **派生状态**：自动计算的衍生状态

#### 类型系统 (Types)

- **完整类型定义**：所有接口和数据结构的类型定义
- **类型安全**：编译时类型检查，零运行时错误
- **智能提示**：完整的 IDE 支持和自动补全

#### 常量管理 (Constants) 🆕

- **API 常量**：接口地址、配置参数
- **UI 常量**：断点、层级、动画时长
- **图表常量**：颜色、配置、选项
- **时间常量**：时区、格式、间隔

#### 工具函数 (Utils) 🆕

- **日志系统**：统一的结构化日志记录
- **格式化工具**：数字、日期、时间格式化
- **时区处理**：时区转换和本地化
- **图表工具**：颜色管理和主题适配
- **性能监控**：操作耗时和资源使用监控

## 🔧 开发指南

### 添加新页面

1. 在 `src/routes/` 下创建新的路由文件
2. 创建对应的页面组件
3. 更新导航配置

### 添加新组件

1. 在适当的组件目录下创建组件文件夹
2. 创建组件文件、类型定义和测试
3. 在 `index.ts` 中导出组件

### 添加新数据服务 🆕

1. 在 `src/lib/services/data/` 下创建服务类
2. 实现数据获取和处理逻辑
3. 添加错误处理
4. 在 `src/lib/services/data/index.ts` 中导出

### 添加新状态管理 🆕

1. 在 `src/lib/stores/features/` 下创建 store 文件
2. 使用 `writable` 和 `derived` 创建响应式状态
3. 实现业务逻辑方法
4. 在 `src/lib/stores/features/index.ts` 中导出

### 添加新常量 🆕

1. 在 `src/lib/constants/` 下的相应文件中添加常量
2. 使用 `as const` 确保类型安全
3. 在 `src/lib/constants/index.ts` 中导出

### 使用日志系统 🆕

```typescript
import { logger, createModuleLogger } from '$lib/utils/logger';

// 基本使用
logger.info('应用启动');
logger.error('发生错误', { error: err });

// 模块专用 Logger
const moduleLogger = createModuleLogger('dashboard');
moduleLogger.debug('模块初始化完成');
```

详细文档请参考：[日志系统文档](./docs/logging.md)

## 📝 代码规范

### 命名规范

- **组件**：PascalCase (如 `UserProfile.svelte`)
- **文件夹**：camelCase (如 `userProfile/`)
- **变量和函数**：camelCase
- **常量**：UPPER_SNAKE_CASE

### 文件组织

- 每个组件使用文件夹结构
- 包含组件文件、类型定义、测试和导出文件
- 统一的 `index.ts` 导出模式

## 📚 文档

- [日志系统文档](./docs/logging.md) - 详细的日志系统使用指南
- [开发指南](./docs/development.md) - 项目开发规范和最佳实践
- [API 文档](./docs/api.md) - 接口文档和数据结构
- [组件文档](./docs/components.md) - 组件库使用说明

## 🔗 相关链接

- [SvelteKit 官方文档](https://kit.svelte.dev/)
- [Tailwind CSS 文档](https://tailwindcss.com/)
- [ECharts 文档](https://echarts.apache.org/)
- [Pino 日志库](https://getpino.io/)
- [Vitest 测试框架](https://vitest.dev/)

## 📄 许可证

MIT License
