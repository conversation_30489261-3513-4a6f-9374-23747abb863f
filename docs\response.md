# 响应体结构

## /api/data/query

交易所level

```json
{
  "code": 1000,
  "message": "success",
  "data": [
    {
      "id": "01JV70KA0VK0ESQ52AN5DX4M2Q",
      "marketEventType": "TWAP_DETECTION",
      "date": 1747213461,
      "parameters": [
        {
          "parameterId": "twapId",
          "value": "01JV704NG1T9SETKTD1GS64AWG"
        },
        {
          "parameterId": "amountUsd",
          "value": 5310.935100000001
        },
        {
          "parameterId": "threshold",
          "value": 1
        },
        {
          "parameterId": "side",
          "value": 2
        },
        {
          "parameterId": "market",
          "value": 2
        },
        {
          "parameterId": "duration",
          "value": 13
        },
        {
          "parameterId": "status",
          "value": "ongoing"
        },
        {
          "parameterId": "tradeId",
          "value": "01JV70K9VN6F9HQAWC52ASNR6Z"
        },
        {
          "parameterId": "tradeExecuted",
          "value": 1747213461000
        },
        {
          "parameterId": "tradePrice",
          "value": 0.25555
        },
        {
          "parameterId": "tradeSize",
          "value": 2890.9998043435726
        },
        {
          "parameterId": "tradeSide",
          "value": 2
        }
      ],
      "currency": "01J8QJG2T7RR1E2V0NFXFGF2RR"
    },
    {
      "id": "01JVHRTFGK1G87XEK7X8VXY1R0",
      "marketEventType": "TWAP_DETECTION",
      "date": 1747574209,
      "parameters": [
        {
          "parameterId": "twapId",
          "value": "01JVHRTFCZ1779E2PG5GYPHFKW"
        },
        {
          "parameterId": "amountUsd",
          "value": 4736.318579999999
        },
        {
          "parameterId": "threshold",
          "value": 1
        },
        {
          "parameterId": "side",
          "value": 2
        },
        {
          "parameterId": "market",
          "value": 1
        },
        {
          "parameterId": "duration",
          "value": 4
        },
        {
          "parameterId": "status",
          "value": "started"
        }
      ],
      "currency": "01HAF64YPQCN0VFY2D0SRQQ0MD"
    },
    {
      "id": "01JVHRVTE3NTSKD1R1XV727HGT",
      "marketEventType": "TWAP_DETECTION",
      "date": 1747574209,
      "parameters": [
        {
          "parameterId": "twapId",
          "value": "01JVHRMF6RH7X8D933Y9AR0D0P"
        },
        {
          "parameterId": "amountUsd",
          "value": 999.6936045999998
        },
        {
          "parameterId": "threshold",
          "value": 1
        },
        {
          "parameterId": "side",
          "value": 1
        },
        {
          "parameterId": "market",
          "value": 1
        },
        {
          "parameterId": "duration",
          "value": 7
        },
        {
          "parameterId": "status",
          "value": "expired"
        }
      ],
      "currency": "01HAF6501AXTMRSK1F836B8Q3F"
    },
    {
      "id": "01JSKY3X725QG8AN81YGTJV7PD",
      "marketEventType": "LIQUIDATION_ORDER",
      "currency": "01HWKTT6BZ7YKZZ545HTPD22N7",
      "date": 1745499583,
      "parameters": [
        {
          "parameterId": "side",
          "value": 1
        },
        {
          "parameterId": "exchange",
          "value": 4096
        },
        {
          "parameterId": "exchangeLabel",
          "value": "OKX"
        },
        {
          "parameterId": "threshold",
          "value": 2
        },
        {
          "parameterId": "base",
          "value": "FOXY"
        },
        {
          "parameterId": "pair",
          "value": "FOXY-USDT-SWAP"
        },
        {
          "parameterId": "priceUsd",
          "value": 0.001887
        },
        {
          "parameterId": "datetime",
          "value": "2025-04-24T12:59:42.539000"
        },
        {
          "parameterId": "amount",
          "value": 672000
        },
        {
          "parameterId": "dailyVolumeRatio",
          "value": 0.00041611941630712
        },
        {
          "parameterId": "amountUsd",
          "value": 1268.064
        }
      ]
    },
    {
      "id": "01JSKYXVDBFRBS8BZ4KWMYWJ3C",
      "marketEventType": "EXCHANGE_TRANSFER",
      "currency": "01HEPMMG27T0J8Y1RKW74XHXTM",
      "date": 1745500433,
      "parameters": [
        {
          "parameterId": "sender",
          "value": "******************************************"
        },
        {
          "parameterId": "recipient",
          "value": "******************************************"
        },
        {
          "parameterId": "threshold",
          "value": 0
        },
        {
          "parameterId": "isExchangeToExchange",
          "value": "False"
        },
        {
          "parameterId": "isDepositWalletToExchange",
          "value": "True"
        },
        {
          "parameterId": "exchange",
          "value": 512
        },
        {
          "parameterId": "exchangeLabel",
          "value": "KRAKEN"
        },
        {
          "parameterId": "base",
          "value": "BEAM"
        },
        {
          "parameterId": "fromLabel",
          "value": "User Deposit Wallet"
        },
        {
          "parameterId": "toLabel",
          "value": "Hot Wallet"
        },
        {
          "parameterId": "toName",
          "value": "KRAKEN"
        },
        {
          "parameterId": "fromName",
          "value": "KRAKEN"
        },
        {
          "parameterId": "toAddress",
          "value": "******************************************"
        },
        {
          "parameterId": "fromAddress",
          "value": "******************************************"
        },
        {
          "parameterId": "txId",
          "value": "0x125b8c1e69349abcad90af240c92a9b54ce8af5a95db7a699cc091c9dd0e937c"
        },
        {
          "parameterId": "exchangeSide",
          "value": 2
        },
        {
          "parameterId": "datetime",
          "value": "2025-04-24T13:09:59"
        },
        {
          "parameterId": "amount",
          "value": 1002727
        },
        {
          "parameterId": "amountUsd",
          "value": 6871.9
        },
        {
          "parameterId": "chain",
          "value": "Ethereum"
        }
      ]
    },
    {
      "id": "01JSKYG1QHVWE26XCVY3RKW8QX",
      "marketEventType": "ORDERBOOK_IMBALANCE",
      "currency": "01JP2G8RNR4JBJP81JS5JNJQ89",
      "date": 1745499981,
      "parameters": [
        {
          "parameterId": "deltaUsd",
          "value": 5322.7401721067945
        },
        {
          "parameterId": "side",
          "value": 1
        },
        {
          "parameterId": "threshold",
          "value": 0
        },
        {
          "parameterId": "variationPercent",
          "value": 11.01
        },
        {
          "parameterId": "bidsSumUsd",
          "value": 659821.6370916502
        },
        {
          "parameterId": "asksSumUsd",
          "value": 594357.6049655201
        },
        {
          "parameterId": "bidsSum",
          "value": 659821.6370916502
        },
        {
          "parameterId": "url",
          "value": ""
        },
        {
          "parameterId": "nbPairs",
          "value": 5
        },
        {
          "parameterId": "exchangeTopBidder",
          "value": 4
        },
        {
          "parameterId": "exchangeTopAsker",
          "value": 512
        },
        {
          "parameterId": "exchangeLabelTopBidder",
          "value": "BITGET"
        },
        {
          "parameterId": "exchangeLabelTopAsker",
          "value": "KRAKEN"
        },
        {
          "parameterId": "range",
          "value": 30
        },
        {
          "parameterId": "exchange",
          "value": -1
        },
        {
          "parameterId": "priceUsd",
          "value": 0.08130785714285714
        },
        {
          "parameterId": "asksSum",
          "value": 594357.6049655201
        },
        {
          "parameterId": "aggregated",
          "value": true
        },
        {
          "parameterId": "base",
          "value": "BMT"
        },
        {
          "parameterId": "datetime",
          "value": "2025-04-24T13:05:47"
        }
      ]
    },
    {
      "id": "01JSKSN7BKE30QERCZ0FFNR97H",
      "marketEventType": "FUNDING_RATE_SWITCH",
      "currency": "01HAF64ZT94SKECAMZ9C8TD0J1",
      "date": 1745494908,
      "parameters": [
        {
          "parameterId": "threshold",
          "value": 0
        },
        {
          "parameterId": "exchangeLabel",
          "value": "OKX FUTURES"
        },
        {
          "parameterId": "side",
          "value": 2
        },
        {
          "parameterId": "exchange",
          "value": 4096
        },
        {
          "parameterId": "wasSince",
          "value": "2025-04-20 17:41"
        },
        {
          "parameterId": "daysSince",
          "value": 3
        },
        {
          "parameterId": "datetime",
          "value": "2025-04-24T11:41:48.275561+00:00"
        },
        {
          "parameterId": "pair",
          "value": "ENJ/USDT:USDT"
        },
        {
          "parameterId": "base",
          "value": "ENJ"
        },
        {
          "parameterId": "previousDailyFundingRate",
          "value": -0.05223
        },
        {
          "parameterId": "dailyFundingRate",
          "value": 0.02847
        },
        {
          "parameterId": "aggregated",
          "value": false
        },
        {
          "parameterId": "fundingRate",
          "value": 0.0011863
        },
        {
          "parameterId": "previousFundingRate",
          "value": -0.0021762
        },
        {
          "parameterId": "fundingRateDiff",
          "value": 0.0033625
        }
      ]
    },
    {
      "id": "01JSM0JMSAAST7BHZW7E02FV7V",
      "marketEventType": "VOLUME_INCREASE",
      "currency": "01HCVQSJ0DG9QV5YNV9NP69KQ6",
      "date": 1745502163,
      "parameters": [
        {
          "parameterId": "datetime",
          "value": "2025-04-24T13:42:43"
        },
        {
          "parameterId": "priceUsd",
          "value": 0.07534
        },
        {
          "parameterId": "previousPriceUsd",
          "value": 0.07376
        },
        {
          "parameterId": "exchange",
          "value": 32
        },
        {
          "parameterId": "exchangeLabel",
          "value": "COINBASE"
        },
        {
          "parameterId": "base",
          "value": "BIGTIME"
        },
        {
          "parameterId": "pair",
          "value": "BIGTIME/USDC"
        },
        {
          "parameterId": "threshold",
          "value": 2
        },
        {
          "parameterId": "aggregated",
          "value": false
        },
        {
          "parameterId": "priceDirection",
          "value": 1
        },
        {
          "parameterId": "mean",
          "value": 1427.6080079220778
        },
        {
          "parameterId": "recentVolumeSumUsd",
          "value": 67178.22341
        },
        {
          "parameterId": "percentAbnormal",
          "value": 672.24
        },
        {
          "parameterId": "isSpot",
          "value": 1
        },
        {
          "parameterId": "isDerivatives",
          "value": 0
        }
      ]
    },
    {
      "id": "01JSM0HHRQVJZ2MR50TG7XFK4B",
      "marketEventType": "EXTREME_FUNDING_RATE",
      "currency": "01HAF64ZYT42GP9AK8MY45CK7T",
      "date": 1745502127,
      "parameters": [
        {
          "parameterId": "base",
          "value": "XDC"
        },
        {
          "parameterId": "pair",
          "value": "XDC/USDT:USDT"
        },
        {
          "parameterId": "side",
          "value": 1
        },
        {
          "parameterId": "datetime",
          "value": "2025-04-24T13:42:07.895674+00:00"
        },
        {
          "parameterId": "exchange",
          "value": 4
        },
        {
          "parameterId": "wasSince",
          "value": "2025-04-24 13:42"
        },
        {
          "parameterId": "daysSince",
          "value": 0
        },
        {
          "parameterId": "threshold",
          "value": -1
        },
        {
          "parameterId": "aggregated",
          "value": false
        },
        {
          "parameterId": "fundingRate",
          "value": 0.0039
        },
        {
          "parameterId": "exchangeLabel",
          "value": "BITGET FUTURES"
        },
        {
          "parameterId": "dailyFundingRate",
          "value": 0.0936
        },
        {
          "parameterId": "previousFundingRate",
          "value": 0.00125
        },
        {
          "parameterId": "previousDailyFundingRate",
          "value": 0.03
        }
      ]
    },
    {
      "id": "01JTZ8PF84P6A1WM2585NHWX9B",
      "marketEventType": "OPEN_INTEREST_VARIATION",
      "currency": "01JC303X64R02BZP8P6CZD1BCE",
      "date": 1746953518,
      "parameters": [
        {
          "parameterId": "base",
          "value": "SWELL"
        },
        {
          "parameterId": "pair",
          "value": "SWELL/USDT:USDT"
        },
        {
          "parameterId": "datetime",
          "value": "2025-05-11T08:51:58.333439"
        },
        {
          "parameterId": "exchange",
          "value": 4
        },
        {
          "parameterId": "priceUsd",
          "value": 0.01204
        },
        {
          "parameterId": "direction",
          "value": 2
        },
        {
          "parameterId": "threshold",
          "value": 0
        },
        {
          "parameterId": "timeframe",
          "value": "15m"
        },
        {
          "parameterId": "aggregated",
          "value": false
        },
        {
          "parameterId": "oiVariation",
          "value": -4411074
        },
        {
          "parameterId": "absVariation",
          "value": 2.73
        },
        {
          "parameterId": "exchangeLabel",
          "value": "BITGET FUTURES"
        },
        {
          "parameterId": "oiVariationUsd",
          "value": -53109.33
        },
        {
          "parameterId": "currentOiAmount",
          "value": 157232051
        },
        {
          "parameterId": "previousOiAmount",
          "value": 161643125
        },
        {
          "parameterId": "variationPercent",
          "value": -2.73
        },
        {
          "parameterId": "currentOiValueUsd",
          "value": 1893073.89
        }
      ]
    }
  ],
}
```

跨交易所聚合

```json
{
  "code": 1000,
  "message": "success",
  "data": [
    {
      "id": "01JSKZ2CZCXCPZS1B62VFBD58G",
      "marketEventType": "VOLUME_INCREASE",
      "currency": "01HAF64N8KNV6EXBZVV0NG0X1F",
      "date": 1745500582,
      "parameters": [
        {
          "parameterId": "datetime",
          "value": "2025-04-24T13:16:22"
        },
        {
          "parameterId": "priceUsd",
          "value": 0.06718025066499402
        },
        {
          "parameterId": "base",
          "value": "RSS3"
        },
        {
          "parameterId": "threshold",
          "value": 2
        },
        {
          "parameterId": "priceDirection",
          "value": 1
        },
        {
          "parameterId": "previousPriceUsd",
          "value": 0.06068863737208303
        },
        {
          "parameterId": "exchangeLabels",
          "value": "Bithumb, Okx, Bitget"
        },
        {
          "parameterId": "exchange",
          "value": -1
        },
        {
          "parameterId": "aggregated",
          "value": true
        },
        {
          "parameterId": "recentVolumeSumUsd",
          "value": 465007.***********
        },
        {
          "parameterId": "percentAbnormal",
          "value": 819.01
        },
        {
          "parameterId": "isSpot",
          "value": 1
        },
        {
          "parameterId": "isDerivatives",
          "value": 0
        }
      ]
    },
    {
      "id": "01JSM0HGZFVETRTG20P63QCGEN",
      "marketEventType": "FUNDING_RATE_SWITCH",
      "currency": "01HAF64KYQHPHH7284QXCH9YV2",
      "date": 1745502127,
      "parameters": [
        {
          "parameterId": "threshold",
          "value": 4
        },
        {
          "parameterId": "side",
          "value": 1
        },
        {
          "parameterId": "base",
          "value": "PYR"
        },
        {
          "parameterId": "datetime",
          "value": "2025-04-24T13:42:07.087755+00:00"
        },
        {
          "parameterId": "wasSince",
          "value": "2024-02-21 12:40"
        },
        {
          "parameterId": "daysSince",
          "value": 428
        },
        {
          "parameterId": "aggregated",
          "value": true
        },
        {
          "parameterId": "exchange",
          "value": -1
        },
        {
          "parameterId": "exchangeLabels",
          "value": "Bitget futures, Bybit futures"
        },
        {
          "parameterId": "previousDailyFundingRate",
          "value": 0.03
        },
        {
          "parameterId": "dailyFundingRate",
          "value": -0.39283
        },
        {
          "parameterId": "fundingRate",
          "value": -0.0163681
        },
        {
          "parameterId": "fundingRateDiff",
          "value": -0.0176181
        },
        {
          "parameterId": "previousFundingRate",
          "value": 0.00125
        }
      ]
    },
    {
      "id": "01JSM0HF0YCFFVCWGQD1Q8SQ5N",
      "marketEventType": "EXTREME_FUNDING_RATE",
      "currency": "01JQXD3TXJ59B5V981BE3M5272",
      "date": 1745502125,
      "parameters": [
        {
          "parameterId": "base",
          "value": "STO"
        },
        {
          "parameterId": "side",
          "value": 2
        },
        {
          "parameterId": "datetime",
          "value": "2025-04-24T13:42:05.085861+00:00"
        },
        {
          "parameterId": "wasSince",
          "value": "2025-04-24 09:44"
        },
        {
          "parameterId": "daysSince",
          "value": 0
        },
        {
          "parameterId": "threshold",
          "value": -1
        },
        {
          "parameterId": "aggregated",
          "value": true
        },
        {
          "parameterId": "fundingRate",
          "value": 0.0060725
        },
        {
          "parameterId": "exchangeLabels",
          "value": "Bitget futures, Binance futures, Bybit futures"
        },
        {
          "parameterId": "dailyFundingRate",
          "value": 0.14574
        },
        {
          "parameterId": "previousFundingRate",
          "value": 0.006424166666666667
        },
        {
          "parameterId": "previousDailyFundingRate",
          "value": 0.15418
        }
      ]
    },
    {
      "id": "01JTZB3QWR8EX3Q48VH8MCJ41Q",
      "marketEventType": "OPEN_INTEREST_VARIATION",
      "currency": "01HAF64YQ9K0DSEVH4B7MBJN1V",
      "date": 1746956050,
      "parameters": [
        {
          "parameterId": "base",
          "value": "TON"
        },
        {
          "parameterId": "datetime",
          "value": "2025-05-11T09:34:10.205903"
        },
        {
          "parameterId": "exchange",
          "value": -1
        },
        {
          "parameterId": "priceUsd",
          "value": 3.3838
        },
        {
          "parameterId": "direction",
          "value": 1
        },
        {
          "parameterId": "threshold",
          "value": 0
        },
        {
          "parameterId": "timeframe",
          "value": "15m"
        },
        {
          "parameterId": "aggregated",
          "value": true
        },
        {
          "parameterId": "oiVariation",
          "value": 1207264.03
        },
        {
          "parameterId": "absVariation",
          "value": 2.59
        },
        {
          "parameterId": "oiVariationUsd",
          "value": 4085140.02
        },
        {
          "parameterId": "currentOiAmount",
          "value": 47771124.48
        },
        {
          "parameterId": "exchangeLabelsUp",
          "value": "Binance futures, Bybit futures, Okx futures, Bitget futures"
        },
        {
          "parameterId": "previousOiAmount",
          "value": 46563860.45
        },
        {
          "parameterId": "variationPercent",
          "value": 2.59
        },
        {
          "parameterId": "currentOiValueUsd",
          "value": 161647931.02
        },
        {
          "parameterId": "exchangeLabelsDown",
          "value": "Bybit futures, Okx futures"
        }
      ]
    }
  ]
}
```
