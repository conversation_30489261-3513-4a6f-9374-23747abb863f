<script lang="ts">
  import { CurrencyIcon } from '$lib/components/features/ui';
  import { Badge } from '$lib/components/ui/badge';
  import { Card, CardContent } from '$lib/components/ui/card';

  // 本地实现 getParameterValue 函数
  function getParameterValue(
    event: any,
    parameterId: string
  ): string | number | boolean | undefined {
    const parameter = event.parameters.find((p: any) => p.parameterId === parameterId);
    return parameter?.value;
  }

  import { type BaseEventCardProps, formatUsdAmount, formatPercentage, formatPrice } from '../utils';

  interface Props extends BaseEventCardProps {}

  const { event, isHighlighted = false, onCardClick }: Props = $props();

  // 提取订单簿失衡相关参数
  const deltaUsd = getParameterValue(event, 'deltaUsd') as number;
  const side = getParameterValue(event, 'side') as number;
  const variationPercent = getParameterValue(event, 'variationPercent') as number;
  const bidsSumUsd = getParameterValue(event, 'bidsSumUsd') as number;
  const asksSumUsd = getParameterValue(event, 'asksSumUsd') as number;
  const priceUsd = getParameterValue(event, 'priceUsd') as number;
  const base = getParameterValue(event, 'base') as string;
  const datetime = getParameterValue(event, 'datetime') as string;
  const currencyId = event.currency;

  // 获取失衡方向和描述
  const isMoreAsks = side === 2; // side 2 表示卖盘优势
  const imbalanceDirection = isMoreAsks
    ? 'more aggregated asks than bids'
    : 'more aggregated bids than asks';
  const imbalanceBadgeVariant = isMoreAsks ? 'destructive' : 'default';

  // 格式化时间显示为 HH:mm 格式
  function formatTime(dateTimeStr: string): string {
    try {
      const eventTime = new Date(dateTimeStr);
      const hours = eventTime.getHours().toString().padStart(2, '0');
      const minutes = eventTime.getMinutes().toString().padStart(2, '0');
      return `${hours}:${minutes}`;
    } catch {
      return '';
    }
  }

  // 卡片样式 - 订单簿失衡 (ORDERBOOK_IMBALANCE): 18rem - 网格布局+比例条
  const cardClasses = [
    'w-full transition-all duration-200',
    'min-w-72',
    isHighlighted ? 'ring-2 ring-primary border-primary/50 shadow-lg' : 'hover:shadow-md',
    onCardClick ? 'cursor-pointer' : '',
  ]
    .filter(Boolean)
    .join(' ');

  // 处理卡片点击
  function handleCardClick() {
    onCardClick?.(event);
  }
</script>

<Card
  class={cardClasses}
  onclick={onCardClick ? handleCardClick : undefined}
  role={onCardClick ? 'button' : undefined}
  tabindex={onCardClick ? 0 : undefined}
>
  <CardContent class="space-y-2 p-3">
    <!-- 顶部：币种信息 -->
    <div class="flex items-center gap-2">
      <!-- 货币图标 -->
      <CurrencyIcon {currencyId} symbol={base} size="size-5" />

      <!-- 币种符号 -->
      <span class="text-foreground text-sm font-bold">
        {(base || '未知').toUpperCase()}
      </span>

      <!-- 时间 -->
      <span class="text-muted-foreground text-xs">
        {formatTime(datetime)}
      </span>
    </div>

    <!-- 中间：失衡信息 -->
    <div class="text-muted-foreground text-xs">
      <span class="font-bold">{(base || '').toUpperCase()}</span> has
      <Badge variant={imbalanceBadgeVariant} class="mx-1 text-xs">
        {formatPercentage(variationPercent)}
      </Badge>
      {imbalanceDirection}
    </div>

    <!-- 底部：买盘、价格、卖盘并排显示 -->
    <div class="grid grid-cols-3 gap-2 text-sm">
      <!-- 聚合买盘 -->
      <div class="space-y-1 text-left">
        <div class="text-muted-foreground text-xs">Aggregated bids</div>
        <div class="font-semibold text-green-600 dark:text-green-400">
          {formatUsdAmount(bidsSumUsd || 0)}
        </div>
      </div>

      <!-- 价格 -->
      <div class="space-y-1 text-center">
        <div class="text-muted-foreground text-xs">Price</div>
        <div class="text-foreground font-semibold">
          ${formatPrice(priceUsd)}
        </div>
      </div>

      <!-- 聚合卖盘 -->
      <div class="space-y-1 text-right">
        <div class="text-muted-foreground text-xs">Aggregated asks</div>
        <div class="font-semibold text-red-600 dark:text-red-400">
          {formatUsdAmount(asksSumUsd || 0)}
        </div>
      </div>
    </div>

    <!-- 买卖盘比例条 -->
    {#if bidsSumUsd && asksSumUsd}
      {@const totalSum = bidsSumUsd + asksSumUsd}
      {@const bidsPercentage = (bidsSumUsd / totalSum) * 100}
      {@const asksPercentage = (asksSumUsd / totalSum) * 100}

      <div class="bg-muted flex h-1 overflow-hidden rounded-full">
        <!-- 买盘部分 -->
        <div
          class="bg-green-500 transition-all duration-300 dark:bg-green-400"
          style="width: {bidsPercentage}%"
          title="Bids: {bidsPercentage.toFixed(1)}%"
        ></div>
        <!-- 卖盘部分 -->
        <div
          class="bg-red-500 transition-all duration-300 dark:bg-red-400"
          style="width: {asksPercentage}%"
          title="Asks: {asksPercentage.toFixed(1)}%"
        ></div>
      </div>
    {/if}
  </CardContent>
</Card>
