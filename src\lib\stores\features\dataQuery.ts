// src/lib/stores/features/dataQuery.ts
import { derived, writable } from 'svelte/store';

import { dataQueryService } from '$lib/services/data/dataQuery';
import type {
  MarketEvent,
  MarketEventType,
  QueryMode,
  AdvancedQueryParams
} from '$lib/types/business/market';
import type { PaginationState, QueryResults } from '$lib/types';

// 基础查询参数类型（原有的查询方式）
interface BasicQueryParams {
  base: string[]; // 币种名称列表，对应API的base参数
  date: string; // 查询日期 (UTC)，格式：yyyy-MM-dd
  marketEventType: MarketEventType[]; // 市场事件类型列表
  threshold?: number; // 阈值，枚举值：0, 1, 2, 4
}

// 统一查询参数类型（支持基础和高级查询）
interface QueryParams {
  queryMode: QueryMode; // 查询模式：basic | advanced
  basic: BasicQueryParams; // 基础查询参数
  advanced: AdvancedQueryParams; // 高级查询参数
}

// 初始基础查询参数
const initialBasicQueryParams: BasicQueryParams = {
  base: [], // 币种名称列表
  date: '', // 查询日期，格式：yyyy-MM-dd
  marketEventType: [], // 市场事件类型列表
  threshold: undefined, // 阈值，可选参数
};

// 初始高级查询参数
const initialAdvancedQueryParams: AdvancedQueryParams = {
  date: '', // 查询日期，格式：yyyy-MM-dd
  base: [], // 币种名称列表
  filters: [], // 过滤条件数组
};

// 初始查询参数
const initialQueryParams: QueryParams = {
  queryMode: 'basic', // 默认使用基础查询模式
  basic: initialBasicQueryParams,
  advanced: initialAdvancedQueryParams,
};

// 初始分页设置（用于无限滚动）
const initialPagination: PaginationState = {
  currentPage: 1,
  itemsPerPage: 20, // 增加每页数量，适合无限滚动
  totalItems: 0,
  totalPages: 0,
  hasPrevious: false,
  hasNext: false,
};

/**
 * 数据查询状态管理
 */
function createDataQueryStore() {
  const { subscribe, update, set } = writable({
    queryParams: initialQueryParams,
    results: {
      items: [] as MarketEvent[], // 使用新的MarketEvent类型
      loading: false,
      pagination: initialPagination,
    } as QueryResults,
    isFilterExpanded: true,
    error: null as string | null,
    // 无限滚动相关状态（保留但可能不再使用，因为API返回所有数据）
    hasMore: false, // 默认为false，因为API返回所有数据
    loadingMore: false, // 是否正在加载更多数据
    // 时间轴扩展相关状态
    timelineExtendable: true, // 是否可以扩展时间轴（向前加载更多天数）
    loadingTimeline: false, // 是否正在加载时间轴数据
    loadedDays: 0, // 已加载的天数（除了初始查询日期）
    maxLoadDays: 7, // 最大可加载天数
  });

  // 派生 stores
  const queryParams = derived({ subscribe }, ($state) => $state.queryParams);
  const results = derived({ subscribe }, ($state) => $state.results);
  const pagination = derived({ subscribe }, ($state) => $state.results.pagination);
  const loading = derived({ subscribe }, ($state) => $state.results.loading);
  const error = derived({ subscribe }, ($state) => $state.error);
  const isFilterExpanded = derived({ subscribe }, ($state) => $state.isFilterExpanded);
  const hasMore = derived({ subscribe }, ($state) => $state.hasMore);
  const loadingMore = derived({ subscribe }, ($state) => $state.loadingMore);
  const timelineExtendable = derived({ subscribe }, ($state) => $state.timelineExtendable);
  const loadingTimeline = derived({ subscribe }, ($state) => $state.loadingTimeline);
  const loadedDays = derived({ subscribe }, ($state) => $state.loadedDays);

  return {
    subscribe,
    queryParams,
    results,
    pagination,
    loading,
    error,
    isFilterExpanded,
    hasMore,
    loadingMore,
    timelineExtendable,
    loadingTimeline,
    loadedDays,

    // 执行查询（获取所有符合条件的数据）
    async executeQuery() {
      let currentState: any;
      update((state) => {
        currentState = state;
        return {
          ...state,
          results: {
            ...state.results,
            loading: true,
          },
          error: null,
          hasMore: false, // API返回所有数据，无需分页
          loadingMore: false,
        };
      });

      try {
        let queryData;

        if (currentState.queryParams.queryMode === 'basic') {
          // 基础查询模式
          const basicParams = currentState.queryParams.basic;

          // 验证基础查询必需参数
          if (!basicParams.base || basicParams.base.length === 0) {
            throw new Error('请选择至少一个币种');
          }
          if (!basicParams.date) {
            throw new Error('请选择查询日期');
          }
          if (!basicParams.marketEventType || basicParams.marketEventType.length === 0) {
            throw new Error('请选择至少一个事件类型');
          }

          queryData = await dataQueryService.queryData(basicParams);
        } else {
          // 高级查询模式
          const advancedParams = currentState.queryParams.advanced;

          // 验证高级查询必需参数
          if (!advancedParams.base || advancedParams.base.length === 0) {
            throw new Error('请选择至少一个币种');
          }
          if (!advancedParams.date) {
            throw new Error('请选择查询日期');
          }
          if (!advancedParams.filters || advancedParams.filters.length === 0) {
            throw new Error('请添加至少一个过滤条件');
          }

          queryData = await dataQueryService.queryAdvancedData(advancedParams);
        }

        update((state) => {
          return {
            ...state,
            results: {
              items: queryData.items,
              loading: false,
              pagination: {
                ...state.results.pagination,
                currentPage: 1,
                totalItems: queryData.total,
                totalPages: 1, // 只有一页，包含所有数据
              },
            },
            hasMore: false, // 所有数据已加载
            // 重置时间轴扩展状态
            timelineExtendable: true,
            loadingTimeline: false,
            loadedDays: 0,
          };
        });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '查询失败';
        update((state) => ({
          ...state,
          results: { ...state.results, loading: false },
          error: errorMessage,
          hasMore: false,
        }));
      }
    },

    // 加载更多数据（无限滚动）
    async loadMore() {
      let currentState: any;
      update((state) => {
        currentState = state;
        return {
          ...state,
          loadingMore: true,
          error: null,
        };
      });

      try {
        const nextPage = currentState.results.pagination.currentPage + 1;
        const queryData = await dataQueryService.queryData({
          ...currentState.queryParams,
          page: nextPage,
          pageSize: currentState.results.pagination.itemsPerPage,
        });

        update((state) => {
          const totalPages = Math.ceil(queryData.total / state.results.pagination.itemsPerPage);
          const hasMore =
            queryData.items.length === state.results.pagination.itemsPerPage &&
            nextPage < totalPages;

          return {
            ...state,
            results: {
              items: [...state.results.items, ...queryData.items], // 追加新数据
              loading: false,
              pagination: {
                ...state.results.pagination,
                currentPage: nextPage,
                totalItems: queryData.total,
                totalPages,
              },
            },
            hasMore,
            loadingMore: false,
          };
        });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '加载更多数据失败';
        update((state) => ({
          ...state,
          loadingMore: false,
          error: errorMessage,
        }));
      }
    },

    // 扩展时间轴（向前加载更多天数的数据）
    async extendTimeline() {
      let currentState: any;
      update((state) => {
        currentState = state;
        // 检查是否可以继续扩展
        if (!state.timelineExtendable || state.loadingTimeline || state.loadedDays >= state.maxLoadDays) {
          return state;
        }
        return {
          ...state,
          loadingTimeline: true,
          error: null,
        };
      });

      // 如果不能扩展，直接返回
      if (!currentState.timelineExtendable || currentState.loadingTimeline || currentState.loadedDays >= currentState.maxLoadDays) {
        return;
      }

      try {
        // 计算前一天的日期
        const currentDate = currentState.queryParams.queryMode === 'basic'
          ? currentState.queryParams.basic.date
          : currentState.queryParams.advanced.date;

        if (!currentDate) {
          throw new Error('查询日期不能为空');
        }

        // 计算目标日期（当前日期减去已加载天数再减1天）
        const targetDate = new Date(currentDate);
        targetDate.setDate(targetDate.getDate() - currentState.loadedDays - 1);
        const targetDateStr = targetDate.toISOString().split('T')[0];

        let queryData;

        if (currentState.queryParams.queryMode === 'basic') {
          // 基础查询模式
          const basicParams = {
            ...currentState.queryParams.basic,
            date: targetDateStr,
          };
          queryData = await dataQueryService.queryData(basicParams);
        } else {
          // 高级查询模式
          const advancedParams = {
            ...currentState.queryParams.advanced,
            date: targetDateStr,
          };
          queryData = await dataQueryService.queryAdvancedData(advancedParams);
        }

        update((state) => {
          const newLoadedDays = state.loadedDays + 1;
          const canExtendMore = newLoadedDays < state.maxLoadDays;

          return {
            ...state,
            results: {
              ...state.results,
              items: [...state.results.items, ...queryData.items], // 追加新数据
              pagination: {
                ...state.results.pagination,
                totalItems: state.results.pagination.totalItems + queryData.items.length,
              },
            },
            loadingTimeline: false,
            loadedDays: newLoadedDays,
            timelineExtendable: canExtendMore,
            error: null, // 清除之前的错误
          };
        });

        // 如果有新数据加载，可以触发一些用户体验优化
        if (queryData.items.length > 0) {
          console.log(`成功加载 ${queryData.items.length} 条历史数据，日期: ${targetDateStr}`);
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '扩展时间轴失败';
        console.error('扩展时间轴失败:', error);

        update((state) => ({
          ...state,
          loadingTimeline: false,
          error: `加载历史数据失败: ${errorMessage}`,
          // 发生错误时不禁用扩展功能，允许用户重试
          timelineExtendable: state.loadedDays < state.maxLoadDays,
        }));
      }
    },

    // 更新查询参数
    updateQueryParams: (params: Partial<QueryParams>) => {
      update((state) => ({
        ...state,
        queryParams: { ...state.queryParams, ...params },
        results: {
          ...state.results,
          pagination: { ...state.results.pagination, currentPage: 1 },
        },
        hasMore: true,
        loadingMore: false,
      }));
    },

    // 添加币种到选中列表
    addSelectedCoin: (coinSymbol: string) => {
      update((state) => {
        const currentMode = state.queryParams.queryMode;
        const currentBase = currentMode === 'basic'
          ? [...state.queryParams.basic.base]
          : [...state.queryParams.advanced.base];

        if (!currentBase.includes(coinSymbol)) {
          currentBase.push(coinSymbol);
        }

        return {
          ...state,
          queryParams: {
            ...state.queryParams,
            [currentMode]: {
              ...state.queryParams[currentMode],
              base: currentBase,
            },
          },
          results: {
            ...state.results,
            pagination: { ...state.results.pagination, currentPage: 1 },
          },
        };
      });
    },

    // 从选中列表移除币种
    removeSelectedCoin: (coinSymbol: string) => {
      update((state) => {
        const currentMode = state.queryParams.queryMode;
        const currentBase = currentMode === 'basic'
          ? state.queryParams.basic.base
          : state.queryParams.advanced.base;

        const filteredBase = currentBase.filter(
          (coin: string) => coin !== coinSymbol
        );

        return {
          ...state,
          queryParams: {
            ...state.queryParams,
            [currentMode]: {
              ...state.queryParams[currentMode],
              base: filteredBase,
            },
          } as QueryParams,
          results: {
            ...state.results,
            pagination: { ...state.results.pagination, currentPage: 1 },
          },
        };
      });
    },

    // 清空选中的币种
    clearSelectedCoins: () => {
      update((state) => {
        const currentMode = state.queryParams.queryMode;
        return {
          ...state,
          queryParams: {
            ...state.queryParams,
            [currentMode]: {
              ...state.queryParams[currentMode],
              base: [],
            },
          } as QueryParams,
          results: {
            ...state.results,
            pagination: { ...state.results.pagination, currentPage: 1 },
          },
        };
      });
    },

    // 设置选中的币种列表
    setSelectedCoins: (coins: string[]) => {
      update((state) => {
        const currentMode = state.queryParams.queryMode;
        return {
          ...state,
          queryParams: {
            ...state.queryParams,
            [currentMode]: {
              ...state.queryParams[currentMode],
              base: coins,
            },
          } as QueryParams,
          results: {
            ...state.results,
            pagination: { ...state.results.pagination, currentPage: 1 },
          },
        };
      });
    },

    // 设置查询日期
    setDate: (date: string) => {
      update((state) => ({
        ...state,
        queryParams: {
          ...state.queryParams,
          basic: { ...state.queryParams.basic, date },
          advanced: { ...state.queryParams.advanced, date },
        },
        results: {
          ...state.results,
          pagination: { ...state.results.pagination, currentPage: 1 },
        },
      }));
    },

    // 设置阈值（仅用于基础查询模式）
    setThreshold: (threshold: number | undefined) => {
      update((state) => ({
        ...state,
        queryParams: {
          ...state.queryParams,
          basic: { ...state.queryParams.basic, threshold },
        },
        results: {
          ...state.results,
          pagination: { ...state.results.pagination, currentPage: 1 },
        },
      }));
    },

    // 设置市场事件类型（仅用于基础查询模式）
    setMarketEventType: (marketEventType: MarketEventType[]) => {
      update((state) => ({
        ...state,
        queryParams: {
          ...state.queryParams,
          basic: { ...state.queryParams.basic, marketEventType },
        },
        results: {
          ...state.results,
          pagination: { ...state.results.pagination, currentPage: 1 },
        },
      }));
    },

    // 切换查询模式
    setQueryMode: (mode: QueryMode) => {
      update((state) => ({
        ...state,
        queryParams: {
          ...state.queryParams,
          queryMode: mode,
        },
        results: {
          ...state.results,
          pagination: { ...state.results.pagination, currentPage: 1 },
        },
      }));
    },

    // 添加高级查询过滤条件
    addAdvancedFilter: (filter: { marketEventType: MarketEventType; threshold: number }) => {
      update((state) => {
        const newFilter = {
          id: `filter_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          ...filter,
        };

        return {
          ...state,
          queryParams: {
            ...state.queryParams,
            advanced: {
              ...state.queryParams.advanced,
              filters: [...state.queryParams.advanced.filters, newFilter],
            },
          },
          results: {
            ...state.results,
            pagination: { ...state.results.pagination, currentPage: 1 },
          },
        };
      });
    },

    // 移除高级查询过滤条件
    removeAdvancedFilter: (filterId: string) => {
      update((state) => ({
        ...state,
        queryParams: {
          ...state.queryParams,
          advanced: {
            ...state.queryParams.advanced,
            filters: state.queryParams.advanced.filters.filter(f => f.id !== filterId),
          },
        },
        results: {
          ...state.results,
          pagination: { ...state.results.pagination, currentPage: 1 },
        },
      }));
    },

    // 更新高级查询过滤条件
    updateAdvancedFilter: (filterId: string, updates: Partial<{ marketEventType: MarketEventType; threshold: number }>) => {
      update((state) => ({
        ...state,
        queryParams: {
          ...state.queryParams,
          advanced: {
            ...state.queryParams.advanced,
            filters: state.queryParams.advanced.filters.map(f =>
              f.id === filterId ? { ...f, ...updates } : f
            ),
          },
        },
        results: {
          ...state.results,
          pagination: { ...state.results.pagination, currentPage: 1 },
        },
      }));
    },

    // 清空高级查询过滤条件
    clearAdvancedFilters: () => {
      update((state) => ({
        ...state,
        queryParams: {
          ...state.queryParams,
          advanced: {
            ...state.queryParams.advanced,
            filters: [],
          },
        },
        results: {
          ...state.results,
          pagination: { ...state.results.pagination, currentPage: 1 },
        },
      }));
    },

    // 设置每页项目数
    setItemsPerPage: (itemsPerPage: number) => {
      update((state) => ({
        ...state,
        results: {
          ...state.results,
          pagination: {
            ...state.results.pagination,
            itemsPerPage,
            currentPage: 1,
          },
        },
      }));
    },

    // 切换筛选区域展开/收起状态
    toggleFilterExpanded: () => {
      update((state) => ({
        ...state,
        isFilterExpanded: !state.isFilterExpanded,
      }));
    },

    // 导出数据
    async exportData() {
      let currentState: any;
      update((state) => {
        currentState = state;
        return state;
      });

      try {
        const blob = await dataQueryService.exportData(currentState.queryParams);

        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `data-export-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '导出失败';
        update((state) => ({ ...state, error: errorMessage }));
      }
    },

    // 重置
    reset: () => {
      set({
        queryParams: initialQueryParams,
        results: {
          items: [],
          loading: false,
          pagination: initialPagination,
        },
        isFilterExpanded: true,
        error: null,
        hasMore: true,
        loadingMore: false,
        // 重置时间轴扩展状态
        timelineExtendable: true,
        loadingTimeline: false,
        loadedDays: 0,
        maxLoadDays: 7,
      });
    },
  };
}

// 创建并导出 store 实例
export const dataQueryStore = createDataQueryStore();
