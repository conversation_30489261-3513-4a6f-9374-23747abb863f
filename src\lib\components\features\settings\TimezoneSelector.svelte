<script lang="ts">
  import { Clock, Globe, Search } from '@lucide/svelte';
  import { onMount } from 'svelte';

  import { Badge } from '$lib/components/ui/badge';
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input';
  import { Select, SelectContent, SelectGroup,SelectItem, SelectLabel, SelectTrigger } from '$lib/components/ui/select';
  import { Separator } from '$lib/components/ui/separator';
  import { COMMON_TIME_ZONES, TIME_ZONES } from '$lib/constants/time';
  import type { TimezoneConfig } from '$lib/stores/features/timezone';
  import {
    currentTimezone,
    getCurrentTimezoneInfo,
    isUsingLocalTimezone,
    timezoneStore,
  } from '$lib/stores/features/timezone';

  /**
   * 组件属性
   */
  interface Props {
    /** 是否显示搜索框 */
    showSearch?: boolean;
    /** 是否只显示常用时区 */
    commonOnly?: boolean;
    /** 自定义样式类名 */
    class?: string;
  }

  const { showSearch = true, commonOnly = false, class: className = '' }: Props = $props();

  // 状态变量
  let searchQuery = $state('');
  let isOpen = $state(false);

  // 响应式数据 - 使用 $state 确保及时更新
  let timezone = $state('');
  let isLocal = $state(false);

  // 监听 store 变化并更新本地状态
  $effect(() => {
    timezone = $currentTimezone;
    isLocal = $isUsingLocalTimezone;
  });

  // 时区列表
  const availableTimezones = $derived(commonOnly ? COMMON_TIME_ZONES : TIME_ZONES);

  // 过滤后的时区列表
  const filteredTimezones = $derived(() => {
    if (!searchQuery.trim()) {
      return availableTimezones;
    }

    const query = searchQuery.toLowerCase();
    return availableTimezones.filter(
      (tz) =>
        tz.displayName.toLowerCase().includes(query) ||
        tz.description.toLowerCase().includes(query) ||
        tz.offset.toLowerCase().includes(query) ||
        tz.id.toLowerCase().includes(query)
    );
  });

  // 分组时区：常用和其他
  const groupedTimezones = $derived(() => {
    const filtered = filteredTimezones();
    if (commonOnly) {
      return { common: filtered, others: [] };
    }

    return {
      common: filtered.filter((tz) => tz.isCommon),
      others: filtered.filter((tz) => !tz.isCommon),
    };
  });

  // 当前时区信息 - 使用 $derived 但确保 timezone 是响应式的
  const currentTimezoneInfo = $derived(() => {
    if (!timezone) {
      return {
        offset: 'UTC+00:00',
        displayName: 'UTC',
      };
    }

    if (timezone === 'browser') {
      return {
        offset: '自动检测',
        displayName: '浏览器本地时区',
      };
    }

    // 确保调用 getCurrentTimezoneInfo 时传入有效的时区
    try {
      return getCurrentTimezoneInfo(timezone);
    } catch (error) {
      console.warn('Failed to get timezone info for:', timezone, error);
      return {
        offset: 'UTC+00:00',
        displayName: timezone || 'UTC',
      };
    }
  });

  // 选中的时区配置
  const selectedTimezoneConfig = $derived(() => {
    // 首先尝试在可用时区中查找
    const found = availableTimezones.find((tz) => tz.id === timezone);
    if (found) {
      return found;
    }

    // 如果没找到，创建一个默认配置
    if (timezone) {
      return {
        id: timezone,
        displayName: timezone,
        offset: 'UTC+00:00',
        description: timezone,
        isCommon: false,
      };
    }

    // 最后的降级选项
    return (
      availableTimezones[0] || {
        id: 'UTC',
        displayName: 'UTC',
        offset: 'UTC+00:00',
        description: 'Coordinated Universal Time',
        isCommon: true,
      }
    );
  });

  /**
   * 处理时区选择
   */
  function handleTimezoneSelect(value: string) {
    console.log('TimezoneSelector: handleTimezoneSelect called with value:', value);

    if (!value) {
      console.warn('TimezoneSelector: No timezone value provided:', value);
      return;
    }

    console.log('TimezoneSelector: Setting timezone to:', value);

    if (value === 'browser') {
      timezoneStore.useLocalTimezone();
    } else {
      timezoneStore.setTimezone(value);
    }

    isOpen = false;
    searchQuery = '';

    // 强制触发响应式更新
    console.log('TimezoneSelector: Timezone updated, current value:', $currentTimezone);
  }

  /**
   * 处理搜索输入
   */
  function handleSearchInput(event: Event) {
    const target = event.target as HTMLInputElement;
    searchQuery = target.value;
  }

  /**
   * 清除搜索
   */
  function clearSearch() {
    searchQuery = '';
  }

  /**
   * 格式化时区显示值
   */
  function formatTimezoneValue(tz: TimezoneConfig): string {
    return `${tz.displayName} (${tz.offset})`;
  }

  /**
   * 组件挂载时初始化
   */
  onMount(() => {
    timezoneStore.initialize();
  });
</script>

<div class="space-y-3 {className}">
  <!-- 当前时区显示 -->
  <div class="flex items-center justify-between rounded-lg border p-3">
    <div class="flex items-center gap-2">
      <Globe class="text-muted-foreground h-4 w-4" />
      <div class="flex flex-col">
        <span class="text-sm font-medium">当前时区</span>
        <span class="text-muted-foreground text-xs">
          {currentTimezoneInfo().displayName}
        </span>
      </div>
    </div>
    <Badge variant="outline" class="text-xs">
      {currentTimezoneInfo().offset}
    </Badge>
  </div>

  <!-- 时区选择器 -->
  <Select
    type="single"
    bind:open={isOpen}
    onValueChange={handleTimezoneSelect}
    value={timezone}
  >
    <SelectTrigger class="w-full">
      <div class="flex items-center gap-2">
        <Clock class="h-4 w-4" />
        <span class="flex-1 text-left">
          {selectedTimezoneConfig() ? formatTimezoneValue(selectedTimezoneConfig()) : '选择时区'}
        </span>
      </div>
    </SelectTrigger>

    <SelectContent class="max-h-80">
      <!-- 搜索框 -->
      {#if showSearch}
        <div class="p-2">
          <div class="relative">
            <Search class="text-muted-foreground absolute top-2.5 left-2 h-4 w-4" />
            <Input
              type="text"
              placeholder="搜索时区..."
              value={searchQuery}
              oninput={handleSearchInput}
              class="pl-8 text-sm"
            />
            {#if searchQuery}
              <Button
                variant="ghost"
                size="sm"
                onclick={clearSearch}
                class="absolute top-1 right-1 h-6 w-6 p-0"
              >
                ×
              </Button>
            {/if}
          </div>
        </div>
        <Separator />
      {/if}

      <!-- 常用时区 -->
      {#if groupedTimezones().common.length > 0}
        <SelectGroup>
          <SelectLabel class="text-muted-foreground px-2 py-1.5 text-xs font-medium">
            常用时区
          </SelectLabel>
          {#each groupedTimezones().common as tz (tz.id)}
            <SelectItem value={tz.id} class="cursor-pointer">
              <div class="flex w-full items-center justify-between">
                <div class="flex flex-col">
                  <span class="text-sm">{tz.displayName}</span>
                  <span class="text-muted-foreground text-xs">{tz.description}</span>
                </div>
                <Badge variant="outline" class="ml-2 text-xs">
                  {tz.offset}
                </Badge>
              </div>
            </SelectItem>
          {/each}
        </SelectGroup>
      {/if}

      <!-- 其他时区 -->
      {#if groupedTimezones().others.length > 0}
        {#if groupedTimezones().common.length > 0}
          <Separator />
        {/if}
        <SelectGroup>
          <SelectLabel class="text-muted-foreground px-2 py-1.5 text-xs font-medium">
            其他时区
          </SelectLabel>
          {#each groupedTimezones().others as tz (tz.id)}
            <SelectItem value={tz.id} class="cursor-pointer">
              <div class="flex w-full items-center justify-between">
                <div class="flex flex-col">
                  <span class="text-sm">{tz.displayName}</span>
                  <span class="text-muted-foreground text-xs">{tz.description}</span>
                </div>
                <Badge variant="outline" class="ml-2 text-xs">
                  {tz.offset}
                </Badge>
              </div>
            </SelectItem>
          {/each}
        </SelectGroup>
      {/if}

      <!-- 无搜索结果 -->
      {#if searchQuery && filteredTimezones().length === 0}
        <div class="text-muted-foreground p-4 text-center text-sm">未找到匹配的时区</div>
      {/if}
    </SelectContent>
  </Select>

  <!-- 快速操作 -->
  <div class="flex gap-2">
    <Button
      variant="outline"
      size="sm"
      onclick={() => timezoneStore.useLocalTimezone()}
      class="flex-1 text-xs"
    >
      <Globe class="mr-1 h-3 w-3" />
      使用本地时区
    </Button>
    <Button
      variant="outline"
      size="sm"
      onclick={() => timezoneStore.setTimezone('UTC')}
      class="flex-1 text-xs"
    >
      <Clock class="mr-1 h-3 w-3" />
      使用 UTC
    </Button>
  </div>
</div>


