# MSW 集成指南

Mock Service Worker (MSW) 是一个强大的 API 模拟工具，本项目使用 MSW 来模拟后端 API，确保前端开发的独立性和测试的可靠性。

## 🎯 为什么使用 MSW

### 优势

1. **开发独立性**：前端开发不依赖后端 API 的完成度
2. **测试可靠性**：提供稳定、可预测的测试数据
3. **离线开发**：无需网络连接即可进行开发
4. **真实环境模拟**：在浏览器层面拦截请求，更接近真实环境

### 与传统 Mock 方案对比

| 特性 | MSW | 传统 Mock |
|------|-----|-----------|
| 拦截层级 | 网络层 | 代码层 |
| 环境一致性 | 高 | 低 |
| 维护成本 | 低 | 高 |
| 真实性 | 高 | 中 |

## 🏗️ 架构设计

### 文件结构

```text
src/mocks/
├── browser.ts          # 浏览器环境 MSW 配置
├── node.ts            # Node.js 环境 MSW 配置
├── handlers.ts        # API 处理器定义
└── index.ts          # MSW 初始化逻辑
```

### 初始化流程

```typescript
// src/mocks/index.ts
async function initMocks() {
  if (import.meta.env.DEV || import.meta.env.VITE_USE_MSW === 'true') {
    const { worker } = await import('./browser');
    await worker.start({
      onUnhandledRequest: (req) => {
        // 智能过滤，只对真正的 API 请求显示警告
        if (req.url.includes('/api/')) {
          console.warn(`[MSW] 未处理的 API 请求: ${req.method} ${req.url}`);
        }
      },
    });
    console.log('MSW: 模拟API服务初始化成功');
  }
}
```

## 📝 API 处理器定义

### 基础处理器结构

```typescript
// src/mocks/handlers.ts
import { http, HttpResponse } from 'msw';

export const handlers = [
  // 处理图表数据请求
  http.get('/api/charts/bar', () => {
    return HttpResponse.json(barChartData);
  }),

  // 处理 POST 请求
  http.post('/api/liquidation/snapshot', async ({ request }) => {
    const body = await request.json();
    const data = generateMockLiquidationData(100);
    return HttpResponse.json({ data, stats: calculateStats(data) });
  }),
];
```

### 动态数据生成

```typescript
// 爆仓数据生成示例
function generateMockLiquidationData(count: number = 100): LiquidationData[] {
  const symbols = ['BTC', 'ETH', 'SOL', 'BNB', 'ADA', 'DOT'];
  const result: LiquidationData[] = [];

  for (let i = 0; i < count; i++) {
    const symbol = symbols[Math.floor(Math.random() * symbols.length)];
    const coinType = ['BTC', 'ETH', 'SOL', 'BNB'].includes(symbol) ? 'Major' : 'Altcoin';
    
    result.push({
      id: `liq-${i}`,
      datetime: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString(),
      amountUsd: Math.round(10000 + Math.random() * 990000),
      side: Math.random() > 0.5 ? 1 : 0,
      symbol,
      threshold: String([10000, 50000, 100000, 500000][Math.floor(Math.random() * 4)]),
      coinType,
    });
  }

  return result.sort((a, b) => new Date(b.datetime).getTime() - new Date(a.datetime).getTime());
}
```

## 🔧 配置和使用

### 环境变量配置

```bash
# .env.local
VITE_USE_MSW=true          # 强制启用 MSW
VITE_API_BASE_URL=/api     # API 基础路径
```

### 在组件中使用

```typescript
// 业务代码保持不变，MSW 自动拦截请求
import { liquidationDataService } from '$lib/services/data/liquidation';

// 这个请求会被 MSW 拦截并返回模拟数据
const response = await liquidationDataService.fetchSnapshotData('24h');
```

### 条件启用

```typescript
// MSW 只在开发环境和测试环境启用
if (import.meta.env.DEV || import.meta.env.MODE === 'test') {
  await initMocks();
}
```

## 🧪 测试集成

### 测试环境配置

```typescript
// src/tests/setup.ts
import { beforeAll, afterEach, afterAll } from 'vitest';
import { server } from '../mocks/node';

// 在所有测试开始前启动 MSW 服务器
beforeAll(() => server.listen());

// 每个测试后重置处理器
afterEach(() => server.resetHandlers());

// 所有测试结束后关闭服务器
afterAll(() => server.close());
```

### 测试中使用

```typescript
import { server } from '../mocks/node';
import { http, HttpResponse } from 'msw';

test('should handle API error', async () => {
  // 临时覆盖处理器以模拟错误
  server.use(
    http.get('/api/charts/bar', () => {
      return new HttpResponse(null, { status: 500 });
    })
  );

  // 测试错误处理逻辑
  const result = await chartsApi.getBarChartData();
  expect(result.status).toBe('error');
});
```

## 🎨 高级功能

### 请求延迟模拟

```typescript
http.get('/api/slow-endpoint', async () => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 2000));
  return HttpResponse.json({ data: 'slow response' });
});
```

### 条件响应

```typescript
http.post('/api/login', async ({ request }) => {
  const { username, password } = await request.json();
  
  if (username === 'admin' && password === 'password') {
    return HttpResponse.json({ token: 'mock-jwt-token' });
  } else {
    return new HttpResponse(null, { status: 401 });
  }
});
```

### 状态管理

```typescript
// 模拟有状态的 API
let currentUser = null;

const handlers = [
  http.post('/api/login', async ({ request }) => {
    const credentials = await request.json();
    currentUser = { id: 1, name: 'Mock User' };
    return HttpResponse.json({ user: currentUser });
  }),

  http.get('/api/profile', () => {
    if (!currentUser) {
      return new HttpResponse(null, { status: 401 });
    }
    return HttpResponse.json(currentUser);
  }),
];
```

## 🔍 调试和监控

### 请求日志

```typescript
// 启用详细日志
worker.start({
  onUnhandledRequest: 'warn',
  quiet: false, // 显示所有 MSW 日志
});
```

### 自定义日志

```typescript
http.get('/api/data', ({ request }) => {
  console.log(`[MSW] 处理请求: ${request.method} ${request.url}`);
  return HttpResponse.json(mockData);
});
```

## 📊 性能考虑

### 数据生成优化

```typescript
// 缓存生成的数据，避免重复计算
const dataCache = new Map();

function getCachedData(key: string, generator: () => any) {
  if (!dataCache.has(key)) {
    dataCache.set(key, generator());
  }
  return dataCache.get(key);
}
```

### 内存管理

```typescript
// 定期清理缓存
setInterval(() => {
  if (dataCache.size > 100) {
    dataCache.clear();
  }
}, 60000);
```

## 🚀 部署注意事项

### 生产环境

```typescript
// 确保生产环境不启用 MSW
if (import.meta.env.PROD) {
  console.warn('MSW should not be enabled in production');
  return;
}
```

### Service Worker 文件

```bash
# 确保 public/mockServiceWorker.js 文件存在
# 这个文件由 MSW 自动生成，需要部署到静态资源目录
```

## 🔗 相关资源

- [MSW 官方文档](https://mswjs.io/)
- [项目服务层文档](../services/README.md)
- [测试策略文档](./testing.md)
- [API 文档](../api/README.md)

## 💡 最佳实践

1. **保持处理器简单**：避免在 MSW 处理器中编写复杂的业务逻辑
2. **数据真实性**：模拟数据应该尽可能接近真实 API 的响应格式
3. **错误场景覆盖**：不仅要模拟成功场景，也要覆盖各种错误情况
4. **性能监控**：定期检查 MSW 对开发环境性能的影响
5. **文档同步**：保持 MSW 处理器与 API 文档的同步更新
