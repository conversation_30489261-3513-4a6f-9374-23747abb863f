# 开发指南

## 概述

本指南详细介绍了 Svelte Demo 项目的开发流程、代码规范、最佳实践和团队协作规范。遵循本指南可以确保代码质量、提高开发效率，并保持项目的一致性和可维护性。

## 开发环境设置

### 系统要求

- **Node.js**: >= 18.0.0 (推荐使用 LTS 版本)
- **npm**: >= 9.0.0
- **Git**: >= 2.28.0
- **VS Code**: 推荐使用的 IDE

### 推荐的 VS Code 扩展

```json
{
  "recommendations": [
    "svelte.svelte-vscode",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "usernamehw.errorlens",
    "gruntfuggly.todo-tree",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense"
  ]
}
```

### 开发环境配置

#### 1. 项目初始化

```bash
# 1. 克隆项目
git clone <repository-url>
cd svelte-demo

# 2. 安装依赖
npm install

# 3. 配置环境变量
cp .env.example .env.local

# 4. 启动开发服务器
npm run dev
```

#### 2. 环境变量配置

创建 `.env.local` 文件并配置以下变量：

```env
# API 配置
PUBLIC_API_BASE_URL=https://api.example.com
PUBLIC_WS_URL=wss://ws.example.com

# 功能开关
PUBLIC_ENABLE_MOCK_DATA=true
PUBLIC_ENABLE_REAL_TIME=false
PUBLIC_ENABLE_PERFORMANCE_MONITOR=true

# 开发工具
PUBLIC_ENABLE_INSPECTOR=true
PUBLIC_LOG_LEVEL=debug

# 测试配置
VITEST_UI_PORT=51204
```

#### 3. Git 配置

```bash
# 配置用户信息
git config user.name "Your Name"
git config user.email "<EMAIL>"

# 配置 Git Hook（可选）
npx simple-git-hooks
```

## 开发流程

### 1. 功能开发流程

#### 分支策略

采用 Git Flow 工作流：

```
main (生产)
├── develop (开发)
│   ├── feature/feature-name (功能)
│   ├── bugfix/bug-description (缺陷修复)
│   └── hotfix/urgent-fix (紧急修复)
└── release/version-number (发布)
```

#### 开发步骤

```bash
# 1. 从 develop 分支创建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/new-chart-component

# 2. 开发功能
# - 编写代码
# - 添加测试
# - 更新文档

# 3. 提交代码
git add .
git commit -m "feat: 新增图表组件功能"

# 4. 推送并创建 PR
git push origin feature/new-chart-component
# 在 GitHub/GitLab 创建 Pull Request

# 5. 代码审查通过后合并
# 合并到 develop 分支
```

### 2. 提交规范 (Conventional Commits)

#### 提交消息格式

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

#### 类型定义

| 类型 | 描述 | 示例 |
|------|------|------|
| `feat` | 新功能 | `feat: 添加图表拖拽功能` |
| `fix` | 缺陷修复 | `fix: 修复图表渲染问题` |
| `docs` | 文档更新 | `docs: 更新组件使用说明` |
| `style` | 代码格式化 | `style: 修复 ESLint 警告` |
| `refactor` | 重构代码 | `refactor: 重构 API 服务层` |
| `test` | 测试相关 | `test: 添加组件单元测试` |
| `chore` | 构建/工具相关 | `chore: 更新依赖版本` |
| `perf` | 性能优化 | `perf: 优化图表渲染性能` |

#### 提交示例

```bash
# 功能提交
git commit -m "feat(dashboard): 添加实时数据刷新功能"

# 缺陷修复
git commit -m "fix(charts): 修复饼图数据为空时的显示问题"

# 重构
git commit -m "refactor(api): 重构 API 错误处理机制

- 统一错误类型定义
- 添加重试机制
- 改善用户提示消息"

# 文档更新
git commit -m "docs: 更新组件开发指南

- 添加新组件开发模板
- 补充测试编写说明"
```

### 3. 代码审查流程

#### Pull Request 检查清单

**提交者检查清单**:
- [ ] 代码符合项目规范
- [ ] 添加了必要的测试
- [ ] 更新了相关文档
- [ ] 没有破坏现有功能
- [ ] 性能影响可接受

**审查者检查清单**:
- [ ] 代码逻辑正确
- [ ] 遵循设计原则
- [ ] 测试覆盖充分
- [ ] 无安全隐患
- [ ] 文档清晰完整

#### 审查标准

1. **功能性**: 代码是否正确实现了需求
2. **可读性**: 代码是否清晰易懂
3. **性能**: 是否存在性能问题
4. **安全性**: 是否存在安全漏洞
5. **测试**: 测试覆盖是否充分
6. **文档**: 是否有必要的文档更新

## 代码规范

### 1. ESLint 配置

项目使用 ESLint 进行代码质量检查：

```javascript
// eslint.config.js 核心规则
export default ts.config({
  rules: {
    'no-debugger': 'error',                    // 禁止 debugger
    'prefer-const': 'error',                   // 优先使用 const
    'no-var': 'error',                         // 禁止使用 var
    '@typescript-eslint/no-unused-vars': [     // 未使用变量检查
      'error',
      {
        argsIgnorePattern: '^_',               // 忽略 _ 开头的参数
        varsIgnorePattern: '^_'                // 忽略 _ 开头的变量
      }
    ],
    'simple-import-sort/imports': 'error',     // 导入排序
    'simple-import-sort/exports': 'error',     // 导出排序
    'import/no-duplicates': 'error',           // 禁止重复导入
    'svelte/no-target-blank': 'error'          // target="_blank" 安全检查
  }
});
```

### 2. Prettier 配置

```json
{
  "useTabs": false,
  "singleQuote": true,
  "trailingComma": "es5",
  "printWidth": 100,
  "tabWidth": 2,
  "semi": true,
  "endOfLine": "lf"
}
```

### 3. TypeScript 规范

#### 类型定义

```typescript
// ✅ 好的做法
interface UserData {
  id: string;
  name: string;
  email: string;
  createdAt: Date;
}

// 使用具体类型而非 any
function processUserData(data: UserData): string {
  return `${data.name} (${data.email})`;
}

// ❌ 避免的做法
function processUserData(data: any): any {
  return data.name + ' ' + data.email;
}
```

#### 组件 Props 类型

```typescript
// ✅ 推荐的组件 Props 定义
interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  onClick?: (event: MouseEvent) => void;
  children?: Snippet;
  class?: string;
}

let {
  variant = 'primary',
  size = 'md',
  disabled = false,
  onClick,
  children,
  class: className,
  ...restProps
}: ButtonProps = $props();
```

### 4. Svelte 组件规范

#### 组件结构

```svelte
<!-- ComponentName.svelte -->
<script lang="ts" module>
  // 1. 模块级别的类型定义和导出
  export interface ComponentProps {
    // Props 类型定义
  }
</script>

<script lang="ts">
  // 2. 导入语句（按字母顺序排序）
  import { createEventDispatcher, onMount, onDestroy } from 'svelte';
  import ComponentA from './ComponentA.svelte';
  import { utilityFunction } from '$lib/utils';
  
  // 3. Props 定义
  let { 
    prop1 = 'defaultValue',
    prop2,
    class: className = '',
    ...restProps 
  }: ComponentProps = $props();
  
  // 4. 事件分发器
  const dispatch = createEventDispatcher<{
    click: { data: string };
    change: { value: number };
  }>();
  
  // 5. 响应式状态
  let localState = $state('initial');
  let derivedValue = $derived(prop1.toUpperCase());
  
  // 6. 函数定义
  function handleClick() {
    dispatch('click', { data: localState });
  }
  
  // 7. 生命周期
  onMount(() => {
    // 初始化逻辑
  });
  
  onDestroy(() => {
    // 清理逻辑
  });
</script>

<!-- 8. 模板 -->
<div class={cn('component-base-class', className)} {...restProps}>
  {@render children?.()}
</div>

<style>
  /* 9. 样式（如需要，优先使用 Tailwind） */
</style>
```

#### 命名规范

```typescript
// 组件文件名：PascalCase
// ComponentName.svelte, UserProfile.svelte

// 组件内部变量：camelCase
let userName = '';
let isLoading = false;

// 常量：UPPER_SNAKE_CASE
const MAX_RETRY_COUNT = 3;
const API_ENDPOINTS = {
  USERS: '/api/users',
  DASHBOARD: '/api/dashboard'
} as const;

// 函数名：camelCase，动词开头
function handleSubmit() {}
function validateForm() {}
function getUserData() {}

// 类型/接口名：PascalCase
interface UserData {}
type ButtonVariant = 'primary' | 'secondary';
```

### 5. CSS/Tailwind 规范

#### Tailwind 类名顺序

```html
<!-- 推荐的类名顺序 -->
<div class="
  <!-- 布局 -->
  flex flex-col items-center justify-between
  <!-- 尺寸 -->
  w-full h-screen max-w-lg
  <!-- 间距 -->
  p-4 m-2 gap-4
  <!-- 背景和边框 -->
  bg-white border border-gray-200 rounded-lg
  <!-- 文字 -->
  text-lg font-medium text-gray-900
  <!-- 交互状态 -->
  hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500
  <!-- 响应式 -->
  sm:p-6 md:max-w-xl lg:max-w-2xl
">
  内容
</div>
```

#### 自定义 CSS 变量

```css
/* 使用 CSS 变量实现主题 */
:root {
  --color-primary: 222.2 47.4% 11.2%;
  --color-primary-foreground: 210 40% 98%;
  --border-radius: 0.5rem;
}

.dark {
  --color-primary: 210 40% 98%;
  --color-primary-foreground: 222.2 47.4% 11.2%;
}

/* 组件样式 */
.custom-component {
  @apply bg-background text-foreground;
  border-radius: var(--border-radius);
}
```

## 测试规范

### 1. 测试策略

```
测试金字塔
├── E2E 测试 (少量)              # 关键用户流程
├── 集成测试 (中等数量)            # 组件交互、API 集成
└── 单元测试 (大量)               # 函数、工具类、组件逻辑
```

### 2. 单元测试

#### 组件测试

```typescript
// Button.test.ts
import { render, screen, fireEvent } from '@testing-library/svelte';
import { describe, it, expect, vi } from 'vitest';
import Button from './Button.svelte';

describe('Button', () => {
  it('renders with default props', () => {
    render(Button, { children: 'Click me' });
    
    const button = screen.getByRole('button');
    expect(button).toHaveTextContent('Click me');
    expect(button).toHaveClass('btn-primary');
  });

  it('applies custom variant', () => {
    render(Button, { 
      variant: 'secondary', 
      children: 'Secondary' 
    });
    
    expect(screen.getByRole('button')).toHaveClass('btn-secondary');
  });

  it('handles click events', async () => {
    const handleClick = vi.fn();
    render(Button, { 
      onClick: handleClick,
      children: 'Click me' 
    });
    
    await fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('is disabled when disabled prop is true', () => {
    render(Button, { 
      disabled: true,
      children: 'Disabled' 
    });
    
    expect(screen.getByRole('button')).toBeDisabled();
  });
});
```

#### Store 测试

```typescript
// dashboard.test.ts
import { describe, it, expect, beforeEach } from 'vitest';
import { get } from 'svelte/store';
import { dashboardStore } from './dashboard';

describe('Dashboard Store', () => {
  beforeEach(() => {
    dashboardStore.reset();
  });

  it('initializes with default state', () => {
    const state = get(dashboardStore);
    
    expect(state.isLoading).toBe(false);
    expect(state.error).toBe(null);
    expect(state.stats.totalUsers).toBe(0);
  });

  it('sets loading state', () => {
    dashboardStore.setLoading(true);
    
    const state = get(dashboardStore);
    expect(state.isLoading).toBe(true);
  });

  it('updates stats data', async () => {
    const mockStats = {
      totalUsers: 100,
      monthlyRevenue: 5000,
      conversionRate: 12.5,
      activeUsers: 75
    };

    // Mock API 调用
    vi.mocked(dashboardApi.getStats).mockResolvedValue({
      data: mockStats,
      status: 'success'
    });

    await dashboardStore.loadDashboardData();
    
    const state = get(dashboardStore);
    expect(state.stats).toEqual(mockStats);
    expect(state.isLoading).toBe(false);
  });
});
```

### 3. 集成测试

```typescript
// Dashboard.integration.test.ts
import { render, screen, waitFor } from '@testing-library/svelte';
import { describe, it, expect } from 'vitest';
import Dashboard from './Dashboard.svelte';

describe('Dashboard Integration', () => {
  it('loads and displays dashboard data', async () => {
    render(Dashboard);
    
    // 等待数据加载
    await waitFor(() => {
      expect(screen.getByText('总用户数')).toBeInTheDocument();
    });
    
    // 验证统计卡片显示
    expect(screen.getByText('1,234')).toBeInTheDocument();
    expect(screen.getByText('$5,678')).toBeInTheDocument();
    
    // 验证图表渲染
    expect(screen.getByLabelText(/chart/i)).toBeInTheDocument();
  });

  it('handles error states', async () => {
    // Mock API 错误
    vi.mocked(dashboardApi.getStats).mockRejectedValue(
      new Error('Network error')
    );
    
    render(Dashboard);
    
    await waitFor(() => {
      expect(screen.getByText(/加载失败/)).toBeInTheDocument();
    });
  });
});
```

### 4. 测试最佳实践

#### 测试文件组织

```
src/
├── lib/
│   ├── components/
│   │   ├── Button/
│   │   │   ├── Button.svelte
│   │   │   ├── Button.test.ts          # 单元测试
│   │   │   └── index.ts
│   │   └── Dashboard/
│   │       ├── Dashboard.svelte
│   │       ├── Dashboard.test.ts       # 单元测试
│   │       ├── Dashboard.integration.test.ts  # 集成测试
│   │       └── index.ts
│   └── utils/
│       ├── formatters.ts
│       └── formatters.test.ts          # 工具函数测试
└── tests/
    ├── setup.ts                        # 测试配置
    ├── mocks/                          # 全局 Mock
    └── e2e/                            # E2E 测试
        ├── dashboard.spec.ts
        └── navigation.spec.ts
```

#### 测试命名规范

```typescript
describe('ComponentName', () => {
  // 功能描述
  it('renders correctly with default props', () => {});
  it('handles user interactions', () => {});
  it('displays error states', () => {});
  
  // 边界情况
  it('handles empty data', () => {});
  it('handles large datasets', () => {});
  
  // 无障碍性
  it('is accessible with keyboard navigation', () => {});
  it('has proper ARIA attributes', () => {});
});
```

## 性能优化指南

### 1. 组件性能

#### 避免不必要的重渲染

```svelte
<script lang="ts">
  // ✅ 使用 $derived 进行计算
  let items = $state([]);
  let filteredItems = $derived(
    items.filter(item => item.active)
  );

  // ❌ 避免在模板中直接计算
  // {items.filter(item => item.active)}
</script>

<!-- ✅ 使用计算后的值 -->
{#each filteredItems as item}
  <Item {item} />
{/each}
```

#### 组件懒加载

```typescript
// 动态导入大型组件
const HeavyChart = lazy(() => import('./HeavyChart.svelte'));

// 或使用条件渲染
{#if shouldShowChart}
  {#await import('./HeavyChart.svelte') then { default: HeavyChart }}
    <HeavyChart data={chartData} />
  {/await}
{/if}
```

### 2. 数据处理优化

#### 虚拟滚动

```svelte
<!-- VirtualList.svelte -->
<script lang="ts">
  import { onMount } from 'svelte';
  
  export let items: any[] = [];
  export let itemHeight = 50;
  export let containerHeight = 400;
  
  let scrollTop = $state(0);
  let containerElement: HTMLDivElement;
  
  // 计算可见范围
  let visibleRange = $derived(() => {
    const start = Math.floor(scrollTop / itemHeight);
    const visibleCount = Math.ceil(containerHeight / itemHeight);
    const end = Math.min(start + visibleCount + 5, items.length);
    
    return { start: Math.max(0, start - 5), end };
  });
  
  // 可见项目
  let visibleItems = $derived(
    items.slice(visibleRange.start, visibleRange.end)
      .map((item, index) => ({ 
        item, 
        index: visibleRange.start + index 
      }))
  );
</script>

<div 
  class="virtual-list" 
  style="height: {containerHeight}px; overflow-y: auto;"
  bind:this={containerElement}
  on:scroll={e => scrollTop = e.currentTarget.scrollTop}
>
  <!-- 上方占位 -->
  <div style="height: {visibleRange.start * itemHeight}px;"></div>
  
  <!-- 可见项目 -->
  {#each visibleItems as { item, index }}
    <div class="virtual-item" style="height: {itemHeight}px;">
      <slot {item} {index} />
    </div>
  {/each}
  
  <!-- 下方占位 -->
  <div style="height: {(items.length - visibleRange.end) * itemHeight}px;"></div>
</div>
```

### 3. 图表性能

#### ECharts 优化

```typescript
// 数据采样
function sampleData(data: any[], maxPoints: number = 1000) {
  if (data.length <= maxPoints) return data;
  
  const step = Math.ceil(data.length / maxPoints);
  return data.filter((_, index) => index % step === 0);
}

// 图表配置优化
const chartOptions = {
  animation: false,        // 大数据时关闭动画
  series: [{
    large: true,          // 启用大数据模式
    largeThreshold: 2000, // 大数据阈值
    sampling: 'average',   // 数据采样策略
    data: sampleData(rawData)
  }]
};
```

### 4. 网络性能

#### 请求优化

```typescript
// 请求批处理
class BatchRequestService {
  private queue: Map<string, Promise<any>> = new Map();
  private batchTimer: number | null = null;
  
  async batchRequest<T>(key: string, requestFn: () => Promise<T>): Promise<T> {
    if (this.queue.has(key)) {
      return this.queue.get(key)!;
    }
    
    const promise = this.deferredRequest(requestFn);
    this.queue.set(key, promise);
    
    // 批量处理延迟
    if (!this.batchTimer) {
      this.batchTimer = window.setTimeout(() => {
        this.processBatch();
        this.batchTimer = null;
      }, 10);
    }
    
    return promise;
  }
  
  private async deferredRequest<T>(requestFn: () => Promise<T>): Promise<T> {
    return requestFn();
  }
  
  private processBatch() {
    // 批量处理逻辑
    this.queue.clear();
  }
}
```

## 部署和发布

### 1. 构建配置

#### 生产构建优化

```typescript
// vite.config.ts
export default defineConfig({
  build: {
    target: 'esnext',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    },
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['echarts', 'svelte'],
          ui: ['@radix-ui/colors', 'tailwind-variants']
        }
      }
    }
  },
  
  // 环境变量
  define: {
    __DEV__: false,
    __VERSION__: JSON.stringify(process.env.npm_package_version)
  }
});
```

### 2. 发布流程

#### 版本发布

```bash
# 1. 更新版本号
npm version patch  # 补丁版本
npm version minor  # 小版本
npm version major  # 主版本

# 2. 生成 Changelog
npm run changelog

# 3. 构建生产版本
npm run build

# 4. 运行测试
npm run test:run

# 5. 发布
git push origin main --tags
npm publish
```

#### 部署检查清单

- [ ] 所有测试通过
- [ ] 生产构建成功
- [ ] 环境变量配置正确
- [ ] 性能指标符合要求
- [ ] 安全检查通过
- [ ] 文档更新完成

## 故障排查指南

### 1. 常见问题

#### 开发环境问题

```bash
# 依赖问题
rm -rf node_modules package-lock.json
npm install

# TypeScript 检查问题
npx svelte-kit sync
npm run check

# 构建问题
npm run build -- --verbose
```

#### 运行时错误

```typescript
// 错误边界调试
function debugError(error: Error, errorInfo: any) {
  console.group('🚨 Error Boundary');
  console.error('Error:', error);
  console.error('Error Info:', errorInfo);
  console.error('Stack:', error.stack);
  console.groupEnd();
  
  // 发送到错误监控服务
  if (import.meta.env.PROD) {
    errorTracker.report(error, errorInfo);
  }
}
```

### 2. 调试技巧

#### 开发工具

```typescript
// 组件调试
$inspect(variableName); // Svelte 5 调试工具

// 性能分析
console.time('operation');
performHeavyOperation();
console.timeEnd('operation');

// 条件断点
if (someCondition) {
  debugger;
}
```

#### 网络调试

```typescript
// API 调试中间件
class DebugApiService extends BaseApiService {
  protected async request<T>(endpoint: string, options: RequestConfig = {}) {
    const startTime = performance.now();
    
    try {
      const response = await super.request<T>(endpoint, options);
      const duration = performance.now() - startTime;
      
      console.log(`✅ ${options.method || 'GET'} ${endpoint} - ${duration.toFixed(2)}ms`);
      return response;
    } catch (error) {
      const duration = performance.now() - startTime;
      console.error(`❌ ${options.method || 'GET'} ${endpoint} - ${duration.toFixed(2)}ms`, error);
      throw error;
    }
  }
}
```

## 团队协作规范

### 1. 代码审查规范

#### 审查检查点

1. **功能完整性**
   - 是否完全实现需求
   - 边界情况处理
   - 错误处理机制

2. **代码质量**
   - 代码可读性
   - 性能考虑
   - 安全性检查

3. **测试覆盖**
   - 单元测试
   - 集成测试
   - 边界测试

4. **文档更新**
   - API 文档
   - 使用说明
   - 变更记录

#### 审查流程

```mermaid
flowchart LR
    A[提交 PR] --> B[自动检查]
    B --> C{检查通过?}
    C -->|否| D[修复问题]
    C -->|是| E[代码审查]
    E --> F{审查通过?}
    F -->|否| G[反馈修改]
    F -->|是| H[合并代码]
    D --> B
    G --> B
```

### 2. 沟通协作

#### 技术文档

- **决策记录**: 重要技术决策的背景和原因
- **API 文档**: 接口规范和使用说明
- **组件文档**: 组件用法和示例
- **故障手册**: 常见问题和解决方案

#### 团队协作工具

- **GitHub/GitLab**: 代码管理和协作
- **Slack/Teams**: 日常沟通
- **Confluence/Notion**: 文档管理
- **Jira/Linear**: 任务管理

## 最佳实践总结

### 1. 开发原则

1. **代码即文档**: 写自解释的代码
2. **测试驱动**: 先写测试再写代码
3. **渐进增强**: 从基础功能开始构建
4. **性能优先**: 时刻考虑性能影响
5. **用户体验**: 站在用户角度思考

### 2. 质量保证

1. **自动化测试**: 完善的测试覆盖
2. **持续集成**: 自动化构建和部署
3. **代码审查**: 严格的代码审查流程
4. **性能监控**: 实时性能指标监控
5. **错误追踪**: 完善的错误报告系统

### 3. 技术债务管理

1. **定期重构**: 持续改善代码质量
2. **依赖更新**: 及时更新依赖版本
3. **性能优化**: 定期性能检查和优化
4. **安全审计**: 定期安全检查
5. **文档维护**: 保持文档同步更新

---

遵循本开发指南，可以确保项目的长期可维护性和团队协作效率。如有疑问或建议，请及时与团队沟通。