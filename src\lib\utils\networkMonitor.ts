/**
 * 网络状态监控工具
 *
 * 监控网络连接状态，提供网络质量检测和连接恢复通知
 *
 * @category Utils
 */

import { derived, writable } from 'svelte/store';

import { browser } from '$app/environment';
import { notifications } from '$lib/stores/ui/notifications';

import { createModuleLogger } from './logger';

const networkLogger = createModuleLogger('network-monitor');

/**
 * 网络状态接口
 */
export interface NetworkStatus {
  /** 是否在线 */
  isOnline: boolean;
  /** 连接类型 */
  connectionType?: string;
  /** 网络质量 */
  quality: 'excellent' | 'good' | 'poor' | 'offline';
  /** 延迟（毫秒） */
  latency?: number;
  /** 下载速度（Mbps） */
  downloadSpeed?: number;
  /** 最后检测时间 */
  lastChecked: Date;
}

/**
 * 网络监控配置
 */
export interface NetworkMonitorConfig {
  /** 是否启用监控 */
  enabled: boolean;
  /** 检测间隔（毫秒） */
  checkInterval: number;
  /** 延迟测试URL */
  latencyTestUrl: string;
  /** 速度测试URL */
  speedTestUrl?: string;
  /** 是否显示通知 */
  showNotifications: boolean;
}

/**
 * 网络监控类
 */
export class NetworkMonitor {
  private config: NetworkMonitorConfig;
  private checkTimer: NodeJS.Timeout | null = null;
  private isInitialized = false;
  private lastOnlineStatus = true;

  constructor(config: Partial<NetworkMonitorConfig> = {}) {
    this.config = {
      enabled: true,
      checkInterval: 30000, // 30秒
      latencyTestUrl: '/api/health',
      showNotifications: true,
      ...config,
    };
  }

  /**
   * 初始化网络监控
   */
  initialize(): void {
    if (this.isInitialized || !browser) {
      return;
    }

    // 立即设置基础网络状态，避免首次加载显示离线
    const initialStatus: NetworkStatus = {
      isOnline: navigator.onLine,
      quality: navigator.onLine ? 'good' : 'offline',
      lastChecked: new Date(),
    };
    networkStatusStore.set(initialStatus);

    // 监听浏览器的在线/离线事件
    window.addEventListener('online', this.handleOnline);
    window.addEventListener('offline', this.handleOffline);

    // 立即进行一次快速检测，然后异步进行详细检测
    if (navigator.onLine) {
      // 如果浏览器认为在线，立即进行网络质量检测
      this.checkNetworkStatus().catch(() => {
        // 如果检测失败，设置为离线状态
        networkStatusStore.set({
          isOnline: false,
          quality: 'offline',
          lastChecked: new Date(),
        });
      });
    }

    // 启动定期检测
    if (this.config.enabled) {
      this.startPeriodicCheck();
    }

    this.isInitialized = true;
    networkLogger.info('NetworkMonitor initialized', {
      config: this.config,
      initialOnline: navigator.onLine,
    });
  }

  /**
   * 销毁网络监控
   */
  destroy(): void {
    if (!this.isInitialized || !browser) {
      return;
    }

    window.removeEventListener('online', this.handleOnline);
    window.removeEventListener('offline', this.handleOffline);

    this.stopPeriodicCheck();
    this.isInitialized = false;

    networkLogger.info('NetworkMonitor destroyed');
  }

  /**
   * 处理在线事件
   */
  private handleOnline = (): void => {
    networkLogger.info('Browser detected online status');
    this.checkNetworkStatus();

    if (this.config.showNotifications && !this.lastOnlineStatus) {
      notifications.success('网络已连接', '网络连接已恢复');
    }

    this.lastOnlineStatus = true;
  };

  /**
   * 处理离线事件
   */
  private handleOffline = (): void => {
    networkLogger.warn('Browser detected offline status');

    networkStatusStore.set({
      isOnline: false,
      quality: 'offline',
      lastChecked: new Date(),
    });

    if (this.config.showNotifications) {
      notifications.warning('网络已断开', '请检查您的网络连接');
    }

    this.lastOnlineStatus = false;
  };

  /**
   * 检测网络状态
   */
  private async checkNetworkStatus(): Promise<void> {
    try {
      const status = await this.performNetworkTest();
      networkStatusStore.set(status);

      networkLogger.debug('Network status updated', {
        isOnline: status.isOnline,
        quality: status.quality,
        latency: status.latency,
        connectionType: status.connectionType,
        downloadSpeed: status.downloadSpeed,
        lastChecked: status.lastChecked.toISOString(),
      });
    } catch (error) {
      networkLogger.error('Failed to check network status', {
        error: error instanceof Error ? error.message : String(error),
      });

      networkStatusStore.set({
        isOnline: false,
        quality: 'offline',
        lastChecked: new Date(),
      });
    }
  }

  /**
   * 执行网络测试
   */
  private async performNetworkTest(): Promise<NetworkStatus> {
    const startTime = performance.now();

    try {
      const response = await fetch(this.config.latencyTestUrl, {
        method: 'HEAD',
        cache: 'no-cache',
        signal: AbortSignal.timeout(5000), // 5秒超时
      });

      const endTime = performance.now();
      const latency = endTime - startTime;

      const status: NetworkStatus = {
        isOnline: response.ok,
        quality: this.calculateQuality(latency),
        latency: Math.round(latency),
        lastChecked: new Date(),
      };

      // 获取连接信息（如果支持）
      if ('connection' in navigator) {
        const connection = (navigator as any).connection;
        status.connectionType = connection?.effectiveType || connection?.type;
        status.downloadSpeed = connection?.downlink;
      }

      return status;
    } catch (error) {
      throw new Error(
        `Network test failed: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * 根据延迟计算网络质量
   */
  private calculateQuality(latency: number): NetworkStatus['quality'] {
    if (latency < 100) return 'excellent';
    if (latency < 300) return 'good';
    if (latency < 1000) return 'poor';
    return 'offline';
  }

  /**
   * 启动定期检测
   */
  private startPeriodicCheck(): void {
    this.checkTimer = setInterval(() => {
      this.checkNetworkStatus();
    }, this.config.checkInterval);
  }

  /**
   * 停止定期检测
   */
  private stopPeriodicCheck(): void {
    if (this.checkTimer) {
      clearInterval(this.checkTimer);
      this.checkTimer = null;
    }
  }

  /**
   * 手动触发网络检测
   */
  async checkNow(): Promise<NetworkStatus> {
    const status = await this.performNetworkTest();
    networkStatusStore.set(status);
    return status;
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<NetworkMonitorConfig>): void {
    const wasEnabled = this.config.enabled;
    this.config = { ...this.config, ...newConfig };

    // 如果启用状态发生变化，重新启动检测
    if (wasEnabled !== this.config.enabled) {
      if (this.config.enabled) {
        this.startPeriodicCheck();
      } else {
        this.stopPeriodicCheck();
      }
    }

    networkLogger.info('NetworkMonitor config updated', { config: this.config });
  }

  /**
   * 获取当前配置
   */
  getConfig(): NetworkMonitorConfig {
    return { ...this.config };
  }
}

// 创建网络状态 store，使用更智能的初始状态
const getInitialNetworkStatus = (): NetworkStatus => {
  if (!browser) {
    return {
      isOnline: true,
      quality: 'good',
      lastChecked: new Date(),
    };
  }

  const isOnline = navigator.onLine;
  return {
    isOnline,
    quality: isOnline ? 'good' : 'offline',
    lastChecked: new Date(),
  };
};

const networkStatusStore = writable<NetworkStatus>(getInitialNetworkStatus());

// 导出网络状态 store
export const networkStatus = {
  subscribe: networkStatusStore.subscribe,
};

// 派生 store：是否在线
export const isOnline = derived(networkStatus, ($status) => $status.isOnline);

// 派生 store：网络质量
export const networkQuality = derived(networkStatus, ($status) => $status.quality);

// 创建全局实例，但不自动初始化
export const networkMonitor = new NetworkMonitor();

// 导出初始化函数，供组件调用
export const initializeNetworkMonitor = (): void => {
  if (browser && !networkMonitor['isInitialized']) {
    networkMonitor.initialize();
  }
};

// 导出销毁函数，供组件清理
export const destroyNetworkMonitor = (): void => {
  if (browser) {
    networkMonitor.destroy();
  }
};
