// src/lib/components/layout/AppSidebar/index.ts

// 导出类型定义
export type {
  AppSidebarProps,
  NavigationHandler,
  NavItem,
  NavMainProps,
  NavProjectsProps,
  NavSubItem,
  NavUserProps,
  ProjectActionHandler,
  ProjectItem,
  SidebarConfig,
  SidebarState,
  SidebarTheme,
  TabInfo,
  TeamInfo,
  TeamSwitcherProps,
  TeamSwitchHandler,
  UserAction,
  UserActionHandler,
  UserInfo,
} from './types.js';

// 导出配置
export { getTabInfoFromPath, sidebarConfig } from './config.js';

// 导出工具函数
export {
  createSafeConfig,
  getNavItemDisplayText,
  getProjectDisplayText,
  getTeamDisplayName,
  getTeamInitials,
  getUserDisplayName,
  getUserInitials,
  hasAvatar,
  hasLogo,
  hasSubItems,
  hasValidStatus,
  isLeafNavItem,
  isNavItemActive,
  isPathActive,
  validateNavItem,
  validateNavSubItem,
  validateProjectItem,
  validateSidebarConfig,
  validateTeamInfo,
  validateUserInfo,
} from './utils.js';

// 导出组件
export { default as AppSidebar } from './AppSidebar.svelte';
export { default as AppSidebarProvider } from './AppSidebarProvider.svelte';
export { default as NavMain } from './NavMain.svelte';
export { default as NavProjects } from './NavProjects.svelte';
export { default as NavUser } from './NavUser.svelte';
export { default as TeamSwitcher } from './TeamSwitcher.svelte';
// SystemStatus 从 features/system 导出，避免重复导出
