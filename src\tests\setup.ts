/**
 * Vitest 测试环境设置
 */

import { vi } from 'vitest';

// 模拟浏览器环境
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// 模拟 ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// 模拟 IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// 模拟 requestIdleCallback
global.requestIdleCallback = vi.fn((callback: any) => {
  setTimeout(callback, 0);
  return 1;
});

// 模拟 cancelIdleCallback
global.cancelIdleCallback = vi.fn();

// 模拟 CSS.supports
if (typeof CSS === 'undefined') {
  global.CSS = {
    supports: vi.fn().mockReturnValue(true),
  } as any;
}
