// src/lib/stores/features/cardInteraction.ts
import { writable } from 'svelte/store';

import type { MarketEvent } from '$lib/types';

interface CardInteractionState {
  selectedEventId: string | null;
  showDetailModal: boolean;
  selectedEvent: MarketEvent | null;
}

const initialState: CardInteractionState = {
  selectedEventId: null,
  showDetailModal: false,
  selectedEvent: null,
};

/**
 * 卡片交互状态管理
 * 处理卡片的选中、详细展示等交互逻辑
 */
function createCardInteractionStore() {
  const { subscribe, update, set } = writable(initialState);

  return {
    subscribe,

    // 选中一个卡片
    selectCard: (event: MarketEvent) => {
      update((state) => ({
        ...state,
        selectedEventId: event.id,
        selectedEvent: event,
      }));
    },

    // 取消选中
    clearSelection: () => {
      update((state) => ({
        ...state,
        selectedEventId: null,
        selectedEvent: null,
      }));
    },

    // 显示详细模态框
    showDetailModal: (event: MarketEvent) => {
      update((state) => ({
        ...state,
        selectedEventId: event.id,
        selectedEvent: event,
        showDetailModal: true,
      }));
    },

    // 隐藏详细模态框
    hideDetailModal: () => {
      update((state) => ({
        ...state,
        showDetailModal: false,
      }));
    },

    // 切换卡片选中状态
    toggleCard: (event: MarketEvent) => {
      update((state) => {
        if (state.selectedEventId === event.id) {
          // 如果已选中，则取消选中
          return {
            ...state,
            selectedEventId: null,
            selectedEvent: null,
          };
        } else {
          // 如果未选中，则选中
          return {
            ...state,
            selectedEventId: event.id,
            selectedEvent: event,
          };
        }
      });
    },

    // 重置状态
    reset: () => {
      set(initialState);
    },
  };
}

// 创建并导出 store 实例
export const cardInteractionStore = createCardInteractionStore();
