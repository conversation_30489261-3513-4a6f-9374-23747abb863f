// src/lib/stores/ui/tabs.ts
import type { Component } from 'svelte';
import { get, type Writable, writable } from 'svelte/store';

// 标签页接口定义
export interface Tab {
  id: string; // 唯一标识符，可以使用 href 或生成 UUID
  label: string;
  href: string;
  icon?: Component; // 可选的图标组件
  closeable: boolean; // 是否可关闭
  badge?: number; // 可选的徽章数字，用于显示未读消息等
}

// 标签页管理状态
export const tabs: Writable<Tab[]> = writable([]);
export const activeTabId: Writable<string | null> = writable(null);

// 打开或激活标签页
export const openTab = (navItem: { href: string; label: string; icon?: Component }) => {
  tabs.update((currentTabs) => {
    const existingTab = currentTabs.find((tab) => tab.href === navItem.href);

    if (existingTab) {
      // 如果标签页已存在，则激活它
      activeTabId.set(existingTab.id);
      return currentTabs;
    } else {
      // 否则，创建新标签页并添加
      const newTab: Tab = {
        id: navItem.href, // 使用 href 作为 id，简化去重逻辑
        label: navItem.label,
        href: navItem.href,
        icon: navItem.icon,
        closeable: true, // 默认可关闭
      };
      const updatedTabs = [...currentTabs, newTab];
      activeTabId.set(newTab.id); // 激活新标签页
      return updatedTabs;
    }
  });
};

// 关闭标签页
export const closeTab = (tabId: string) => {
  tabs.update((currentTabs) => {
    const tabIndexToClose = currentTabs.findIndex((tab) => tab.id === tabId);

    if (tabIndexToClose === -1) {
      return currentTabs; // 标签页不存在
    }

    const closingTab = currentTabs[tabIndexToClose];
    const updatedTabs = currentTabs.filter((tab) => tab.id !== tabId);
    const currentActiveTabId = get(activeTabId);

    // 检查是否关闭的是实时消息标签页，如果是则通知相关服务
    if (closingTab.href === '/query/real-time-messages') {
      // 延迟执行，确保标签页已经从数组中移除
      setTimeout(() => {
        // 动态导入避免循环依赖
        import('../features/realTimeMessages').then(({ realTimeMessagesStore }) => {
          realTimeMessagesStore.stopTabAwarePolling();
        });
      }, 0);
    }

    if (currentActiveTabId === tabId) {
      // 如果关闭的是当前激活的标签页
      let newActiveTabId: string | null = null;

      // 尝试激活左侧标签页
      if (tabIndexToClose > 0) {
        newActiveTabId = currentTabs[tabIndexToClose - 1].id;
      }
      // 尝试激活右侧标签页 (如果左侧没有或左侧是关闭的标签页本身)
      else if (updatedTabs.length > 0) {
        newActiveTabId = updatedTabs[0].id;
      }

      activeTabId.set(newActiveTabId);
    }

    return updatedTabs;
  });
};

// 设置激活标签页
export const setActiveTab = (tabId: string) => {
  const currentTabs = get(tabs);
  const tabExists = currentTabs.some((tab) => tab.id === tabId);
  if (tabExists) {
    activeTabId.set(tabId);
  }
};

// 更新标签页徽章
export const updateTabBadge = (href: string, badge: number) => {
  tabs.update((currentTabs) => {
    return currentTabs.map((tab) => {
      if (tab.href === href) {
        return { ...tab, badge };
      }
      return tab;
    });
  });
};
