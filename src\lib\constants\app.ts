/**
 * 应用级常量定义
 *
 * @category Constants
 */

/**
 * 应用信息常量
 */
export const APP_INFO = {
  NAME: 'Svelte Demo',
  VERSION: '1.0.0',
  DESCRIPTION: '数据可视化仪表板',
  AUTHOR: 'Svelte Demo Team',
  REPOSITORY: 'https://github.com/example/svelte-demo',
  HOMEPAGE: 'https://svelte-demo.example.com',
  BUILD_DATE: new Date().toISOString(),
} as const;

/**
 * 路由常量
 */
export const ROUTES = {
  HOME: '/',
  DASHBOARD: '/dashboard',
  LIQUIDATION: '/liquidation',
  DATA_QUERY: '/query',
  SETTINGS: '/settings',
  ABOUT: '/about',
  ERROR: '/error',
  NOT_FOUND: '/404',
} as const;

/**
 * 存储键常量
 */
export const STORAGE_KEYS = {
  // 主题相关
  THEME: 'app-theme',
  THEME_MODE: 'theme-mode',

  // 用户偏好
  USER_PREFERENCES: 'user-preferences',
  SIDEBAR_COLLAPSED: 'sidebar-collapsed',
  SELECTED_TAB: 'selected-tab',
  LANGUAGE: 'language',
  TIMEZONE: 'timezone',

  // 数据缓存
  DASHBOARD_CACHE: 'dashboard-cache',
  LIQUIDATION_CACHE: 'liquidation-cache',
  CHART_CONFIG_CACHE: 'chart-config-cache',
  API_CACHE: 'api-cache',

  // 应用状态
  LAST_VISIT: 'last-visit',
  SESSION_ID: 'session-id',
  ERROR_REPORTS: 'error-reports',
  PERFORMANCE_METRICS: 'performance-metrics',

  // 功能状态
  ONBOARDING_COMPLETED: 'onboarding-completed',
  FEATURE_FLAGS: 'feature-flags',
  NOTIFICATIONS_SETTINGS: 'notifications-settings',
} as const;

/**
 * 事件名称常量
 */
export const EVENTS = {
  // 应用生命周期
  APP_READY: 'app-ready',
  APP_ERROR: 'app-error',
  APP_OFFLINE: 'app-offline',
  APP_ONLINE: 'app-online',

  // 主题事件
  THEME_CHANGED: 'theme-changed',
  THEME_TOGGLE: 'theme-toggle',

  // 数据事件
  DATA_LOADED: 'data-loaded',
  DATA_ERROR: 'data-error',
  DATA_REFRESH: 'data-refresh',
  DATA_CACHE_UPDATED: 'data-cache-updated',

  // 图表事件
  CHART_READY: 'chart-ready',
  CHART_CLICK: 'chart-click',
  CHART_RESIZE: 'chart-resize',
  CHART_ERROR: 'chart-error',

  // 用户交互事件
  SIDEBAR_TOGGLE: 'sidebar-toggle',
  TAB_CHANGE: 'tab-change',
  MODAL_OPEN: 'modal-open',
  MODAL_CLOSE: 'modal-close',
  SEARCH_QUERY: 'search-query',

  // 系统事件
  ERROR_OCCURRED: 'error-occurred',
  PERFORMANCE_METRIC: 'performance-metric',
  NETWORK_STATUS_CHANGED: 'network-status-changed',

  // 通知事件
  NOTIFICATION_SHOW: 'notification-show',
  NOTIFICATION_HIDE: 'notification-hide',
  NOTIFICATION_CLEAR: 'notification-clear',
} as const;

/**
 * 错误代码常量
 */
export const ERROR_CODES = {
  // 网络错误
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  ABORT_ERROR: 'ABORT_ERROR',
  CONNECTION_ERROR: 'CONNECTION_ERROR',

  // API 错误
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',
  SERVER_ERROR: 'SERVER_ERROR',
  BAD_REQUEST: 'BAD_REQUEST',
  RATE_LIMITED: 'RATE_LIMITED',

  // 数据错误
  INVALID_DATA: 'INVALID_DATA',
  MISSING_DATA: 'MISSING_DATA',
  DATA_TOO_LARGE: 'DATA_TOO_LARGE',
  PARSE_ERROR: 'PARSE_ERROR',

  // 应用错误
  COMPONENT_ERROR: 'COMPONENT_ERROR',
  STORE_ERROR: 'STORE_ERROR',
  CHART_ERROR: 'CHART_ERROR',
  INITIALIZATION_ERROR: 'INITIALIZATION_ERROR',

  // 用户错误
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  PERMISSION_ERROR: 'PERMISSION_ERROR',
  QUOTA_EXCEEDED: 'QUOTA_EXCEEDED',
  SESSION_EXPIRED: 'SESSION_EXPIRED',

  // 系统错误
  MEMORY_ERROR: 'MEMORY_ERROR',
  STORAGE_ERROR: 'STORAGE_ERROR',
  BROWSER_NOT_SUPPORTED: 'BROWSER_NOT_SUPPORTED',
} as const;

/**
 * 消息类型常量
 */
export const MESSAGE_TYPES = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info',
  LOADING: 'loading',
} as const;

/**
 * 数据格式常量
 */
export const DATA_FORMATS = {
  JSON: 'json',
  CSV: 'csv',
  EXCEL: 'xlsx',
  PDF: 'pdf',
  PNG: 'png',
  SVG: 'svg',
  XML: 'xml',
  TSV: 'tsv',
} as const;

/**
 * 键盘快捷键常量
 */
export const KEYBOARD_SHORTCUTS = {
  // 全局快捷键
  TOGGLE_SIDEBAR: 'ctrl+b',
  TOGGLE_THEME: 'ctrl+shift+t',
  SEARCH: 'ctrl+k',
  REFRESH: 'f5',
  HELP: 'f1',

  // 导航快捷键
  GO_TO_DASHBOARD: 'ctrl+1',
  GO_TO_LIQUIDATION: 'ctrl+2',
  GO_TO_DATA_QUERY: 'ctrl+3',
  GO_TO_SETTINGS: 'ctrl+4',

  // 操作快捷键
  SAVE: 'ctrl+s',
  EXPORT: 'ctrl+e',
  PRINT: 'ctrl+p',
  COPY: 'ctrl+c',
  PASTE: 'ctrl+v',
  UNDO: 'ctrl+z',
  REDO: 'ctrl+y',

  // 图表快捷键
  ZOOM_IN: 'ctrl+plus',
  ZOOM_OUT: 'ctrl+minus',
  RESET_ZOOM: 'ctrl+0',
  FULLSCREEN: 'f11',
} as const;

/**
 * 限制相关常量
 */
export const LIMITS = {
  // 文件大小（字节）
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_IMAGE_SIZE: 5 * 1024 * 1024, // 5MB
  MAX_CSV_SIZE: 50 * 1024 * 1024, // 50MB

  // 文本长度
  MAX_TITLE_LENGTH: 100,
  MAX_DESCRIPTION_LENGTH: 500,
  MAX_COMMENT_LENGTH: 1000,
  MAX_SEARCH_LENGTH: 200,

  // 数据量
  MAX_CHART_DATA_POINTS: 10000,
  MAX_TABLE_ROWS: 1000,
  MAX_EXPORT_ROWS: 100000,
  MAX_BATCH_SIZE: 100,

  // 请求
  MAX_CONCURRENT_REQUESTS: 5,
  MAX_RETRY_ATTEMPTS: 3,
  MAX_UPLOAD_FILES: 10,

  // 缓存
  MAX_CACHE_SIZE: 100,
  MAX_ERROR_CACHE: 50,
  MAX_LOG_ENTRIES: 1000,

  // 时间
  MAX_SESSION_DURATION: 24 * 60 * 60 * 1000, // 24小时
  MAX_IDLE_TIME: 30 * 60 * 1000, // 30分钟
} as const;

/**
 * 默认值常量
 */
export const DEFAULTS = {
  // 分页
  PAGE: 1,
  PAGE_SIZE: 20,

  // 图表
  CHART_HEIGHT: 400,
  ANIMATION_DURATION: 1000,

  // 时间范围
  TIME_RANGE: '24h',
  REFRESH_INTERVAL: 30000, // 30秒

  // 主题
  THEME: 'system',

  // 语言和地区
  LOCALE: 'zh-CN',
  TIMEZONE: 'Asia/Shanghai',
  CURRENCY: 'CNY',

  // 数据格式
  DATE_FORMAT: 'YYYY-MM-DD',
  TIME_FORMAT: 'HH:mm:ss',
  DATETIME_FORMAT: 'YYYY-MM-DD HH:mm:ss',

  // 数字格式
  DECIMAL_PLACES: 2,
  THOUSAND_SEPARATOR: ',',
  DECIMAL_SEPARATOR: '.',
} as const;

/**
 * 正则表达式常量
 */
export const REGEX = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  URL: /^https?:\/\/.+/,
  PHONE: /^\+?[\d\s-()]+$/,
  NUMBER: /^\d+$/,
  DECIMAL: /^\d+(\.\d+)?$/,
  CURRENCY: /^\d+(\.\d{1,2})?$/,
  DATE_ISO: /^\d{4}-\d{2}-\d{2}$/,
  DATETIME_ISO: /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/,
  HEX_COLOR: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
  IPV4: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
  UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
} as const;

/**
 * 元数据常量
 */
export const META = {
  // SEO
  TITLE: 'Svelte Demo - 数据可视化仪表板',
  DESCRIPTION: '专业的加密货币清算数据可视化分析系统',
  KEYWORDS: 'svelte, dashboard, visualization, cryptocurrency, liquidation, charts',

  // Open Graph
  OG_TYPE: 'website',
  OG_SITE_NAME: 'Svelte Demo',
  OG_LOCALE: 'zh_CN',

  // Twitter Card
  TWITTER_CARD: 'summary_large_image',
  TWITTER_SITE: '@svelte_demo',

  // 图标
  FAVICON: '/favicon.ico',
  APPLE_TOUCH_ICON: '/apple-touch-icon.png',
  MANIFEST: '/manifest.json',

  // 主题色
  THEME_COLOR: '#3b82f6',
  BACKGROUND_COLOR: '#ffffff',
} as const;
