// src/lib/services/api/dashboard.ts
import type { ApiResponse, DashboardStats } from '$lib/types';

import { BaseApiService } from './base';

/**
 * 仪表板数据 API 服务
 */
export class DashboardApiService extends BaseApiService {
  /**
   * 获取仪表板统计数据
   */
  async getStats(): Promise<ApiResponse<DashboardStats>> {
    return this.get<DashboardStats>('/dashboard/stats');
  }

  /**
   * 获取仪表板概览数据
   */
  async getOverview(): Promise<ApiResponse<unknown>> {
    return this.get<unknown>('/dashboard/overview');
  }
}

// 创建默认实例
export const dashboardApi = new DashboardApiService();
