// 图表颜色工具函数 - 支持主题切换
import { mode } from 'mode-watcher';

import { CHART_COLORS } from '$lib/constants/charts';

import { themeLogger } from './logger';

/**
 * 检测当前是否为暗色主题
 * 使用 mode-watcher 的 API
 */
function isDarkTheme(): boolean {
  if (typeof window === 'undefined') return false;
  return mode.current === 'dark';
}

/**
 * 根据主题获取图表颜色
 */
function getThemeColors() {
  const isDark = isDarkTheme();

  if (isDark) {
    // 暗色主题颜色 - 更亮更鲜艳
    return {
      chart1: '#60a5fa', // 亮蓝色
      chart2: '#34d399', // 亮绿色
      chart3: '#fbbf24', // 亮黄色
      chart4: '#f87171', // 亮红色
      chart5: '#c084fc', // 亮紫色
    };
  } else {
    // 浅色主题颜色 - 更深更沉稳
    return {
      chart1: '#2563eb', // 深蓝色
      chart2: '#059669', // 深绿色
      chart3: '#d97706', // 深黄色
      chart4: '#dc2626', // 深红色
      chart5: '#7c3aed', // 深紫色
    };
  }
}

/**
 * 获取当前主题下的图表颜色映射
 */
export function getChartColorMap(): Record<string, string> {
  // 检查是否在浏览器环境
  if (typeof window === 'undefined') {
    // 服务端渲染时返回默认颜色
    return {
      多头: '#059669',
      空头: '#dc2626',
      主流币: '#2563eb',
      山寨币: '#7c3aed',
      '主流币-多头': '#2563eb',
      '主流币-空头': '#7c3aed',
      '山寨币-多头': '#059669',
      '山寨币-空头': '#dc2626',
      Major: '#2563eb',
      Altcoin: '#7c3aed',
      小订单: '#d97706',
      中订单: '#059669',
      大订单: '#dc2626',
      超大订单: '#7c3aed',
      '10000': '#d97706',
      '50000': '#059669',
      '100000': '#dc2626',
      '500000': '#7c3aed',
      '1000000': '#2563eb',
    };
  }

  // 获取主题相关的颜色
  const colors = getThemeColors();

  return {
    // 多空颜色
    多头: colors.chart2, // 绿色系
    空头: colors.chart4, // 红色系

    // 币种类型颜色
    主流币: colors.chart1, // 蓝色系
    山寨币: colors.chart5, // 紫色系

    // 组合颜色
    '主流币-多头': colors.chart1,
    '主流币-空头': colors.chart5,
    '山寨币-多头': colors.chart2,
    '山寨币-空头': colors.chart4,

    // 英文映射
    Major: colors.chart1,
    Altcoin: colors.chart5,

    // 订单大小颜色
    小订单: colors.chart3,
    中订单: colors.chart2,
    大订单: colors.chart4,
    超大订单: colors.chart5,

    // 阈值颜色
    '10000': colors.chart3,
    '50000': colors.chart2,
    '100000': colors.chart4,
    '500000': colors.chart5,
    '1000000': colors.chart1,
  };
}

/**
 * 获取当前主题下的默认颜色数组
 */
export function getDefaultColors(): string[] {
  if (typeof window === 'undefined') {
    return [
      '#2563eb',
      '#059669',
      '#dc2626',
      '#7c3aed',
      '#d97706',
      '#0891b2',
      '#ea580c',
      '#8b5cf6',
      '#e11d48',
      '#65a30d',
    ];
  }

  const colors = getThemeColors();

  return [
    colors.chart1,
    colors.chart2,
    colors.chart3,
    colors.chart4,
    colors.chart5,
    // 额外的颜色变体
    isDarkTheme() ? '#0891b2' : '#0369a1', // 青色
    isDarkTheme() ? '#ea580c' : '#c2410c', // 橙色
    isDarkTheme() ? '#8b5cf6' : '#7c3aed', // 紫色变体
    isDarkTheme() ? '#e11d48' : '#be123c', // 粉红色
    isDarkTheme() ? '#65a30d' : '#4d7c0f', // 绿色变体
  ];
}

/**
 * 监听主题变化并更新图表
 * 使用 mode-watcher 的响应式机制
 */
export function createThemeObserver(callback: () => void): () => void {
  let debounceTimer: number | null = null;
  let lastMode = mode.current;
  let isProcessing = false; // 防止重入

  // 防抖函数，避免频繁触发
  const debouncedCallback = () => {
    if (isProcessing) return; // 防止重入

    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }

    debounceTimer = window.setTimeout(() => {
      const currentMode = mode.current;
      // 只有当主题真正发生变化时才执行回调
      if (currentMode !== lastMode && !isProcessing) {
        themeLogger.info('Theme change detected', {
          previousTheme: lastMode,
          currentTheme: currentMode,
          changeType: 'mode-watcher',
        });
        isProcessing = true;
        lastMode = currentMode;

        try {
          const startTime = performance.now();
          callback();
          const endTime = performance.now();
          const duration = endTime - startTime;

          themeLogger.debug('Theme callback completed', {
            duration: `${duration.toFixed(2)}ms`,
            durationMs: duration,
          });
        } catch (error) {
          themeLogger.error('Theme observer callback error', {
            error: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack : undefined,
          });
        } finally {
          isProcessing = false;
        }
      }
      debounceTimer = null;
    }, 150); // 增加防抖延迟到150ms
  };

  const observer = new MutationObserver((mutations) => {
    // 检查是否有实际的主题相关变化
    let hasThemeChange = false;

    mutations.forEach((mutation) => {
      if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
        const target = mutation.target as HTMLElement;
        if (target === document.documentElement) {
          const classList = target.classList;
          const hasDarkClass = classList.contains('dark');
          const expectedMode = hasDarkClass ? 'dark' : 'light';

          // 只有当检测到的模式与上次记录的模式不同时才标记为有变化
          if (expectedMode !== lastMode) {
            hasThemeChange = true;
          }
        }
      }
    });

    // 只有确实有主题变化时才触发防抖回调
    if (hasThemeChange) {
      debouncedCallback();
    }
  });

  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['class'],
  });

  // 返回清理函数
  return () => {
    observer.disconnect();
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }
    isProcessing = false;
  };
}

/**
 * 兼容性函数：为了保持向后兼容，保留 MutationObserver 版本
 * @deprecated 建议使用新的 createThemeObserver 函数
 */
export function createLegacyThemeObserver(callback: () => void): MutationObserver {
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
        // 检查是否是主题相关的类变化
        const target = mutation.target as HTMLElement;
        if (target === document.documentElement) {
          const classList = target.classList;
          const currentTheme = classList.contains('dark') ? 'dark' : 'light';

          themeLogger.info('Theme change detected (legacy observer)', {
            theme: currentTheme,
            changeType: 'mutation-observer',
            classList: Array.from(classList),
          });

          // 延迟执行回调，确保 CSS 变量已更新
          setTimeout(callback, 50);
        }
      }
    });
  });

  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['class'],
  });

  return observer;
}
