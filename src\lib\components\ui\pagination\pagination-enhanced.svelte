<script lang="ts">
  import ChevronLeftIcon from '@lucide/svelte/icons/chevron-left';
  import ChevronRightIcon from '@lucide/svelte/icons/chevron-right';

  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input';
  import { Label } from '$lib/components/ui/label';

  import * as Pagination from './index.js';

  // Props - 保持与原组件兼容的API
  export let currentPage: number;
  export let totalPages: number;
  export let totalItems: number;
  export let itemsPerPage: number;
  export let loading: boolean = false;

  // Events - Svelte 5 style
  export let onPageChange: (page: number) => void = () => {};
  export const onItemsPerPageChange: (itemsPerPage: number) => void = () => {};

  // 内部状态
  let jumpToPage = currentPage;

  // 计算总数量（shadcn-svelte pagination需要的格式）
  $: count = totalItems;
  $: perPage = itemsPerPage;

  // 处理页面变化
  function handlePageChange(newPage: number) {
    if (newPage >= 1 && newPage <= totalPages && !loading) {
      onPageChange(newPage);
    }
  }

  // 处理跳转页面输入
  function handleJumpToPage() {
    if (jumpToPage >= 1 && jumpToPage <= totalPages && !loading) {
      onPageChange(jumpToPage);
    }
  }

  // 处理输入框变化
  function handleInputChange(event: Event) {
    const input = event.target as HTMLInputElement;
    const value = parseInt(input.value);
    if (!isNaN(value)) {
      jumpToPage = value;
    }
  }

  // 计算显示范围
  $: startItem = (currentPage - 1) * itemsPerPage + 1;
  $: endItem = Math.min(currentPage * itemsPerPage, totalItems);
</script>

<div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
  <!-- 显示信息 -->
  <div class="text-muted-foreground text-sm">
    显示 {startItem} 到 {endItem} 条，共 {totalItems} 条
  </div>

  <!-- 分页控件 -->
  <Pagination.Root {count} {perPage} bind:page={currentPage} siblingCount={1}>
    {#snippet children({ pages, currentPage: current })}
      <Pagination.Content>
        <Pagination.Item>
          <Pagination.PrevButton
            disabled={currentPage === 1 || loading}
            onclick={() => handlePageChange(currentPage - 1)}
          >
            <ChevronLeftIcon class="h-4 w-4" />
            <span class="hidden sm:block">上一页</span>
          </Pagination.PrevButton>
        </Pagination.Item>

        {#each pages as pageItem (pageItem.key)}
          {#if pageItem.type === 'ellipsis'}
            <Pagination.Item>
              <Pagination.Ellipsis />
            </Pagination.Item>
          {:else}
            <Pagination.Item>
              <Pagination.Link
                page={pageItem}
                isActive={current === pageItem.value}
                disabled={loading}
                onclick={() => handlePageChange(pageItem.value)}
              >
                {pageItem.value}
              </Pagination.Link>
            </Pagination.Item>
          {/if}
        {/each}

        <Pagination.Item>
          <Pagination.NextButton
            disabled={currentPage === totalPages || loading}
            onclick={() => handlePageChange(currentPage + 1)}
          >
            <span class="hidden sm:block">下一页</span>
            <ChevronRightIcon class="h-4 w-4" />
          </Pagination.NextButton>
        </Pagination.Item>
      </Pagination.Content>
    {/snippet}
  </Pagination.Root>

  <!-- 跳转控件 -->
  <div class="flex items-center gap-2">
    <Label for="jump-to-page" class="text-muted-foreground text-sm">跳转到</Label>
    <Input
      id="jump-to-page"
      type="number"
      min="1"
      max={totalPages}
      value={jumpToPage.toString()}
      oninput={handleInputChange}
      disabled={loading}
      class="h-8 w-16"
    />
    <Button
      size="sm"
      onclick={handleJumpToPage}
      disabled={loading || jumpToPage < 1 || jumpToPage > totalPages}
    >
      Go
    </Button>
  </div>
</div>
