<script lang="ts">
  import type { EChartsOption, ScatterSeriesOption } from 'echarts';
  import * as echarts from 'echarts';
  import { createEventDispatcher, onDestroy, onMount } from 'svelte';

  import type { ScatterChartItem } from '$lib/types';

  const dispatch = createEventDispatcher();

  export let options: EChartsOption = {};
  export let data: ScatterChartItem[] = [];
  export let title: string = 'User Behavior Analysis';
  export let subtitle: string = '';

  let chartDom: HTMLDivElement;
  let myChart: echarts.ECharts;

  const baseOptions: EChartsOption = {
    title: {
      text: title,
      subtext: subtitle,
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        animation: false,
        label: {
          backgroundColor: '#505765',
        },
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '8%',
      top: '15%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      splitLine: {
        lineStyle: {
          type: 'dashed',
        },
      },
    },
    yAxis: {
      type: 'value',
      splitLine: {
        lineStyle: {
          type: 'dashed',
        },
      },
    },
  };

  let chartInitialized = false;

  function initializeChart() {
    if (chartInitialized) return;
    myChart = echarts.init(chartDom);
    updateChart();
    chartInitialized = true;

    myChart.on('click', (params) => {
      dispatch('chartClick', { chartType: 'scatter', name: params.name, value: params.value });
    });
  }

  function updateChart() {
    if (!myChart) return;

    // 按 category 分组数据
    const groupedData = new Map<string, Array<[number, number, string]>>();

    data.forEach((item) => {
      const category = item.category || '默认';
      if (!groupedData.has(category)) {
        groupedData.set(category, []);
      }
      // 添加 name 信息用于 tooltip
      groupedData.get(category)!.push([item.x, item.y, item.name]);
    });

    // 定义颜色方案
    const colors = [
      '#5470c6',
      '#91cc75',
      '#fac858',
      '#ee6666',
      '#73c0de',
      '#3ba272',
      '#fc8452',
      '#9a60b4',
    ];

    // 为每个 category 创建一个系列
    const series: ScatterSeriesOption[] = Array.from(groupedData.entries()).map(
      ([category, categoryData], index) => ({
        name: `类别 ${category}`,
        type: 'scatter',
        data: categoryData,
        symbolSize: 10,
        itemStyle: {
          color: colors[index % colors.length],
          opacity: 0.8,
        },
        emphasis: {
          focus: 'series',
          itemStyle: {
            opacity: 1,
            borderColor: '#333',
            borderWidth: 2,
          },
        },
      })
    );

    myChart.setOption(
      {
        ...baseOptions,
        ...options,
        color: colors,
        legend: {
          show: true,
          top: '10%',
          left: 'center',
          textStyle: {
            fontSize: 12,
            color: '#666',
          },
          itemGap: 20,
          icon: 'circle',
        },
        tooltip: {
          trigger: 'item',
          formatter: (params: any) => {
            const [x, y, name] = params.value;
            return `${params.seriesName}<br/>${name}<br/>X: ${x}<br/>Y: ${y}`;
          },
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '8%',
          top: '20%', // 为图例留出更多空间
          containLabel: true,
        },
        series: series,
      },
      { notMerge: false }
    );
  }

  onMount(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            initializeChart();
            observer.unobserve(chartDom);
          }
        });
      },
      { threshold: 0.1 }
    );

    observer.observe(chartDom);

    const resizeChart = () => {
      if (myChart) {
        myChart.resize();
      }
    };

    window.addEventListener('resize', resizeChart);

    onDestroy(() => {
      window.removeEventListener('resize', resizeChart);
      if (myChart) {
        myChart.dispose();
      }
    });
  });

  $: if (myChart && chartInitialized && (data || options)) {
    updateChart();
  }
</script>

<div
  bind:this={chartDom}
  style="width: 100%; height: 100%;"
  aria-label="{title} - {subtitle}"
></div>
