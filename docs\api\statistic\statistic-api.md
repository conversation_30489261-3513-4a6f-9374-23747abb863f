# 统计 API

## GET /historical/query

### 描述

查询历史数据。

### 参数

| 参数名      | 类型     | 描述                 | 是否必需             |
| ----------- | -------- | -------------------- | -------------------- |
| `coin`      | `String` | 币种ID列表，例如 `01HAF64YM82JQN8XSGDZ0ZJP7A,01HAF64YMSYF7BPH8JJRWVATT5`   | coin/base 至少提供一个        |
| `base`      | `String` | 币种名称列表，例如 `BTC,ETH` | coin/base 至少提供一个       |
| `startDate` | `String` | 查询的起始日期 (UTC)，格式：`yyyy-MM-dd` | 是                   |
| `endDate`   | `String` | 查询的结束日期 (UTC)，格式：`yyyy-MM-dd` | 是                   |
| `type`      | `String` | 查询类型，枚举值：`LIQUIDATION_ORDER`, `TWAP` | 是                   |
| `threshold` | `Number` | 阈值，枚举值：`0`, `1`, `2`, `4` | 否                   |

### 示例请求

```
GET /historical/query?base=BTC,ETH&startDate=2025-08-01&endDate=2025-08-31&type=LIQUIDATION_ORDER&threshold=1
```

### 示例响应

```json
{
  "code": 1000,
  "message": "success",
  "data": [
    {
        "date": "2025-08-07",
        "amount": {
            "BTC": {
                "long": 10, // 空头爆仓金额 side = 1
                "short": 20, // 多头爆仓金额 side = 2
            },
            "ETH": {
                "long": 10,
                "short": 20,
            },
        }
    },
  ]
}
```
