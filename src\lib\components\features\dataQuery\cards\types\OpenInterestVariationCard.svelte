<script lang="ts">
  import { CurrencyIcon } from '$lib/components/features/ui';
  import { Card, CardContent } from '$lib/components/ui/card';

  // 本地实现 getParameterValue 函数
  function getParameterValue(
    event: any,
    parameterId: string
  ): string | number | boolean | undefined {
    const parameter = event.parameters.find((p: any) => p.parameterId === parameterId);
    return parameter?.value;
  }

  import {
    type BaseEventCardProps,
    formatPercentage,
    formatPrice,
    formatTimeDiff,
    formatUsdAmount,
  } from '../utils';

  interface Props extends BaseEventCardProps {}

  const { event, isHighlighted = false, onCardClick }: Props = $props();

  // 提取持仓量变化相关参数
  const base = getParameterValue(event, 'base') as string;
  const exchangeLabel = getParameterValue(event, 'exchangeLabel') as string;
  const direction = getParameterValue(event, 'direction') as number;
  const oiVariationUsd = getParameterValue(event, 'oiVariationUsd') as number;
  const variationPercent = getParameterValue(event, 'variationPercent') as number;
  const currentOiValueUsd = getParameterValue(event, 'currentOiValueUsd') as number;
  const priceUsd = getParameterValue(event, 'priceUsd') as number;
  const timeframe = getParameterValue(event, 'timeframe') as string;
  const aggregated = getParameterValue(event, 'aggregated') as boolean;

  // 获取变化方向和颜色
  const isIncrease = direction === 1;
  const changeText = isIncrease ? 'increased' : 'decreased';
  const changeColor = isIncrease
    ? 'text-green-600 dark:text-green-400'
    : 'text-red-600 dark:text-red-400';

  // 格式化时间差
  const timeAgo = formatTimeDiff(event.date);

  // 卡片样式 - 持仓量变化 (OPEN_INTEREST_VARIATION): 20rem - 最复杂布局
  const cardClasses = `
    transition-all duration-200 w-full bg-card
    min-w-48
    ${isHighlighted ? 'ring-primary border-primary/50 shadow-lg ring-2' : 'hover:shadow-md'}
    ${onCardClick ? 'cursor-pointer' : ''}
  `;

  // 处理卡片点击
  function handleCardClick() {
    onCardClick?.(event);
  }
</script>

<Card
  class={cardClasses}
  onclick={onCardClick ? handleCardClick : undefined}
  role={onCardClick ? 'button' : undefined}
  tabindex={onCardClick ? 0 : undefined}
>
  <CardContent class="space-y-3 p-4">
    <!-- 头部：币种图标、名称和时间 -->
    <div class="flex items-center space-x-2">
      <!-- 币种图标 -->
      <CurrencyIcon
        currencyId={event.currency || ''}
        symbol={base}
        size="size-6"
        class="flex-shrink-0"
      />

      <!-- 币种名称和时间 -->
      <div class="flex min-w-0 flex-1 items-center space-x-1">
        <span class="text-foreground truncate text-sm font-semibold">
          {base || '未知币种'}
        </span>
        <span class="text-muted-foreground text-xs">•</span>
        <span class="text-muted-foreground text-xs whitespace-nowrap">
          {timeAgo}
        </span>
      </div>
    </div>

    <!-- 描述文本 -->
    <div class="text-foreground text-sm">
      Open interest {changeText} on
      <span class="font-medium">{exchangeLabel || '未知交易所'}</span>
    </div>

    <!-- 主要数据区域 - 并排布局 -->
    <div class="space-y-2">
      <!-- 标签行：Open interest 和 Price 标签对齐 -->
      <div class="grid grid-cols-1 gap-3 sm:grid-cols-2">
        <!-- 左侧标签 -->
        <div class="text-muted-foreground text-xs">
          {#if aggregated || timeframe}
            {#if aggregated}
              Aggregated Open interest
            {:else}
              Open interest
            {/if}
            {#if timeframe}
              ({timeframe})
            {/if}
          {:else}
            Open interest
          {/if}
        </div>

        <!-- 右侧标签 -->
        <div class="text-muted-foreground text-xs">Price</div>
      </div>

      <!-- 数据行：持仓量和价格数据对齐 -->
      <div class="grid grid-cols-1 gap-3 sm:grid-cols-2">
        <!-- 左侧：持仓量数据 -->
        <div class="space-y-1">
          <div class="text-foreground text-lg font-bold">
            {formatUsdAmount(currentOiValueUsd)}
          </div>
          <!-- 变化百分比和金额 -->
          <div class={`text-sm font-medium ${changeColor}`}>
            {isIncrease ? '+' : ''}{formatPercentage(variationPercent)}
            ({isIncrease ? '+' : ''}{formatUsdAmount(Math.abs(oiVariationUsd))})
          </div>
        </div>

        <!-- 右侧：价格数据 -->
        <div class="space-y-1">
          <div class="text-foreground font-medium">
            ${formatPrice(priceUsd)}
          </div>
        </div>
      </div>
    </div>
  </CardContent>
</Card>
