<script lang="ts">
  import type { MarketEvent } from '$lib/types';
  import { ClipboardList } from '@lucide/svelte';

  import Timeline from './Timeline.svelte';

  interface Props {
    items: MarketEvent[];
    timelineExtendable?: boolean;
    loadingTimeline?: boolean;
    error?: string | null;
    onExtendTimeline?: () => Promise<void>;
  }

  const {
    items = [],
    timelineExtendable = false,
    loadingTimeline = false,
    error = null,
    onExtendTimeline
  }: Props = $props();

  // 按时间倒序渲染
  const sortedItems: MarketEvent[] = $derived(
    [...items].sort((a, b) => b.date - a.date)
  );
</script>

<div class="w-full">
  {#if sortedItems.length === 0}
    <!-- 空状态 -->
    <div class="flex flex-col items-center justify-center py-16 text-center">
      <div class="bg-muted mb-6 flex h-16 w-16 items-center justify-center rounded-full">
        <ClipboardList class="text-muted-foreground h-8 w-8" />
      </div>
      <h3 class="text-foreground mb-2 text-lg font-semibold">暂无数据</h3>
      <p class="text-muted-foreground max-w-md">
        未找到匹配的查询结果，请调整查询条件或数据源。
      </p>
    </div>
  {:else}
    <!-- 时间轴布局 -->
    <div class="h-full w-full">
      <!-- 时间轴滚动容器（仅滚动，不做自动检测） -->
      <div
        class="scrollbar-thin scrollbar-thumb-border scrollbar-track-transparent overflow-x-hidden overflow-y-auto pr-2"
      >
        <Timeline items={sortedItems} />

        <!-- 时间轴加载状态 -->
        {#if loadingTimeline}
          <div class="flex items-center justify-center py-8">
            <div class="flex items-center space-x-2">
              <div
                class="border-primary h-4 w-4 animate-spin rounded-full border-2 border-t-transparent"
              ></div>
              <span class="text-muted-foreground text-sm">加载中...</span>
            </div>
          </div>
        {:else if timelineExtendable}
          <div class="flex flex-col items-center justify-center py-6 space-y-3">
            <!-- 错误提示 -->
            {#if error}
              <div class="bg-destructive/10 border-destructive/20 text-destructive rounded-lg border px-3 py-2 text-sm">
                {error}
              </div>
            {/if}

            <button
              onclick={async () => {
                try {
                  await onExtendTimeline?.();
                } catch (error) {
                  console.error('手动扩展时间线失败:', error);
                }
              }}
              disabled={loadingTimeline}
              class="text-muted-foreground hover:text-foreground border-border/50 hover:border-border bg-card/50 hover:bg-card rounded-lg border px-4 py-2 text-sm transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {error ? '重试加载' : '加载更多历史数据'}
            </button>
          </div>
        {:else if sortedItems.length > 0}
          <div class="flex items-center justify-center py-6">
            <span class="text-muted-foreground text-sm">已加载全部历史数据（近7天）</span>
          </div>
        {/if}
      </div>
    </div>
  {/if}
  
</div>

