import type { EChartsOption } from 'echarts';

import type { ScatterChartItem } from '$lib/types';

// 模拟柱状图数据
export const barChartData = [
  { name: '衬衫', value: 5 },
  { name: '羊毛衫', value: 20 },
  { name: '雪纺衫', value: 36 },
  { name: '裤子', value: 10 },
  { name: '高跟鞋', value: 10 },
  { name: '袜子', value: 20 },
];

// 模拟折线图数据
export const lineChartData = [
  { name: '周一', value: 120 },
  { name: '周二', value: 200 },
  { name: '周三', value: 150 },
  { name: '周四', value: 80 },
  { name: '周五', value: 70 },
  { name: '周六', value: 110 },
  { name: '周日', value: 130 },
];

// 模拟饼图数据
export const pieChartData = [
  { value: 335, name: '直接访问' },
  { value: 310, name: '邮件营销' },
  { value: 234, name: '联盟广告' },
  { value: 135, name: '视频广告' },
  { value: 1548, name: '搜索引擎' },
];

// 模拟散点图数据
export const scatterChartData: ScatterChartItem[] = [
  { name: 'Point 1', x: 10.0, y: 8.04, category: 'A' },
  { name: 'Point 2', x: 8.0, y: 6.95, category: 'B' },
  { name: 'Point 3', x: 13.0, y: 7.58, category: 'A' },
  { name: 'Point 4', x: 9.0, y: 8.81, category: 'B' },
  { name: 'Point 5', x: 11.0, y: 8.33, category: 'A' },
  { name: 'Point 6', x: 14.0, y: 9.96, category: 'B' },
  { name: 'Point 7', x: 6.0, y: 7.24, category: 'A' },
  { name: 'Point 8', x: 4.0, y: 4.26, category: 'B' },
  { name: 'Point 9', x: 12.0, y: 10.84, category: 'A' },
  { name: 'Point 10', x: 7.0, y: 4.82, category: 'B' },
  { name: 'Point 11', x: 5.0, y: 5.68, category: 'A' },
];

// 示例：自定义ECharts选项
export const customBarChartOptions: EChartsOption = {
  color: ['#3398DB'],
  series: [
    {
      type: 'bar',
      barWidth: '60%',
      itemStyle: {
        borderRadius: [5, 5, 0, 0],
      },
    },
  ],
};

// 生成新的模拟数据
export function generateMockData() {
  const newBarChartData = barChartData.map((item) => ({
    name: item.name,
    value: Math.floor(Math.random() * 40) + 5,
  }));

  const newLineChartData = lineChartData.map((item) => ({
    name: item.name,
    value: Math.floor(Math.random() * 150) + 50,
  }));

  const newPieChartData = pieChartData.map((item) => ({
    name: item.name,
    value: Math.floor(Math.random() * 500) + 100,
  }));

  const newScatterChartData = scatterChartData.map((item) => ({
    name: item.name,
    x: item.x + (Math.random() - 0.5) * 2,
    y: item.y + (Math.random() - 0.5) * 2,
    category: item.category,
  }));

  return {
    barChartData: newBarChartData,
    lineChartData: newLineChartData,
    pieChartData: newPieChartData,
    scatterChartData: newScatterChartData,
  };
}

export const customLineChartOptions: EChartsOption = {
  series: [
    {
      type: 'line',
      smooth: true,
      areaStyle: {},
    },
  ],
};

export const customPieChartOptions: EChartsOption = {
  series: [
    {
      radius: ['40%', '70%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 2,
      },
      label: {
        show: false,
        position: 'center',
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '20',
          fontWeight: 'bold',
        },
      },
      labelLine: {
        show: false,
      },
    },
  ],
};

export const customScatterChartOptions: EChartsOption = {
  series: [
    {
      symbolSize: 10,
      itemStyle: {
        shadowBlur: 10,
        shadowColor: 'rgba(120, 36, 50, 0.5)',
        shadowOffsetY: 5,
      },
    },
  ],
};
