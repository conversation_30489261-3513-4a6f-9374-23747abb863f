# 组件开发文档

## 概述

Svelte Demo 项目包含完整的组件体系，分为基础 UI 组件、图表组件、功能组件和布局组件四个主要分类。所有组件都基于 TypeScript 开发，支持主题切换，并遵循无障碍设计原则。

## 组件架构

### 组件分层结构

```
组件层级
├── 基础 UI 组件 (ui/)              # 基于 shadcn/ui 的可复用基础组件
│   ├── 表单组件                    # Button, Input, Select 等
│   ├── 数据展示组件                 # Table, Card, Badge 等
│   ├── 反馈组件                    # Alert, Toast, Progress 等
│   └── 导航组件                    # Tabs, Breadcrumb, Pagination 等
├── 图表组件 (charts/)             # 基于 ECharts 的数据可视化组件
│   ├── BaseChart                  # 图表基类
│   ├── BarChart                   # 柱状图
│   ├── LineChart                  # 折线图
│   ├── PieChart                   # 饼图
│   ├── ScatterChart               # 散点图
│   └── ChartPanel                 # 图表容器
├── 功能组件 (features/)           # 业务相关的复合组件
│   ├── dashboard/                 # 仪表板组件
│   ├── liquidation/               # 清算功能组件
│   ├── dataQuery/                 # 数据查询组件
│   └── settings/                  # 设置组件
└── 布局组件 (layout/)             # 应用布局相关组件
    ├── AppSidebar                 # 侧边栏
    ├── TabBar                     # 标签栏
    └── ErrorBoundary              # 错误边界
```

## 基础 UI 组件

基础 UI 组件基于 [shadcn/ui](https://github.com/huntabyte/shadcn-svelte) 构建，提供统一的设计语言和用户体验。

### Button 组件

通用按钮组件，支持多种变体和尺寸。

#### Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|-------|------|
| `variant` | `'default' \| 'destructive' \| 'outline' \| 'secondary' \| 'ghost' \| 'link'` | `'default'` | 按钮样式变体 |
| `size` | `'default' \| 'sm' \| 'lg' \| 'icon'` | `'default'` | 按钮尺寸 |
| `disabled` | `boolean` | `false` | 是否禁用 |
| `href` | `string` | `undefined` | 链接地址（设置后渲染为 a 标签） |
| `type` | `'button' \| 'submit' \| 'reset'` | `'button'` | 按钮类型 |
| `class` | `string` | `''` | 额外的 CSS 类名 |

#### 使用示例

```svelte
<script>
  import { Button } from '$lib/components/ui/button';
</script>

<!-- 基本按钮 -->
<Button>点击我</Button>

<!-- 不同变体 -->
<Button variant="destructive">删除</Button>
<Button variant="outline">取消</Button>
<Button variant="ghost">透明按钮</Button>

<!-- 不同尺寸 -->
<Button size="sm">小按钮</Button>
<Button size="lg">大按钮</Button>

<!-- 链接按钮 -->
<Button href="/dashboard">跳转到仪表板</Button>

<!-- 禁用状态 -->
<Button disabled>禁用按钮</Button>
```

### Card 组件

卡片容器组件，用于内容分组和组织。

#### 子组件

- `Card` - 卡片容器
- `CardHeader` - 卡片头部
- `CardTitle` - 卡片标题
- `CardDescription` - 卡片描述
- `CardContent` - 卡片内容
- `CardFooter` - 卡片底部

#### 使用示例

```svelte
<script>
  import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '$lib/components/ui/card';
  import { Button } from '$lib/components/ui/button';
</script>

<Card>
  <CardHeader>
    <CardTitle>卡片标题</CardTitle>
    <CardDescription>这是卡片的描述信息</CardDescription>
  </CardHeader>
  <CardContent>
    <p>这里是卡片的主要内容。</p>
  </CardContent>
  <CardFooter>
    <Button>操作按钮</Button>
  </CardFooter>
</Card>
```

### Input 组件

输入框组件，支持多种类型和状态。

#### Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|-------|------|
| `type` | `string` | `'text'` | 输入框类型 |
| `value` | `string` | `''` | 输入值 |
| `placeholder` | `string` | `''` | 占位符文本 |
| `disabled` | `boolean` | `false` | 是否禁用 |
| `readonly` | `boolean` | `false` | 是否只读 |
| `class` | `string` | `''` | 额外的 CSS 类名 |

#### 使用示例

```svelte
<script>
  import { Input } from '$lib/components/ui/input';
  import { Label } from '$lib/components/ui/label';
  
  let value = '';
</script>

<div class="space-y-2">
  <Label for="username">用户名</Label>
  <Input id="username" bind:value placeholder="请输入用户名" />
</div>
```

## 图表组件

图表组件基于 ECharts 构建，提供丰富的数据可视化功能。

### 图表组件架构

```
图表组件结构
├── BaseChart                      # 图表基类（抽象）
│   ├── 生命周期管理                 # 初始化、更新、销毁
│   ├── 主题响应                    # 自动响应主题变化
│   ├── 响应式处理                  # 窗口大小变化适配
│   └── 性能优化                    # 懒加载、防抖更新
├── 具体图表实现
│   ├── BarChart                   # 柱状图
│   ├── LineChart                  # 折线图
│   ├── PieChart                   # 饼图
│   └── ScatterChart               # 散点图
└── ChartPanel                     # 图表容器组件
    ├── 标题和配置                  # 图表标题、工具栏
    ├── 拖拽支持                    # 图表重排功能
    └── 加载状态                    # 加载动画和错误处理
```

### BarChart 柱状图组件

用于展示分类数据的数值比较。

#### Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|-------|------|
| `data` | `LiquidationData[] \| BarChartItem[]` | `[]` | 图表数据 |
| `title` | `string` | `'Monthly Sales Data'` | 图表标题 |
| `subtitle` | `string` | `''` | 图表副标题 |
| `groupBy` | `'side' \| 'coinType' \| null` | `null` | 分组方式 |
| `stackPercent` | `boolean` | `false` | 是否显示百分比堆叠 |
| `timeFrame` | `string` | `'1d'` | 时间粒度 (`1h`, `4h`, `1d`, `1w`) |
| `options` | `EChartsOption` | `{}` | ECharts 配置选项 |

#### 事件

- `chartClick` - 图表点击事件，返回点击的数据信息

#### 数据格式

```typescript
// 基础柱状图数据
interface BarChartItem {
  name: string;
  value: number;
}

// 清算数据（支持时间序列和分组）
interface LiquidationData {
  datetime: string;
  side: number; // 1: 多头, -1: 空头
  coinType: string;
  amountUsd: number;
}
```

#### 使用示例

```svelte
<script>
  import { BarChart } from '$lib/components/charts';
  
  // 基础数据
  const simpleData = [
    { name: '1月', value: 120 },
    { name: '2月', value: 200 },
    { name: '3月', value: 150 }
  ];
  
  // 清算数据
  const liquidationData = [
    { datetime: '2024-01-01T10:00:00Z', side: 1, coinType: 'Major', amountUsd: 10000 },
    { datetime: '2024-01-01T11:00:00Z', side: -1, coinType: 'Alt', amountUsd: 15000 }
  ];
  
  function handleChartClick(event) {
    console.log('图表点击:', event.detail);
  }
</script>

<!-- 基础柱状图 -->
<BarChart 
  data={simpleData} 
  title="月度销售数据" 
  on:chartClick={handleChartClick} 
/>

<!-- 清算数据分组展示 -->
<BarChart 
  data={liquidationData}
  title="清算数据分析"
  groupBy="side"
  stackPercent={true}
  timeFrame="1h"
  on:chartClick={handleChartClick}
/>
```

#### 特色功能

1. **多种数据格式支持**: 支持简单的名值对数据和复杂的时间序列数据
2. **灵活分组**: 支持按多空方向、币种类型等维度分组
3. **百分比堆叠**: 可以显示相对比例而非绝对数值
4. **时间粒度**: 支持小时、日、周等不同时间粒度聚合
5. **主题适配**: 自动响应暗色/浅色主题切换
6. **性能优化**: 懒加载、防抖更新、数据变化检测

### LineChart 折线图组件

用于展示数据随时间的变化趋势。

#### Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|-------|------|
| `data` | `LineChartItem[]` | `[]` | 图表数据 |
| `title` | `string` | `'User Growth Trend'` | 图表标题 |
| `subtitle` | `string` | `''` | 图表副标题 |
| `smooth` | `boolean` | `true` | 是否平滑曲线 |
| `showSymbol` | `boolean` | `true` | 是否显示数据点 |
| `options` | `EChartsOption` | `{}` | ECharts 配置选项 |

#### 数据格式

```typescript
interface LineChartItem {
  name: string;
  value: number;
}
```

#### 使用示例

```svelte
<script>
  import { LineChart } from '$lib/components/charts';
  
  const trendData = [
    { name: '1月', value: 100 },
    { name: '2月', value: 132 },
    { name: '3月', value: 101 },
    { name: '4月', value: 134 },
    { name: '5月', value: 90 },
    { name: '6月', value: 230 }
  ];
</script>

<LineChart 
  data={trendData}
  title="用户增长趋势"
  smooth={true}
  showSymbol={false}
/>
```

### PieChart 饼图组件

用于展示数据的占比关系。

#### Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|-------|------|
| `data` | `PieChartItem[]` | `[]` | 图表数据 |
| `title` | `string` | `'Distribution'` | 图表标题 |
| `subtitle` | `string` | `''` | 图表副标题 |
| `showPercentage` | `boolean` | `true` | 是否显示百分比 |
| `radius` | `[string, string]` | `['40%', '70%']` | 内外半径 |
| `options` | `EChartsOption` | `{}` | ECharts 配置选项 |

#### 使用示例

```svelte
<script>
  import { PieChart } from '$lib/components/charts';
  
  const distributionData = [
    { name: '移动端', value: 60 },
    { name: '桌面端', value: 30 },
    { name: '平板', value: 10 }
  ];
</script>

<PieChart 
  data={distributionData}
  title="访问设备分布"
  showPercentage={true}
/>
```

### ChartPanel 图表容器组件

提供图表的统一容器，包含标题、工具栏、加载状态等功能。

#### Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|-------|------|
| `chart` | `ChartConfig` | - | 图表配置对象 |
| `data` | `any[]` | `[]` | 图表数据 |
| `isLoading` | `boolean` | `false` | 是否加载中 |
| `enableDrag` | `boolean` | `false` | 是否启用拖拽 |
| `showHeader` | `boolean` | `true` | 是否显示头部 |

#### 事件

- `chartDrop` - 图表拖拽事件
- `chartClick` - 图表点击事件

#### 使用示例

```svelte
<script>
  import { ChartPanel } from '$lib/components/charts';
  import { BarChart } from '$lib/components/charts';
  
  const chartConfig = {
    id: 'sales-chart',
    component: BarChart,
    title: '销售数据',
    subTitle: '月度统计',
    visible: true,
    order: 0
  };
  
  const salesData = [
    { name: '1月', value: 100 },
    { name: '2月', value: 200 }
  ];
  
  function handleChartDrop(event) {
    console.log('图表拖拽:', event.detail);
  }
</script>

<ChartPanel
  chart={chartConfig}
  data={salesData}
  isLoading={false}
  enableDrag={true}
  on:chartDrop={handleChartDrop}
/>
```

## 功能组件

功能组件是业务逻辑相关的复合组件，集成了多个基础组件来实现特定功能。

### Dashboard 仪表板组件

仪表板主组件，整合统计卡片、图表面板等子组件。

#### 功能特性

- 统计数据展示
- 多图表展示和管理
- 自动刷新配置
- 图表拖拽排序
- 响应式布局

#### 子组件

- `StatCard` - 统计卡片
- `ControlPanel` - 控制面板
- `TimeRangeSelector` - 时间范围选择器
- `TimezoneSelector` - 时区选择器

#### 使用示例

```svelte
<script>
  import { Dashboard } from '$lib/components/features/dashboard';
</script>

<Dashboard />
```

### StatCard 统计卡片组件

用于展示关键指标数据。

#### Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|-------|------|
| `title` | `string` | - | 卡片标题 |
| `value` | `number` | - | 数值 |
| `icon` | `string` | - | 图标（emoji） |
| `color` | `string` | `'blue'` | 主题色 |
| `prefix` | `string` | `''` | 前缀（如货币符号） |
| `suffix` | `string` | `''` | 后缀（如单位） |

#### 使用示例

```svelte
<script>
  import { StatCard } from '$lib/components/features/dashboard';
</script>

<div class="grid grid-cols-2 gap-4">
  <StatCard 
    title="总用户数" 
    value={1234} 
    icon="👤" 
    color="blue" 
  />
  <StatCard 
    title="月收入" 
    value={5678} 
    icon="💰" 
    color="green" 
    prefix="$" 
  />
  <StatCard 
    title="转化率" 
    value={12.5} 
    icon="📈" 
    color="purple" 
    suffix="%" 
  />
</div>
```

### 清算功能组件

清算相关的业务组件，包含数据查询、风险评估、历史记录等功能。

#### LiquidationDashboard

清算仪表板主组件。

```svelte
<script>
  import { LiquidationDashboard } from '$lib/components/features/liquidation';
</script>

<LiquidationDashboard />
```

#### LiquidationChart

清算数据图表组件，基于 BarChart 扩展。

```svelte
<script>
  import { LiquidationChart } from '$lib/components/features/liquidation';
  
  const liquidationData = [
    { datetime: '2024-01-01T10:00:00Z', side: 1, coinType: 'Major', amountUsd: 10000 }
  ];
</script>

<LiquidationChart 
  data={liquidationData}
  groupBy="side"
  timeFrame="1h"
/>
```

### 数据查询组件

提供灵活的数据查询和结果展示功能。

#### DataQueryPanel

数据查询面板组件。

```svelte
<script>
  import { DataQueryPanel } from '$lib/components/features/dataQuery';
</script>

<DataQueryPanel />
```

#### EventCard 系列

事件卡片组件，用于展示查询结果。

- `BaseEventCard` - 基础事件卡片
- `EventCardFactory` - 事件卡片工厂
- `EventCardDetailModal` - 事件详情弹窗

## 布局组件

布局组件负责应用的整体布局和导航结构。

### AppSidebar 侧边栏组件

应用主导航侧边栏。

#### 功能特性

- 多级导航菜单
- 搜索功能
- 收缩/展开
- 响应式适配

#### 使用示例

```svelte
<script>
  import { AppSidebar, AppSidebarProvider } from '$lib/components/layout/AppSidebar';
</script>

<AppSidebarProvider>
  <AppSidebar />
  <main>
    <!-- 页面内容 -->
  </main>
</AppSidebarProvider>
```

### TabBar 标签栏组件

多标签页导航组件。

#### 功能特性

- 动态添加/删除标签
- 标签页切换
- 键盘快捷键支持
- 标签拖拽排序

#### 使用示例

```svelte
<script>
  import { TabBar } from '$lib/components/layout/TabBar';
  import { openTab, tabs } from '$lib/stores/ui';
  
  // 添加标签页
  openTab({
    href: '/dashboard',
    label: '仪表板',
    icon: DashboardIcon
  });
</script>

{#if $tabs.length > 0}
  <TabBar />
{/if}
```

### ErrorBoundary 错误边界组件

错误捕获和处理组件。

#### Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|-------|------|
| `showErrorDetails` | `boolean` | `false` | 是否显示错误详情 |
| `enableRecovery` | `boolean` | `true` | 是否启用错误恢复 |
| `fallbackComponent` | `SvelteComponent` | - | 自定义错误展示组件 |

#### 事件

- `error` - 错误事件，包含错误信息和上下文

#### 使用示例

```svelte
<script>
  import { ErrorBoundary } from '$lib/components/features/error-boundary';
  
  function handleError(event) {
    console.error('捕获到错误:', event.detail);
    // 发送错误报告等处理
  }
</script>

<ErrorBoundary 
  showErrorDetails={import.meta.env.DEV}
  enableRecovery={true}
  on:error={handleError}
>
  <!-- 可能出错的组件 -->
  <SomeComponent />
</ErrorBoundary>
```

## 主题系统

所有组件都支持暗色/浅色主题切换。

### 主题配置

主题通过 CSS 变量和 TailwindCSS 类实现：

```css
/* 浅色主题 */
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --primary: 222.2 47.4% 11.2%;
  --primary-foreground: 210 40% 98%;
  /* 更多变量... */
}

/* 暗色主题 */
.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --primary: 210 40% 98%;
  --primary-foreground: 222.2 47.4% 11.2%;
  /* 更多变量... */
}
```

### 主题切换

```svelte
<script>
  import { toggleMode } from 'mode-watcher';
</script>

<button on:click={toggleMode}>
  切换主题
</button>
```

## 组件开发规范

### 1. 组件结构

```svelte
<script lang="ts" module>
  // 类型定义和导出
  export interface ComponentProps {
    // props 类型定义
  }
</script>

<script lang="ts">
  // 导入依赖
  import { createEventDispatcher, onMount, onDestroy } from 'svelte';
  
  // Props 定义
  let { 
    prop1 = 'defaultValue',
    prop2,
    ...restProps 
  }: ComponentProps = $props();
  
  // 事件分发器
  const dispatch = createEventDispatcher();
  
  // 响应式状态
  let localState = $state('');
  
  // 计算属性
  let computedValue = $derived(prop1.toUpperCase());
  
  // 生命周期
  onMount(() => {
    // 组件挂载逻辑
  });
  
  onDestroy(() => {
    // 清理逻辑
  });
  
  // 方法定义
  function handleClick() {
    dispatch('click', { data: localState });
  }
</script>

<!-- 模板 -->
<div class="component-class" {...restProps}>
  {@render children?.()}
</div>

<style>
  /* 组件样式（如需要） */
</style>
```

### 2. Props 设计原则

- 使用 TypeScript 接口定义 Props 类型
- 为可选属性提供合理的默认值
- 使用 `...restProps` 支持透传属性
- 布尔属性默认为 `false`

### 3. 事件处理

- 使用 `createEventDispatcher` 创建自定义事件
- 事件名称使用 kebab-case（如 `chart-click`）
- 事件载荷包含必要的上下文信息

### 4. 样式规范

- 使用 TailwindCSS 类名
- 支持自定义 `class` 属性
- 使用 CSS 变量实现主题支持
- 避免硬编码颜色值

### 5. 可访问性

- 添加适当的 ARIA 属性
- 支持键盘导航
- 提供语义化的 HTML 结构
- 考虑屏幕阅读器支持

### 6. 性能考虑

- 使用 `$derived` 进行计算属性
- 避免不必要的重新渲染
- 大型数据使用虚拟化
- 图表组件使用懒加载

## 组件测试

### 单元测试

```typescript
import { render, screen } from '@testing-library/svelte';
import { describe, it, expect } from 'vitest';
import Button from './Button.svelte';

describe('Button', () => {
  it('renders with default props', () => {
    render(Button, { children: 'Click me' });
    expect(screen.getByRole('button')).toHaveTextContent('Click me');
  });
  
  it('applies correct variant classes', () => {
    render(Button, { variant: 'destructive', children: 'Delete' });
    const button = screen.getByRole('button');
    expect(button).toHaveClass('bg-destructive');
  });
});
```

### 集成测试

```typescript
import { render, screen, fireEvent } from '@testing-library/svelte';
import { describe, it, expect, vi } from 'vitest';
import BarChart from './BarChart.svelte';

describe('BarChart', () => {
  it('renders chart with data', async () => {
    const data = [
      { name: 'A', value: 100 },
      { name: 'B', value: 200 }
    ];
    
    const { component } = render(BarChart, { data });
    
    // 等待图表初始化
    await new Promise(resolve => setTimeout(resolve, 100));
    
    expect(screen.getByLabelText(/chart/i)).toBeInTheDocument();
  });
  
  it('emits click event', async () => {
    const handleClick = vi.fn();
    const { component } = render(BarChart, { data: [] });
    
    component.$on('chartClick', handleClick);
    
    // 模拟图表点击
    // ...
    
    expect(handleClick).toHaveBeenCalled();
  });
});
```

## 性能优化最佳实践

### 1. 图表组件优化

- **懒加载**: 使用 IntersectionObserver 实现图表懒加载
- **防抖更新**: 数据变化时使用防抖避免频繁重绘
- **数据缓存**: 缓存计算结果避免重复处理

### 2. 大数据处理

- **虚拟滚动**: 长列表使用虚拟滚动技术
- **数据采样**: 大数据集采用采样显示
- **分页加载**: API 请求使用分页机制

### 3. 内存管理

- **及时清理**: 组件销毁时清理事件监听器和定时器
- **避免内存泄漏**: 正确处理订阅和 store 引用
- **资源释放**: 图表组件调用 `dispose()` 方法

## 总结

Svelte Demo 的组件系统提供了完整的解决方案，从基础的 UI 组件到复杂的业务组件，都遵循统一的设计原则和开发规范。通过合理的分层架构、类型安全的 TypeScript 支持、响应式的状态管理，确保了组件的可维护性、可扩展性和高性能。

组件系统的主要优势：

1. **统一性**: 基于 shadcn/ui 的设计系统确保视觉一致性
2. **灵活性**: 支持多种配置选项和自定义扩展
3. **性能**: 针对大数据和复杂交互进行优化
4. **可访问性**: 遵循 WCAG 指南的无障碍设计
5. **开发体验**: 完整的 TypeScript 支持和测试覆盖

这套组件系统为构建现代化的数据可视化应用提供了坚实的基础。