<script lang="ts">
  import { ArrowDown,ArrowUp } from '@lucide/svelte';

  import { CurrencyIcon } from '$lib/components/features/ui';
  import { Card, CardContent } from '$lib/components/ui/card';

  // 本地实现 getParameterValue 函数
  function getParameterValue(
    event: any,
    parameterId: string
  ): string | number | boolean | undefined {
    const parameter = event.parameters.find((p: any) => p.parameterId === parameterId);
    return parameter?.value;
  }

  import Badge from '$lib/components/ui/badge/badge.svelte';
  import { cn } from '$lib/utils';

  import { type BaseEventCardProps,formatAmount, formatUsdAmount } from '../utils';

  type Props = BaseEventCardProps

  const { event, isHighlighted = false, onCardClick }: Props = $props();

  // 提取交易所转账相关参数
  const base = getParameterValue(event, 'base') as string;
  const exchangeSide = getParameterValue(event, 'exchangeSide') as number;
  const amount = getParameterValue(event, 'amount') as number;
  const amountUsd = getParameterValue(event, 'amountUsd') as number;
  const fromLabel = getParameterValue(event, 'fromLabel') as string;
  const toLabel = getParameterValue(event, 'toLabel') as string;
  const fromName = getParameterValue(event, 'fromName') as string;
  const toName = getParameterValue(event, 'toName') as string;
  const toAddress = getParameterValue(event, 'toAddress') as string;

  // 根据参考代码优化信息生成逻辑
  const isWithdrawal = exchangeSide === 1; // 修正：1 为提取，2 为存入
  const transferDirection = isWithdrawal ? 'withdrawn' : 'deposited';

  // 格式化金额显示
  const formattedAmount = formatAmount(amount);
  const formattedUsdAmount = formatUsdAmount(amountUsd);

  // 处理来源和目标信息（用于卡片显示）
  const fromDisplayName = fromName
    ? fromName.charAt(0).toUpperCase() + fromName.slice(1).toLowerCase()
    : '';
  const toDisplayName = toName
    ? toName.charAt(0).toUpperCase() + toName.slice(1).toLowerCase()
    : '';

  // 简化的显示信息（用于卡片）
  const fromInfo = fromDisplayName || fromLabel || '未知来源';
  const toInfo = toDisplayName || toLabel || '未知目标';

  // 截断地址显示
  function truncateAddress(address: string, length: number = 8): string {
    if (!address) return '';
    if (address.length <= length * 2) return address;
    return `${address.slice(0, length)}...${address.slice(-length)}`;
  }

  // 卡片样式 - 使用 cn 函数优化，添加响应式最小宽度
  const cardClasses = cn(
    // 基础样式 - 交易所转账 (EXCHANGE_TRANSFER): 18rem - 复杂响应式布局
    'w-full min-w-30 transition-all duration-200',
    // 高亮状态
    isHighlighted && 'ring-2 ring-primary border-primary/50 shadow-lg',
    // 默认悬停效果
    !isHighlighted && 'hover:shadow-md',
    // 可点击状态
    onCardClick && 'cursor-pointer'
  );

  // 处理卡片点击
  function handleCardClick() {
    onCardClick?.(event);
  }
</script>

<Card
  class={cardClasses}
  onclick={onCardClick ? handleCardClick : undefined}
  role={onCardClick ? 'button' : undefined}
  tabindex={onCardClick ? 0 : undefined}
>
  <CardContent class={cn('p-2 md:p-3')}>
    <!-- 响应式布局：使用更小的断点控制布局 -->
    <div class={cn('flex gap-2 md:gap-3', 'flex-wrap')}>
      <!-- 货币图标：窄屏幕在上方，宽屏幕在左侧 -->
      <div class="flex-shrink-0 self-start">
        <CurrencyIcon
          currencyId={event.currency}
          symbol={base}
          size="size-6 md:size-8"
          class="border-border border"
        />
      </div>

      <!-- 金额和转账信息 -->
      <div class="flex-1">
        <!-- 金额行 -->
        <div class={cn('mb-1 flex', 'gap-1 md:gap-2')}>
          <!-- 金额信息 - 响应式换行 -->
          <div class={cn('flex flex-wrap items-center gap-1', 'text-xs font-medium md:text-sm')}>
            <!-- 转账方向图标 -->
            <div class="flex-none">
              {#if isWithdrawal}
                <ArrowUp class={cn('size-3 md:size-4', 'text-emerald-400')} />
              {:else}
                <ArrowDown class={cn('size-3 md:size-4', 'text-red-400')} />
              {/if}
            </div>
            <span class="text-foreground whitespace-nowrap">
              {formattedAmount}
            </span>
            <span class="text-foreground whitespace-nowrap">
              {base}
            </span>
            <Badge variant="secondary" class={`${isWithdrawal ? 'bg-green-600' : 'bg-red-600'}`}>
              {formattedUsdAmount}
            </Badge>
            <span class="text-muted-foreground whitespace-nowrap">
              {transferDirection}
            </span>
          </div>
        </div>

        <!-- 转账路径信息 - 响应式布局 -->
        <div
          class={cn(
            'flex flex-wrap items-center space-y-0.5 md:gap-1 md:space-y-0',
            'text-muted-foreground text-xs'
          )}
        >
          <!-- From 行 -->
          <div class="flex flex-nowrap items-center gap-1">
            <span class="whitespace-nowrap">From</span>
            <span class={cn('text-foreground truncate font-medium')} title={fromInfo}>
              {fromInfo}
            </span>
          </div>

          <!-- To 行 -->
          {#if toAddress && toAddress !== toInfo}
            <div class="items-center gap-1">
              <span class="whitespace-nowrap">To</span>
              <span class={cn('text-foreground truncate font-mono')} title={toAddress}>
                {truncateAddress(toAddress, 4)}
              </span>
            </div>
          {:else if toInfo !== fromInfo}
            <div class="flex flex-nowrap items-center gap-1">
              <span class="whitespace-nowrap">To</span>
              <span class={cn('text-foreground truncate font-medium')} title={toInfo}>
                {toInfo}
              </span>
            </div>
          {/if}
        </div>
      </div>
    </div>
  </CardContent>
</Card>
