/**
 * 货币信息缓存 Store
 * 基于智能持久化缓存系统的 Svelte Store 包装器
 */

import { writable } from 'svelte/store';

import { coinSearchApiService } from '$lib/services/api';
import {
  type CacheStats,
  currencyPersistentCache,
} from '$lib/services/cache/currencyPersistentCache';
import type { CoinInfo } from '$lib/types';

/**
 * Store 状态接口
 */
interface CurrencyCacheStoreState {
  /** 当前加载的货币ID集合 */
  loadingCurrencies: Set<string>;
  /** 全局加载状态 */
  loading: boolean;
  /** 错误信息 */
  error: string | null;
  /** 缓存统计信息 */
  stats: CacheStats | null;
}

/**
 * 初始状态
 */
const initialState: CurrencyCacheStoreState = {
  loadingCurrencies: new Set(),
  loading: false,
  error: null,
  stats: null,
};

/**
 * 智能货币缓存 Store
 */
function createCurrencyCacheStore() {
  const { subscribe, set, update } = writable<CurrencyCacheStoreState>(initialState);

  return {
    subscribe,

    /**
     * 获取货币信息（带智能缓存）
     * @param currencyId 货币ID
     * @param forceRefresh 是否强制刷新缓存
     * @returns 货币信息
     */
    async getCurrency(currencyId: string, forceRefresh: boolean = false): Promise<CoinInfo | null> {
      if (!currencyId) {
        return null;
      }

      try {
        // 更新加载状态
        update((state) => ({
          ...state,
          loadingCurrencies: new Set([...state.loadingCurrencies, currencyId]),
          loading: state.loadingCurrencies.size === 0, // 如果这是第一个加载项，设置全局加载状态
          error: null,
        }));

        // 使用智能缓存系统获取数据
        const result = await coinSearchApiService.getCurrencyInfo(currencyId, forceRefresh);

        // 更新状态
        update((state) => {
          const newLoadingCurrencies = new Set(state.loadingCurrencies);
          newLoadingCurrencies.delete(currencyId);

          return {
            ...state,
            loadingCurrencies: newLoadingCurrencies,
            loading: newLoadingCurrencies.size > 0,
            error: null,
            stats: currencyPersistentCache.getStats(),
          };
        });

        return result;
      } catch (error) {
        // 更新错误状态
        update((state) => {
          const newLoadingCurrencies = new Set(state.loadingCurrencies);
          newLoadingCurrencies.delete(currencyId);

          return {
            ...state,
            loadingCurrencies: newLoadingCurrencies,
            loading: newLoadingCurrencies.size > 0,
            error: error instanceof Error ? error.message : '获取货币信息失败',
            stats: currencyPersistentCache.getStats(),
          };
        });

        return null;
      }
    },

    /**
     * 预加载多个货币信息
     * @param currencyIds 货币ID数组
     */
    async preloadCurrencies(currencyIds: string[]): Promise<void> {
      const promises = currencyIds.map((id) => this.getCurrency(id));
      await Promise.allSettled(promises);
    },

    /**
     * 强制刷新货币信息
     * @param currencyId 货币ID
     * @returns 货币信息
     */
    async refreshCurrency(currencyId: string): Promise<CoinInfo | null> {
      return this.getCurrency(currencyId, true);
    },

    /**
     * 清除缓存
     * @param currencyId 可选，指定要清除的货币ID，不传则清除所有
     */
    clearCache(currencyId?: string): void {
      if (currencyId) {
        currencyPersistentCache.removeCurrency(currencyId);
      } else {
        currencyPersistentCache.clearCache();
      }

      // 更新统计信息
      update((state) => ({
        ...state,
        stats: currencyPersistentCache.getStats(),
      }));
    },

    /**
     * 获取缓存的货币信息（同步，不触发网络请求）
     * @param currencyId 货币ID
     * @returns 缓存的货币信息，如果不存在则返回null
     */
    getCachedCurrency(currencyId: string): CoinInfo | null {
      const cachedItem = currencyPersistentCache.getCachedItem(currencyId);
      return cachedItem?.data || null;
    },

    /**
     * 检查货币是否正在加载
     * @param currencyId 货币ID
     * @returns 是否正在加载
     */
    isLoading(currencyId: string): boolean {
      let result = false;
      subscribe((state) => {
        result = state.loadingCurrencies.has(currencyId);
      })();
      return result;
    },

    /**
     * 获取缓存统计信息
     * @returns 缓存统计信息
     */
    getStats(): CacheStats {
      return currencyPersistentCache.getStats();
    },

    /**
     * 检查是否有正在进行的请求
     * @param currencyId 货币ID
     * @returns 是否有正在进行的请求
     */
    hasPendingRequest(currencyId: string): boolean {
      return currencyPersistentCache.hasPendingRequest(currencyId);
    },

    /**
     * 重置状态
     */
    reset(): void {
      currencyPersistentCache.clearCache();
      set(initialState);
    },
  };
}

/**
 * 导出货币缓存 Store 实例
 */
export const currencyCacheStore = createCurrencyCacheStore();
