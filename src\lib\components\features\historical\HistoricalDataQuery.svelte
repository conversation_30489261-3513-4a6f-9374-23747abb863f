<script lang="ts">
  import { AlertCircle, Calendar, Database,RefreshCw, TrendingUp } from '@lucide/svelte';

  import { Alert, AlertDescription } from '$lib/components/ui/alert';
  import { Button } from '$lib/components/ui/button';
  import { historicalDataService } from '$lib/services/data/historical';
  import type {
    HistoricalDataStats,
    HistoricalQueryError,
    HistoricalQueryParams,
    HistoricalQueryResponse,
    HistoricalQueryStatus,
  } from '$lib/types';
  import { createModuleLogger } from '$lib/utils/logger';

  import HistoricalDataTable from './HistoricalDataTable.svelte';
  import HistoricalQueryForm from './HistoricalQueryForm.svelte';

  const historicalLogger = createModuleLogger('historical-data-query');

  // 组件状态 - Svelte 5 语法
  let queryStatus = $state<HistoricalQueryStatus>('idle');
  let queryResponse = $state<HistoricalQueryResponse | null>(null);
  let queryError = $state<HistoricalQueryError | null>(null);
  let queryStats = $state<HistoricalDataStats | null>(null);
  let currentQueryParams = $state<HistoricalQueryParams | null>(null);

  // 响应式状态 - Svelte 5 语法
  let isLoading = $derived(queryStatus === 'loading');
  let hasData = $derived(queryResponse && queryResponse.tableRows.length > 0);
  let hasError = $derived(queryStatus === 'error' && queryError);

  // 组件挂载时的初始化 - Svelte 5 语法
  $effect(() => {
    historicalLogger.info('Historical data query component mounted');
  });

  // 处理查询请求
  async function handleQuery(params: HistoricalQueryParams) {
    historicalLogger.info('Starting historical data query', { params });

    // 验证查询参数
    const validation = historicalDataService.validateQueryParams(params);
    if (!validation.isValid) {
      queryError = {
        code: 'VALIDATION_ERROR',
        message: '查询参数验证失败',
        details: validation.errors.join('; '),
        timestamp: Date.now(),
      };
      queryStatus = 'error';
      historicalLogger.warn('Query validation failed', { errors: validation.errors });
      return;
    }

    queryStatus = 'loading';
    queryError = null;
    currentQueryParams = params;

    try {
      // 并行请求数据和统计信息
      const dataResponse = await historicalDataService.queryHistoricalData(params);

      if (dataResponse.status === 'success') {
        queryResponse = dataResponse.data;
        queryStatus = 'success';

        historicalLogger.info('Historical data query completed successfully', {
          totalRecords: queryResponse.total,
          tableRows: queryResponse.tableRows.length,
        });
      } else {
        throw new Error(dataResponse.message || '查询失败');
      }
    } catch (error) {
      queryError = {
        code: 'QUERY_ERROR',
        message: error instanceof Error ? error.message : '查询过程中发生未知错误',
        details: error instanceof Error ? error.stack : undefined,
        timestamp: Date.now(),
      };
      queryStatus = 'error';

      historicalLogger.error('Historical data query failed', {
        error: error instanceof Error ? error.message : String(error),
        params,
      });
    }
  }

  // 处理表单重置
  function handleReset() {
    historicalLogger.info('Resetting historical data query');

    queryStatus = 'idle';
    queryResponse = null;
    queryError = null;
    queryStats = null;
    currentQueryParams = null;
  }

  // 处理表格排序
  async function handleSort(detail: { field: string; order: 'asc' | 'desc' }) {
    if (!currentQueryParams) return;

    const { field, order } = detail;
    historicalLogger.debug('Handling table sort', { field, order });

    const updatedParams: HistoricalQueryParams = {
      ...currentQueryParams,
      sortBy: field as any,
      sortOrder: order,
    };

    await handleQuery(updatedParams);
  }

  // 重新查询
  async function handleRefresh() {
    if (currentQueryParams) {
      await handleQuery(currentQueryParams);
    }
  }
</script>

<div class="space-y-6">
  <!-- 页面标题和描述 -->
  <div class="space-y-2">
    <h1 class="text-3xl font-bold tracking-tight">历史数据查询</h1>
    <p class="text-muted-foreground">
      查询和分析历史加密货币数据，支持多币种、多时间范围和多数据类型的灵活查询。
    </p>
  </div>

  <!-- 查询表单 -->
  <HistoricalQueryForm
    loading={isLoading}
    onQuery={handleQuery}
    onReset={handleReset}
  />

  <!-- 错误提示 -->
  {#if hasError}
    <Alert variant="destructive">
      <AlertCircle class="h-4 w-4" />
      <AlertDescription>
        <div class="space-y-2">
          <p class="font-medium">{queryError?.message}</p>
          {#if queryError?.details}
            <p class="text-sm opacity-90">{queryError.details}</p>
          {/if}
          <Button variant="outline" size="sm" onclick={handleRefresh} disabled={isLoading}>
            <RefreshCw class="mr-2 h-4 w-4" />
            重试
          </Button>
        </div>
      </AlertDescription>
    </Alert>
  {/if}

  <!-- 数据表格 -->
  {#if queryStatus !== 'idle'}
    <HistoricalDataTable
      tableRows={queryResponse?.tableRows || []}
      selectedCoins={currentQueryParams?.selectedCoins || []}
      loading={isLoading}
      total={queryResponse?.total || 0}
      sortBy={currentQueryParams?.sortBy || 'date'}
      sortOrder={currentQueryParams?.sortOrder || 'desc'}
      dataType={currentQueryParams?.dataTypes}
      onSort={handleSort}
    />
  {/if}
</div>
