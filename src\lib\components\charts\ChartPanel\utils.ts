/**
 * ChartPanel 工具函数
 */

import type {
  ChartConfig,
  LiquidationChartConfig,
  SnapshotTimeRange,
  TrendTimeRange,
} from '$lib/types';

/**
 * 统一的图表配置类型
 */
export type UnifiedChartConfig = ChartConfig | LiquidationChartConfig;

/**
 * 判断是否为清算图表配置
 */
export function isLiquidationChartConfig(
  config: UnifiedChartConfig
): config is LiquidationChartConfig {
  return 'series' in config || 'options' in config || 'groupBy' in config;
}

/**
 * 获取快照时间范围标签
 */
export function getSnapshotTimeRangeLabel(timeRange: SnapshotTimeRange): string {
  const labels: Record<SnapshotTimeRange, string> = {
    '1h': '最近1小时',
    '4h': '最近4小时',
    '12h': '最近12小时',
    '24h': '最近24小时',
  };
  return labels[timeRange] || '未知时间范围';
}

/**
 * 获取趋势时间范围标签
 */
export function getTrendTimeRangeLabel(timeRange: TrendTimeRange): string {
  const labels: Record<TrendTimeRange, string> = {
    '7d': '最近7天',
    '30d': '最近30天',
    '90d': '最近90天',
  };
  return labels[timeRange] || '未知时间范围';
}

/**
 * 根据图表ID判断是否需要特殊布局
 */
export function getChartLayoutType(chartId: string): 'kpi' | 'pie' | 'standard' {
  if (chartId === 'kpi') {
    return 'kpi';
  }
  if (chartId === 'pie-long-short' || chartId === 'pie-order-size') {
    return 'pie';
  }
  return 'standard';
}

/**
 * 获取图表默认高度
 */
export function getChartHeight(chartId: string, customHeight?: string): string {
  if (customHeight) {
    return customHeight;
  }

  const layoutType = getChartLayoutType(chartId);
  switch (layoutType) {
    case 'kpi':
      return 'auto';
    case 'pie':
      return '450px';
    case 'standard':
    default:
      return '320px';
  }
}

/**
 * 构建图表组件的Props
 */
export function buildChartProps(
  config: UnifiedChartConfig,
  data: any,
  timeRangeLabel: string,
  onChartClick: (params: any) => void,
  additionalProps: Record<string, any> = {}
): Record<string, any> {
  const formattedTitle = timeRangeLabel ? `[${timeRangeLabel}] ${config.title}` : config.title;

  const baseProps = {
    data: data,
    title: formattedTitle,
    subtitle: config.subTitle,
    onChartClick: onChartClick,
    ...additionalProps,
  };

  // 如果是LiquidationChartConfig，添加额外属性
  if (isLiquidationChartConfig(config) && config.options) {
    Object.assign(baseProps, config.options);
  }

  return baseProps;
}
