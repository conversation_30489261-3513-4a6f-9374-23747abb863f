<script lang="ts">
  import AlertCircleIcon from '@lucide/svelte/icons/alert-circle';
  import RefreshCwIcon from '@lucide/svelte/icons/refresh-cw';
  // 图标导入
  import TrendingUpIcon from '@lucide/svelte/icons/trending-up';
  import { onMount } from 'svelte';

  import { Alert, AlertDescription } from '$lib/components/ui/alert';
  import { Button } from '$lib/components/ui/button';
  import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
  } from '$lib/components/ui/card';
  import { Select, SelectContent, SelectItem, SelectTrigger } from '$lib/components/ui/select';
  import { Tabs, TabsList, TabsTrigger } from '$lib/components/ui/tabs';
  import { liquidationDataService } from '$lib/services/data/liquidation';
  import { openTab } from '$lib/stores/ui';
  import type { LiquidationRankResponse, RankQueryParams } from '$lib/types';

  // 本地类型定义
  type RankSortField =
    | 'amount'
    | 'count'
    | 'changePercent'
    | 'totalAmount'
    | 'totalCount'
    | 'longAmount'
    | 'shortAmount'
    | 'longCount'
    | 'shortCount'
    | 'longShortRatio'
    | 'avgAmount'
    | 'maxAmount'
    | 'minAmount';
  type SortDirection = 'asc' | 'desc';

  // 扩展的查询参数类型
  interface ExtendedRankQueryParams extends RankQueryParams {
    coinTypeFilter?: 'all' | 'Major' | 'Altcoin';
    rankType?: 'amount' | 'count';
    sortField?: RankSortField;
    sortDirection?: SortDirection;
  }

  // 组件导入
  import { RankTable } from './index';

  // 状态管理
  let amountRankData: LiquidationRankResponse | null = $state(null);
  let countRankData: LiquidationRankResponse | null = $state(null);
  let amountLoading = $state(false);
  let countLoading = $state(false);
  let error = $state<string | null>(null);

  // 查询参数
  const queryParams: ExtendedRankQueryParams = $state({
    rankType: 'amount',
    sortField: 'totalAmount',
    sortDirection: 'desc',
    coinTypeFilter: 'all',
    timeRange: '24h',
  });

  // 独立的排序状态
  let amountSortField: RankSortField = $state('totalAmount');
  let amountSortDirection: SortDirection = $state('desc');
  let countSortField: RankSortField = $state('totalCount');
  let countSortDirection: SortDirection = $state('desc');

  // 加载金额排行数据
  async function loadAmountRankData() {
    amountLoading = true;
    error = null;

    try {
      const amountParams = {
        ...queryParams,
        rankType: 'amount' as const,
        sortField: amountSortField,
        sortDirection: amountSortDirection,
      };
      const response = await liquidationDataService.fetchRankData(amountParams);

      if (response.status === 'success') {
        amountRankData = response.data;
      } else {
        error = response.message || '获取金额排行数据失败';
      }
    } catch (err) {
      error = err instanceof Error ? err.message : '未知错误';
      console.error('加载清算金额排行数据失败:', err);
    } finally {
      amountLoading = false;
    }
  }

  // 加载笔数排行数据
  async function loadCountRankData() {
    countLoading = true;

    try {
      const countParams = {
        ...queryParams,
        rankType: 'count' as const,
        sortField: countSortField,
        sortDirection: countSortDirection,
      };
      const response = await liquidationDataService.fetchRankData(countParams);

      if (response.status === 'success') {
        countRankData = response.data;
      } else {
        if (!error) error = response.message || '获取笔数排行数据失败';
      }
    } catch (err) {
      if (!error) error = err instanceof Error ? err.message : '未知错误';
      console.error('加载清算笔数排行数据失败:', err);
    } finally {
      countLoading = false;
    }
  }

  // 加载所有排行数据
  async function loadAllRankData() {
    await Promise.all([loadAmountRankData(), loadCountRankData()]);
  }

  // 处理金额排行榜排序变更
  function handleAmountSort(field: RankSortField, direction: SortDirection) {
    // 更新金额排行榜的排序状态
    amountSortField = field;
    amountSortDirection = direction;
    // 重新加载金额排行数据
    loadAmountRankData();
  }

  // 处理笔数排行榜排序变更
  function handleCountSort(field: RankSortField, direction: SortDirection) {
    // 更新笔数排行榜的排序状态
    countSortField = field;
    countSortDirection = direction;
    // 重新加载笔数排行数据
    loadCountRankData();
  }

  // 监听筛选参数变化
  $effect(() => {
    // 当筛选参数变化时重新加载所有数据
    const { coinTypeFilter, timeRange } = queryParams;

    // 重新加载数据
    loadAllRankData();
  });

  // 刷新数据
  function refreshData() {
    loadAllRankData();
  }

  // 组件挂载时加载数据和初始化标签页
  onMount(async () => {
    // 自动添加"清算排行"标签页到TabBar
    openTab({
      href: '/rank/liquidation',
      label: '清算排行',
      icon: TrendingUpIcon,
    });

    // 加载初始数据
    await loadAllRankData();
  });
</script>

<div class="space-y-6">
  <!-- 页面标题和控制面板 -->
  <div class="flex flex-col gap-4 lg:flex-row lg:items-start lg:justify-between">
    <!-- 页面标题 -->
    <div class="space-y-2">
      <h1 class="text-foreground text-3xl font-bold tracking-tight">清算排行 📈</h1>
      <p class="text-muted-foreground">加密货币清算金额和笔数排行榜</p>
    </div>

    <!-- 控制面板 -->
    <div class="flex flex-col gap-3 lg:flex-shrink-0 lg:flex-row lg:items-center">
      <!-- 时间范围选择 -->
      <div class="flex flex-col gap-1 lg:flex-row lg:items-center lg:gap-2">
        <label
          for="time-range-select"
          class="text-muted-foreground text-xs font-medium lg:whitespace-nowrap">时间范围</label
        >
        <Select type="single" bind:value={queryParams.timeRange}>
          <SelectTrigger id="time-range-select" class="w-full lg:w-32">
            {queryParams.timeRange === '15min'
              ? '15分钟'
              : queryParams.timeRange === '1h'
                ? '1小时'
                : queryParams.timeRange === '4h'
                  ? '4小时'
                  : queryParams.timeRange === '8h'
                    ? '8小时'
                    : '24小时'}
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="15min">15分钟</SelectItem>
            <SelectItem value="1h">1小时</SelectItem>
            <SelectItem value="4h">4小时</SelectItem>
            <SelectItem value="8h">8小时</SelectItem>
            <SelectItem value="24h">24小时</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <!-- 币种类型筛选 -->
      <div class="flex flex-col gap-1 lg:flex-row lg:items-center lg:gap-2">
        <label
          for="coin-type-tabs"
          class="text-muted-foreground text-xs font-medium lg:whitespace-nowrap">币种类型</label
        >
        <Tabs bind:value={queryParams.coinTypeFilter} class="w-full lg:w-auto">
          <TabsList id="coin-type-tabs" class="grid w-full grid-cols-3 lg:w-auto">
            <TabsTrigger value="all" class="text-xs">全部</TabsTrigger>
            <TabsTrigger value="Major" class="text-xs">主流币</TabsTrigger>
            <TabsTrigger value="Altcoin" class="text-xs">山寨币</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <!-- 刷新按钮 -->
      <Button
        variant="outline"
        size="sm"
        class="w-full lg:w-auto"
        onclick={refreshData}
        disabled={amountLoading || countLoading}
      >
        <RefreshCwIcon class="mr-2 h-4 w-4 {amountLoading || countLoading ? 'animate-spin' : ''}" />
        刷新数据
      </Button>
    </div>
  </div>

  <!-- 错误提示 -->
  {#if error}
    <Alert variant="destructive">
      <AlertCircleIcon class="h-4 w-4" />
      <AlertDescription>
        {error}
      </AlertDescription>
    </Alert>
  {/if}

  <!-- 双排行榜布局 -->
  <div class="grid gap-6 lg:grid-cols-2">
    <!-- 清算金额排行榜 -->
    <Card>
      <CardHeader>
        <CardTitle>清算金额排行榜 💰</CardTitle>
        <CardDescription>
          {#if amountRankData?.items}
            显示前 {amountRankData.items.length} 个币种的清算金额排行
          {:else}
            按总清算金额排序的加密货币排行榜（前30名）
          {/if}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <RankTable
          rankType="amount"
          items={amountRankData?.items || []}
          sortField={amountSortField}
          sortDirection={amountSortDirection}
          loading={amountLoading}
          onSort={handleAmountSort}
        />
      </CardContent>
    </Card>

    <!-- 清算笔数排行榜 -->
    <Card>
      <CardHeader>
        <CardTitle>清算笔数排行榜 📊</CardTitle>
        <CardDescription>
          {#if countRankData?.items}
            显示前 {countRankData.items.length} 个币种的清算笔数排行
          {:else}
            按总清算笔数排序的加密货币排行榜（前30名）
          {/if}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <RankTable
          rankType="count"
          items={countRankData?.items || []}
          sortField={countSortField}
          sortDirection={countSortDirection}
          loading={countLoading}
          onSort={handleCountSort}
        />
      </CardContent>
    </Card>
  </div>
</div>
