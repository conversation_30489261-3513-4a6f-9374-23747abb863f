# MSW 生命周期管理文档

## 📋 概述

本文档介绍如何解决 MSW (Mock Service Worker) 在页面关闭后仍然运行并占用 CPU 的问题，以及如何正确管理 MSW 的生命周期。

## 🔍 问题分析

### MSW CPU 占用的原因

1. **Service Worker 持久化**：MSW 使用 Service Worker 技术，即使页面关闭，Service Worker 仍可能在后台运行
2. **事件监听器未清理**：MSW 注册的网络拦截器和事件监听器没有正确清理
3. **开发环境配置**：开发环境中 MSW 可能配置为持续运行状态

### 影响

- 浏览器 CPU 占用率高
- 内存泄漏
- 影响其他网站的性能
- 开发体验下降

## 🛠️ 解决方案

### 1. MSW 管理器 (MSWManager)

我们创建了一个专门的 MSW 管理器来处理生命周期：

```typescript
// src/mocks/manager.ts
class MSWManagerClass {
  private manager: MSWManager = {
    worker: null,
    isRunning: false,
    cleanupFunctions: []
  };

  async initialize(): Promise<void> {
    // 初始化 MSW
  }

  stop(): void {
    // 停止 MSW 并清理资源
  }

  private setupCleanupListeners(): void {
    // 设置页面卸载监听器
  }
}
```

### 2. 自动清理机制

#### 页面卸载事件监听

```typescript
// 监听多种页面卸载事件
window.addEventListener('beforeunload', cleanup);
window.addEventListener('unload', cleanup);
window.addEventListener('pagehide', cleanup);

// 监听页面可见性变化
document.addEventListener('visibilitychange', () => {
  if (document.visibilityState === 'hidden') {
    setTimeout(cleanup, 1000);
  }
});
```

#### 清理函数管理

```typescript
// 保存清理函数，确保在停止时移除所有监听器
this.manager.cleanupFunctions.push(
  () => window.removeEventListener('beforeunload', beforeUnloadHandler),
  () => window.removeEventListener('unload', unloadHandler),
  () => window.removeEventListener('pagehide', pageHideHandler),
  () => document.removeEventListener('visibilitychange', visibilityChangeHandler)
);
```

### 3. 开发调试工具

#### 全局调试接口

在开发环境中，我们在 `window` 对象上添加了调试工具：

```javascript
// 在浏览器控制台中使用
window.__MSW__.stop()      // 停止 MSW
window.__MSW__.restart()   // 重启 MSW
window.__MSW__.status()    // 查看状态
```

#### 编程式 API

```typescript
import { stopMSW, restartMSW, getMSWStatus } from '../mocks';

// 停止 MSW
stopMSW();

// 重启 MSW
await restartMSW();

// 获取状态
const status = getMSWStatus();
console.log(status); // { isRunning: boolean, hasWorker: boolean }
```

## 🔧 使用指南

### 开发环境调试

#### 1. 检查 MSW 状态

```javascript
// 在浏览器控制台中执行
window.__MSW__.status()
// 输出: { isRunning: true, hasWorker: true }
```

#### 2. 手动停止 MSW

```javascript
// 方法 1: 使用全局调试工具
window.__MSW__.stop()

// 方法 2: 使用编程式 API
import { stopMSW } from '../mocks';
stopMSW();
```

#### 3. 重启 MSW

```javascript
// 方法 1: 使用全局调试工具
window.__MSW__.restart()

// 方法 2: 使用编程式 API
import { restartMSW } from '../mocks';
await restartMSW();
```

### 生产环境配置

在生产环境中，MSW 不会启动，因此不会有 CPU 占用问题：

```typescript
// MSW 只在以下条件下启动
if (
  import.meta.env.DEV ||
  import.meta.env.MODE === 'test' ||
  import.meta.env.VITE_USE_MSW === 'true'
) {
  // 启动 MSW
}
```

## 🧪 测试验证

### 验证清理机制

1. **打开开发者工具**
2. **查看 MSW 状态**：
   ```javascript
   window.__MSW__.status()
   ```
3. **关闭标签页**
4. **重新打开并检查**：确认 Service Worker 已停止

### 验证 CPU 占用

1. **打开任务管理器**（Windows）或**活动监视器**（macOS）
2. **关闭包含 MSW 的标签页**
3. **观察浏览器 CPU 占用率**：应该显著下降

### 验证内存泄漏

1. **打开浏览器的任务管理器**（Shift+Esc）
2. **关闭标签页前后对比内存使用**
3. **确认内存被正确释放**

## 🚨 故障排除

### 问题：MSW 仍然在后台运行

**解决方案：**
1. 手动停止：`window.__MSW__.stop()`
2. 重启浏览器
3. 清除浏览器缓存和 Service Worker

### 问题：页面刷新后 MSW 不工作

**解决方案：**
1. 检查控制台错误信息
2. 重启 MSW：`window.__MSW__.restart()`
3. 确认 Service Worker 注册状态

### 问题：开发环境性能问题

**解决方案：**
1. 临时禁用 MSW：设置环境变量 `VITE_USE_MSW=false`
2. 优化 MSW 处理器，减少不必要的请求拦截
3. 使用真实 API 进行开发

## 📊 性能监控

### 监控指标

- **CPU 占用率**：页面关闭后应降至正常水平
- **内存使用**：无明显内存泄漏
- **Service Worker 状态**：页面关闭后应停止

### 监控工具

- 浏览器开发者工具
- 系统任务管理器
- 浏览器任务管理器（Shift+Esc）

## 🔮 最佳实践

### 开发建议

1. **及时清理**：开发完成后手动停止 MSW
2. **监控资源**：定期检查 CPU 和内存使用
3. **合理使用**：只在需要时启用 MSW
4. **版本控制**：确保团队使用相同的 MSW 配置

### 部署建议

1. **环境隔离**：确保生产环境不启用 MSW
2. **配置管理**：使用环境变量控制 MSW 启用
3. **监控告警**：设置性能监控和告警机制

## 📚 相关资源

- [MSW 官方文档](https://mswjs.io/)
- [Service Worker API](https://developer.mozilla.org/en-US/docs/Web/API/Service_Worker_API)
- [浏览器性能监控](https://developer.mozilla.org/en-US/docs/Web/API/Performance_API)
