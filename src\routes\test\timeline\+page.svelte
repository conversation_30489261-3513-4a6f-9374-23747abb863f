<script lang="ts">

  import { Separator } from '$lib/components/ui/separator';
  import { ResizablePaneGroup, ResizablePane, ResizableHandle } from '$lib/components/ui/resizable';
  import { EventCardFactory } from '$lib/components/features/dataQuery/cards';
  import EventCardDetailModal from '$lib/components/features/dataQuery/cards/EventCardDetailModal.svelte';
  import { cardInteractionStore } from '$lib/stores/features/cardInteraction';
  import type { MarketEvent, MarketEventType } from '$lib/types';
  import { MARKET_EVENT_TYPE_LABELS } from '$lib/types';

  // 拖动配置接口
  interface DragOptions {
    sensitivity?: number;
    minDistance?: number;
    scrollAmount?: number;
    onDragStart?: () => void;
    onDragEnd?: () => void;
  }

  // 使用 docs/response.md 中的真实测试数据
  const testData: MarketEvent[] = [
    {
      id: '01JV70KA0VK0ESQ52AN5DX4M2Q',
      marketEventType: 'TWAP',
      date: 1747213461,
      parameters: [
        {
          parameterId: 'twapId',
          value: '01JV704NG1T9SETKTD1GS64AWG',
        },
        {
          parameterId: 'amountUsd',
          value: 5310.935100000001,
        },
        {
          parameterId: 'threshold',
          value: 1,
        },
        {
          parameterId: 'side',
          value: 2,
        },
        {
          parameterId: 'market',
          value: 2,
        },
        {
          parameterId: 'duration',
          value: 13,
        },
        {
          parameterId: 'status',
          value: 'ongoing',
        },
        {
          parameterId: 'tradeId',
          value: '01JV70K9VN6F9HQAWC52ASNR6Z',
        },
        {
          parameterId: 'tradeExecuted',
          value: 1747213461000,
        },
        {
          parameterId: 'tradePrice',
          value: 0.25555,
        },
        {
          parameterId: 'tradeSize',
          value: 2890.9998043435726,
        },
        {
          parameterId: 'tradeSide',
          value: 2,
        },
      ],
      currency: '01J8QJG2T7RR1E2V0NFXFGF2RR',
    },
    {
      id: '01JSKY3X725QG8AN81YGTJV7PD',
      marketEventType: 'LIQUIDATION_ORDER',
      currency: '01HWKTT6BZ7YKZZ545HTPD22N7',
      date: 1745499583,
      parameters: [
        {
          parameterId: 'side',
          value: 1,
        },
        {
          parameterId: 'exchange',
          value: 4096,
        },
        {
          parameterId: 'exchangeLabel',
          value: 'OKX',
        },
        {
          parameterId: 'threshold',
          value: 2,
        },
        {
          parameterId: 'base',
          value: 'FOXY',
        },
        {
          parameterId: 'pair',
          value: 'FOXY-USDT-SWAP',
        },
        {
          parameterId: 'priceUsd',
          value: 0.001887,
        },
        {
          parameterId: 'datetime',
          value: '2025-04-24T12:59:42.539000',
        },
        {
          parameterId: 'amount',
          value: 672000,
        },
        {
          parameterId: 'dailyVolumeRatio',
          value: 0.00041611941630712,
        },
        {
          parameterId: 'amountUsd',
          value: 1268.064,
        },
      ],
    },
    {
      id: '01JSKYXVDBFRBS8BZ4KWMYWJ3C',
      marketEventType: 'EXCHANGE_TRANSFER',
      currency: '01HEPMMG27T0J8Y1RKW74XHXTM',
      date: 1745500433,
      parameters: [
        {
          parameterId: 'sender',
          value: '******************************************',
        },
        {
          parameterId: 'recipient',
          value: '******************************************',
        },
        {
          parameterId: 'threshold',
          value: 0,
        },
        {
          parameterId: 'isExchangeToExchange',
          value: 'False',
        },
        {
          parameterId: 'isDepositWalletToExchange',
          value: 'True',
        },
        {
          parameterId: 'exchange',
          value: 512,
        },
        {
          parameterId: 'exchangeLabel',
          value: 'KRAKEN',
        },
        {
          parameterId: 'base',
          value: 'BEAM',
        },
        {
          parameterId: 'fromLabel',
          value: 'User Deposit Wallet',
        },
        {
          parameterId: 'toLabel',
          value: 'Hot Wallet',
        },
        {
          parameterId: 'toName',
          value: 'KRAKEN',
        },
        {
          parameterId: 'fromName',
          value: 'KRAKEN',
        },
        {
          parameterId: 'toAddress',
          value: '******************************************',
        },
        {
          parameterId: 'fromAddress',
          value: '******************************************',
        },
        {
          parameterId: 'txId',
          value: '0x125b8c1e69349abcad90af240c92a9b54ce8af5a95db7a699cc091c9dd0e937c',
        },
        {
          parameterId: 'exchangeSide',
          value: 2,
        },
        {
          parameterId: 'datetime',
          value: '2025-04-24T13:09:59',
        },
        {
          parameterId: 'amount',
          value: 1002727,
        },
        {
          parameterId: 'amountUsd',
          value: 6871.9,
        },
        {
          parameterId: 'chain',
          value: 'Ethereum',
        },
      ],
    },
    {
      id: '01JSKSN7BKE30QERCZ0FFNR97H',
      marketEventType: 'FUNDING_RATE_SWITCH',
      currency: '01HAF64ZT94SKECAMZ9C8TD0J1',
      date: 1745494908,
      parameters: [
        {
          parameterId: 'threshold',
          value: 0,
        },
        {
          parameterId: 'exchangeLabel',
          value: 'OKX FUTURES',
        },
        {
          parameterId: 'side',
          value: 2,
        },
        {
          parameterId: 'exchange',
          value: 4096,
        },
        {
          parameterId: 'wasSince',
          value: '2025-04-20 17:41',
        },
        {
          parameterId: 'daysSince',
          value: 3,
        },
        {
          parameterId: 'datetime',
          value: '2025-04-24T11:41:48.275561+00:00',
        },
        {
          parameterId: 'pair',
          value: 'ENJ/USDT:USDT',
        },
        {
          parameterId: 'base',
          value: 'ENJ',
        },
        {
          parameterId: 'previousDailyFundingRate',
          value: -0.05223,
        },
        {
          parameterId: 'dailyFundingRate',
          value: 0.02847,
        },
        {
          parameterId: 'aggregated',
          value: false,
        },
        {
          parameterId: 'fundingRate',
          value: 0.0011863,
        },
        {
          parameterId: 'previousFundingRate',
          value: -0.0021762,
        },
        {
          parameterId: 'fundingRateDiff',
          value: 0.0033625,
        },
      ],
    },
    {
      id: '01JSM0JMSAAST7BHZW7E02FV7V',
      marketEventType: 'VOLUME_INCREASE',
      currency: '01HCVQSJ0DG9QV5YNV9NP69KQ6',
      date: 1745502163,
      parameters: [
        {
          parameterId: 'datetime',
          value: '2025-04-24T13:42:43',
        },
        {
          parameterId: 'priceUsd',
          value: 0.07534,
        },
        {
          parameterId: 'previousPriceUsd',
          value: 0.07376,
        },
        {
          parameterId: 'exchange',
          value: 32,
        },
        {
          parameterId: 'exchangeLabel',
          value: 'COINBASE',
        },
        {
          parameterId: 'base',
          value: 'BIGTIME',
        },
        {
          parameterId: 'pair',
          value: 'BIGTIME/USDC',
        },
        {
          parameterId: 'threshold',
          value: 2,
        },
        {
          parameterId: 'aggregated',
          value: false,
        },
        {
          parameterId: 'priceDirection',
          value: 1,
        },
        {
          parameterId: 'mean',
          value: 1427.6080079220778,
        },
        {
          parameterId: 'recentVolumeSumUsd',
          value: 67178.22341,
        },
        {
          parameterId: 'percentAbnormal',
          value: 672.24,
        },
        {
          parameterId: 'isSpot',
          value: 1,
        },
        {
          parameterId: 'isDerivatives',
          value: 0,
        },
      ],
    },
    {
      id: '01JSM0HHRQVJZ2MR50TG7XFK4B',
      marketEventType: 'EXTREME_FUNDING_RATE',
      currency: '01HAF64ZYT42GP9AK8MY45CK7T',
      date: 1745502127,
      parameters: [
        {
          parameterId: 'base',
          value: 'XDC',
        },
        {
          parameterId: 'pair',
          value: 'XDC/USDT:USDT',
        },
        {
          parameterId: 'side',
          value: 1,
        },
        {
          parameterId: 'datetime',
          value: '2025-04-24T13:42:07.895674+00:00',
        },
        {
          parameterId: 'exchange',
          value: 4,
        },
        {
          parameterId: 'wasSince',
          value: '2025-04-24 13:42',
        },
        {
          parameterId: 'daysSince',
          value: 0,
        },
        {
          parameterId: 'threshold',
          value: -1,
        },
        {
          parameterId: 'aggregated',
          value: false,
        },
        {
          parameterId: 'fundingRate',
          value: 0.0039,
        },
        {
          parameterId: 'exchangeLabel',
          value: 'BITGET FUTURES',
        },
        {
          parameterId: 'dailyFundingRate',
          value: 0.0936,
        },
        {
          parameterId: 'previousFundingRate',
          value: 0.00125,
        },
        {
          parameterId: 'previousDailyFundingRate',
          value: 0.03,
        },
      ],
    },
    {
      id: '01JSKYG1QHVWE26XCVY3RKW8QX',
      marketEventType: 'ORDERBOOK_IMBALANCE',
      currency: '01JP2G8RNR4JBJP81JS5JNJQ89',
      date: 1745499981,
      parameters: [
        {
          parameterId: 'deltaUsd',
          value: 5322.7401721067945,
        },
        {
          parameterId: 'side',
          value: 1,
        },
        {
          parameterId: 'threshold',
          value: 0,
        },
        {
          parameterId: 'variationPercent',
          value: 11.01,
        },
        {
          parameterId: 'bidsSumUsd',
          value: 659821.6370916502,
        },
        {
          parameterId: 'asksSumUsd',
          value: 594357.6049655201,
        },
        {
          parameterId: 'bidsSum',
          value: 659821.6370916502,
        },
        {
          parameterId: 'url',
          value: '',
        },
        {
          parameterId: 'nbPairs',
          value: 5,
        },
        {
          parameterId: 'exchangeTopBidder',
          value: 4,
        },
        {
          parameterId: 'exchangeTopAsker',
          value: 512,
        },
        {
          parameterId: 'exchangeLabelTopBidder',
          value: 'BITGET',
        },
        {
          parameterId: 'exchangeLabelTopAsker',
          value: 'KRAKEN',
        },
        {
          parameterId: 'range',
          value: 30,
        },
        {
          parameterId: 'exchange',
          value: -1,
        },
        {
          parameterId: 'priceUsd',
          value: 0.08130785714285714,
        },
        {
          parameterId: 'asksSum',
          value: 594357.6049655201,
        },
        {
          parameterId: 'aggregated',
          value: true,
        },
        {
          parameterId: 'base',
          value: 'BMT',
        },
        {
          parameterId: 'datetime',
          value: '2025-04-24T13:05:47',
        },
      ],
    },
    {
      id: '01JTZ8PF84P6A1WM2585NHWX9B',
      marketEventType: 'OPEN_INTEREST_VARIATION',
      currency: '01JC303X64R02BZP8P6CZD1BCE',
      date: 1746953518,
      parameters: [
        {
          parameterId: 'base',
          value: 'SWELL',
        },
        {
          parameterId: 'pair',
          value: 'SWELL/USDT:USDT',
        },
        {
          parameterId: 'datetime',
          value: '2025-05-11T08:51:58.333439',
        },
        {
          parameterId: 'exchange',
          value: 4,
        },
        {
          parameterId: 'priceUsd',
          value: 0.01204,
        },
        {
          parameterId: 'direction',
          value: 2,
        },
        {
          parameterId: 'threshold',
          value: 0,
        },
        {
          parameterId: 'timeframe',
          value: '15m',
        },
        {
          parameterId: 'aggregated',
          value: false,
        },
        {
          parameterId: 'oiVariation',
          value: -4411074,
        },
        {
          parameterId: 'absVariation',
          value: 2.73,
        },
        {
          parameterId: 'exchangeLabel',
          value: 'BITGET FUTURES',
        },
        {
          parameterId: 'oiVariationUsd',
          value: -53109.33,
        },
        {
          parameterId: 'currentOiAmount',
          value: 157232051,
        },
        {
          parameterId: 'previousOiAmount',
          value: 161643125,
        },
        {
          parameterId: 'variationPercent',
          value: -2.73,
        },
        {
          parameterId: 'currentOiValueUsd',
          value: 1893073.89,
        },
      ],
    },
  ];

  // 获取所有唯一的市场事件类型
  const uniqueTypes: MarketEventType[] = [
    ...new Set(testData.map((item) => item.marketEventType)),
  ].sort() as MarketEventType[];

  // 控制显示的事件类型（使用 Svelte 5 $state）
  let visibleTypes = $state(new Set(uniqueTypes));

  // 过滤后的数据（使用 Svelte 5 $derived）
  const filteredData = $derived(
    testData.filter((item) => visibleTypes.has(item.marketEventType))
  );

  // 切换事件类型显示状态（优化为 Svelte 5 runes 兼容）
  function toggleEventType(type: MarketEventType) {
    if (visibleTypes.has(type)) {
      visibleTypes.delete(type);
    } else {
      visibleTypes.add(type);
    }
    // Svelte 5 runes 会自动检测 Set 的变化，但为了确保更新，我们重新赋值
    visibleTypes = new Set(visibleTypes);
  }

  // 全选/全不选（优化为 Svelte 5 runes 兼容）
  function toggleAll() {
    if (visibleTypes.size === uniqueTypes.length) {
      visibleTypes = new Set();
    } else {
      visibleTypes = new Set(uniqueTypes);
    }
  }

  // 将Unix时间戳归并到15分钟间隔
  function roundToFifteenMinutes(timestamp: number): string {
    const date = new Date(timestamp * 1000);
    const minutes = date.getMinutes();
    const roundedMinutes = Math.floor(minutes / 15) * 15;

    const roundedDate = new Date(date);
    roundedDate.setMinutes(roundedMinutes, 0, 0);

    return roundedDate.toISOString();
  }

  // 按15分钟间隔分组数据（使用过滤后的数据，使用 Svelte 5 $derived）
  const itemsByTimeSlot = $derived(
    filteredData.reduce(
      (acc, item) => {
        const timeSlot = roundToFifteenMinutes(item.date);
        if (!acc[timeSlot]) {
          acc[timeSlot] = [];
        }
        acc[timeSlot].push(item);
        return acc;
      },
      {} as Record<string, MarketEvent[]>
    )
  );

  // 获取所有15分钟时间间隔并排序（使用 Svelte 5 $derived）
  const allTimeSlots = $derived(
    Object.keys(itemsByTimeSlot).sort(
      (a, b) => new Date(b).getTime() - new Date(a).getTime()
    )
  );

  // 获取可见的事件类型（用于表头显示，使用 Svelte 5 $derived）
  const visibleUniqueTypes = $derived(
    uniqueTypes.filter((type) => visibleTypes.has(type))
  );

  // 复杂的派生状态示例：统计信息（使用 Svelte 5 $derived）
  const statistics = $derived({
    totalEvents: filteredData.length,
    totalTimeSlots: allTimeSlots.length,
    averageEventsPerSlot: (() => {
      const avg = allTimeSlots.length > 0 ? filteredData.length / allTimeSlots.length : 0;
      return Math.round(avg * 100) / 100;
    })(),

    // 按事件类型统计
    eventTypeStats: uniqueTypes.reduce((acc, type) => {
      const count = filteredData.filter(item => item.marketEventType === type).length;
      acc[type] = {
        count,
        percentage: filteredData.length > 0 ? (count / filteredData.length) * 100 : 0,
        visible: visibleTypes.has(type)
      };
      return acc;
    }, {} as Record<MarketEventType, { count: number; percentage: number; visible: boolean }>),

    visibleEventTypes: visibleTypes.size,
    visibleEventsCount: filteredData.length
  });

  // 卡片组件最小宽度配置映射
  const CARD_MIN_WIDTHS: Record<MarketEventType, string> = {
    LIQUIDATION_ORDER: 'min-w-36', // 9rem
    TWAP: 'min-w-80', // 20rem
    EXCHANGE_TRANSFER: 'min-w-30', // 7.5rem
    VOLUME_INCREASE: 'min-w-36', // 9rem
    EXTREME_FUNDING_RATE: 'min-w-30', // 7.5rem
    FUNDING_RATE_SWITCH: 'min-w-30', // 7.5rem
    OPEN_INTEREST_VARIATION: 'min-w-48', // 12rem
    ORDERBOOK_IMBALANCE: 'min-w-72', // 18rem
  };

  // 获取事件类型对应的最小宽度类名
  function getColumnMinWidth(type: MarketEventType): string {
    return CARD_MIN_WIDTHS[type] || 'min-w-30'; // 默认最小宽度
  }

  // 格式化时间显示
  function formatTime(dateString: string): string {
    const date = new Date(dateString);
    const endDate = new Date(date.getTime() + 15 * 60 * 1000);

    const startTime = date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });

    const endTime = endDate.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });

    return `${startTime}-${endTime}`;
  }

  // 格式化日期显示
  function formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  }

  // 事件卡片点击处理
  function handleCardClick(event: MarketEvent) {
    cardInteractionStore.showDetailModal(event);
  }

  // 模态框关闭处理
  function handleModalClose() {
    cardInteractionStore.hideDetailModal();
  }

  // 拖动状态管理（使用 Svelte 5 $state）
  let isDragging = $state(false);
  let timelineContainer: HTMLElement;

  // 滚动位置状态（使用 Svelte 绑定优化 DOM 操作）
  let scrollLeft = $state(0);
  let scrollWidth = $state(0);
  let clientWidth = $state(0);

  // 滚动相关的派生状态
  const maxScrollLeft = $derived(Math.max(0, scrollWidth - clientWidth));
  const canScroll = $derived(scrollWidth > clientWidth);
  const scrollProgress = $derived(
    maxScrollLeft > 0 ? (scrollLeft / maxScrollLeft) * 100 : 0
  );

  // 性能监控状态
  let isVisible = $state(true);
  let performanceMetrics = $state({
    scrollEvents: 0,
    renderTime: 0,
    lastUpdate: Date.now()
  });

  // 默认拖动配置（使用 Svelte 5 状态更新）
  const defaultDragOptions: DragOptions = {
    sensitivity: 1,
    minDistance: 3,
    scrollAmount: 100,
    onDragStart: () => { isDragging = true; },
    onDragEnd: () => { isDragging = false; }
  };

  // 拖动指令函数
  function dragAction(node: HTMLElement, options: DragOptions = {}) {
    const config = { ...defaultDragOptions, ...options };

    // 局部状态管理
    let localIsDragging = false;
    let dragStartX = 0;
    let dragStartScrollLeft = 0;
    let animationFrameId: number | null = null;
    let pendingScrollLeft: number | null = null;
    let cachedElements: HTMLElement[] = [];

    // 缓存子元素（优化版本）
    function cacheChildElements() {
      cachedElements = Array.from(node.querySelectorAll('*')) as HTMLElement[];
    }

    // 设置指针事件状态
    function setPointerEvents(disabled: boolean) {
      // 对于需要直接控制的元素，使用 DOM 操作
      if (cachedElements.length === 0) {
        cacheChildElements();
      }
      cachedElements.forEach((el) => {
        el.style.pointerEvents = disabled ? 'none' : '';
      });
    }

    // 滚动更新函数（使用响应式状态）
    function updateScroll() {
      if (pendingScrollLeft !== null) {
        // 使用响应式状态更新滚动位置
        scrollLeft = pendingScrollLeft;
        pendingScrollLeft = null;
      }
      animationFrameId = null;
    }

    // 拖动开始处理
    function handleMouseDown(event: MouseEvent) {
      // 只响应左键点击
      if (event.button !== 0) return;

      // 检查是否点击在可拖动区域（避免与卡片交互冲突）
      const target = event.target as HTMLElement;
      if (target.closest('.event-card') || target.closest('button') || target.closest('input')) {
        return;
      }

      // 检查容器是否可滚动（使用响应式状态）
      if (!canScroll) {
        return; // 如果内容不超出容器，不需要拖动
      }

      localIsDragging = true;
      dragStartX = event.clientX;
      dragStartScrollLeft = scrollLeft;

      // 触发外部回调
      config.onDragStart?.();

      // 动态设置拖动时的指针事件处理
      setPointerEvents(true);

      // 添加全局事件监听器
      document.addEventListener('mousemove', handleMouseMove, { passive: false });
      document.addEventListener('mouseup', handleMouseUp);

      // 防止页面滚动
      document.body.style.overflow = 'hidden';
    }

    // 拖动过程处理
    function handleMouseMove(event: MouseEvent) {
      if (!localIsDragging) return;

      const deltaX = event.clientX - dragStartX;

      // 检查是否达到最小拖动距离
      if (Math.abs(deltaX) < (config.minDistance || 3)) {
        return;
      }

      // 应用拖动敏感度
      const adjustedDelta = deltaX * (config.sensitivity || 1);
      const newScrollLeft = dragStartScrollLeft - adjustedDelta;

      // 边界检测（使用响应式状态）
      const clampedScrollLeft = Math.max(0, Math.min(newScrollLeft, maxScrollLeft));

      // 使用 requestAnimationFrame 节流，避免过度频繁更新
      pendingScrollLeft = clampedScrollLeft;

      if (animationFrameId === null) {
        animationFrameId = requestAnimationFrame(updateScroll);
      }
    }

    // 拖动结束处理
    function handleMouseUp() {
      if (!localIsDragging) return;

      localIsDragging = false;

      // 清理动画帧
      if (animationFrameId !== null) {
        cancelAnimationFrame(animationFrameId);
        animationFrameId = null;
      }

      // 执行最后一次滚动更新（如果有待处理的）
      if (pendingScrollLeft !== null) {
        updateScroll();
      }

      // 恢复指针事件
      setPointerEvents(false);

      // 恢复页面滚动
      document.body.style.overflow = '';

      // 移除全局事件监听器
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);

      // 触发外部回调
      config.onDragEnd?.();
    }

    // 键盘导航处理（使用响应式状态）
    function handleKeyDown(event: KeyboardEvent) {
      const scrollAmount = config.scrollAmount || 100;

      switch (event.key) {
        case 'ArrowLeft':
          scrollLeft = Math.max(0, scrollLeft - scrollAmount);
          break;
        case 'ArrowRight':
          scrollLeft = Math.min(maxScrollLeft, scrollLeft + scrollAmount);
          break;
        case 'Home':
          scrollLeft = 0;
          break;
        case 'End':
          scrollLeft = maxScrollLeft;
          break;
      }
    }

    // 绑定事件监听器
    node.addEventListener('mousedown', handleMouseDown);
    node.addEventListener('keydown', handleKeyDown);

    // 返回销毁函数
    return {
      destroy() {
        // 清理拖动状态
        if (localIsDragging) {
          handleMouseUp();
        }

        // 清理动画帧
        if (animationFrameId !== null) {
          cancelAnimationFrame(animationFrameId);
        }

        // 恢复指针事件
        setPointerEvents(false);

        // 恢复页面滚动
        document.body.style.overflow = '';

        // 移除事件监听器
        node.removeEventListener('mousedown', handleMouseDown);
        node.removeEventListener('keydown', handleKeyDown);
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      }
    };
  }

  // 监听数据变化的副作用（使用 Svelte 5 $effect）
  $effect(() => {
    // 当过滤数据或统计信息变化时执行
    // 这里可以添加数据变化时的副作用，如：
    // - 发送分析数据到服务器
    // - 更新浏览器标题
    // - 保存用户偏好设置等

    // 示例：更新页面标题
    if (typeof document !== 'undefined') {
      document.title = `时间线 (${statistics.visibleEventsCount} 条记录)`;
    }
  });

  // 同步 DOM 属性和响应式状态（DOM 操作优化）
  $effect(() => {
    if (timelineContainer) {
      // 监听滚动事件，同步滚动位置
      const handleScroll = () => {
        scrollLeft = timelineContainer.scrollLeft;
        scrollWidth = timelineContainer.scrollWidth;
        clientWidth = timelineContainer.clientWidth;
      };

      // 初始化值
      handleScroll();

      // 监听滚动事件
      timelineContainer.addEventListener('scroll', handleScroll, { passive: true });

      // 监听尺寸变化 - 使用防抖避免 ResizeObserver 循环
      let resizeTimeout: ReturnType<typeof setTimeout>;
      const debouncedHandleResize = () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
          try {
            handleScroll();
          } catch (error) {
            // 忽略 ResizeObserver 循环错误
            if (error instanceof Error && !error.message.includes('ResizeObserver loop')) {
              throw error;
            }
          }
        }, 16); // 约 60fps
      };

      const resizeObserver = new ResizeObserver(debouncedHandleResize);
      resizeObserver.observe(timelineContainer);

      // 清理函数
      return () => {
        clearTimeout(resizeTimeout);
        timelineContainer.removeEventListener('scroll', handleScroll);
        resizeObserver.disconnect();
      };
    }
  });

  // 响应式更新滚动位置（当 scrollLeft 状态变化时）
  $effect(() => {
    if (timelineContainer && timelineContainer.scrollLeft !== scrollLeft) {
      timelineContainer.scrollLeft = scrollLeft;
    }
  });

  // 高级 DOM 交互优化：智能滚动行为
  $effect(() => {
    if (timelineContainer) {
      // 当数据变化时，智能调整滚动位置
      const currentProgress = scrollProgress;

      // 如果用户在末尾，保持在末尾
      if (currentProgress > 95) {
        scrollLeft = maxScrollLeft;
      }

      // 如果滚动位置超出新的最大值，调整到最大值
      if (scrollLeft > maxScrollLeft) {
        scrollLeft = maxScrollLeft;
      }
    }
  });

  // 键盘快捷键支持（DOM 操作优化）
  $effect(() => {
    if (typeof document !== 'undefined') {
      const handleGlobalKeyDown = (event: KeyboardEvent) => {
        // 只在时间线容器获得焦点时响应
        if (document.activeElement === timelineContainer) {
          switch (event.key) {
            case 'PageUp':
              event.preventDefault();
              scrollLeft = Math.max(0, scrollLeft - clientWidth * 0.8);
              break;
            case 'PageDown':
              event.preventDefault();
              scrollLeft = Math.min(maxScrollLeft, scrollLeft + clientWidth * 0.8);
              break;
          }
        }
      };

      document.addEventListener('keydown', handleGlobalKeyDown);

      return () => {
        document.removeEventListener('keydown', handleGlobalKeyDown);
      };
    }
  });

  // 性能监控和可见性优化（高级 DOM 操作优化）
  $effect(() => {
    if (timelineContainer && typeof IntersectionObserver !== 'undefined') {
      // 使用 Intersection Observer 监控组件可见性
      const intersectionObserver = new IntersectionObserver(
        (entries) => {
          try {
            const entry = entries[0];
            isVisible = entry.isIntersecting;

            // 当组件不可见时，可以暂停一些性能密集的操作
            // 这里可以添加性能优化逻辑
          } catch (error) {
            // 忽略可能的观察器错误
            if (error instanceof Error && !error.message.includes('ResizeObserver')) {
              throw error;
            }
          }
        },
        { threshold: 0.1 }
      );

      intersectionObserver.observe(timelineContainer);

      // 性能监控 - 使用节流避免过度更新
      const startTime = performance.now();
      let scrollEventCount = 0;
      let performanceUpdateTimeout: ReturnType<typeof setTimeout>;

      const handlePerformanceScroll = () => {
        scrollEventCount++;

        // 节流性能指标更新
        clearTimeout(performanceUpdateTimeout);
        performanceUpdateTimeout = setTimeout(() => {
          performanceMetrics = {
            scrollEvents: scrollEventCount,
            renderTime: performance.now() - startTime,
            lastUpdate: Date.now()
          };
        }, 100); // 每100ms最多更新一次
      };

      timelineContainer.addEventListener('scroll', handlePerformanceScroll, { passive: true });

      return () => {
        clearTimeout(performanceUpdateTimeout);
        intersectionObserver.disconnect();
        timelineContainer.removeEventListener('scroll', handlePerformanceScroll);
      };
    }
  });

  // 简化的组件清理和错误处理（使用 Svelte 5 $effect）
  $effect(() => {
    // 全局错误处理 - 忽略 ResizeObserver 循环错误
    const handleGlobalError = (event: ErrorEvent) => {
      if (event.message && event.message.includes('ResizeObserver loop completed with undelivered notifications')) {
        event.preventDefault();
        event.stopPropagation();
        return false;
      }
    };

    window.addEventListener('error', handleGlobalError);

    // 返回清理函数，在组件卸载时执行
    return () => {
      window.removeEventListener('error', handleGlobalError);
      // 恢复页面滚动（作为备用清理）
      document.body.style.overflow = '';
    };
  });
</script>

<div class="mx-auto w-full p-6">
  <div class="mb-6">
    <h1 class="mb-2 text-2xl font-bold">时间线组件测试页面</h1>
    <p class="text-muted-foreground">简化版本的时间线组件，用于测试基础功能</p>
  </div>

  <Separator class="mb-6" />

  <!-- 控制面板 -->
  <div class="bg-card mb-6 rounded-lg border p-4">
    <div class="mb-4 flex items-center justify-between">
      <h3 class="text-lg font-semibold">事件类型控制</h3>
      <button
        class="bg-primary text-primary-foreground hover:bg-primary/90 rounded-md px-3 py-1 text-sm"
        onclick={toggleAll}
      >
        {visibleTypes.size === uniqueTypes.length ? '全不选' : '全选'}
      </button>
    </div>

    <div class="grid grid-cols-2 gap-2 sm:grid-cols-3 lg:grid-cols-4">
      {#each uniqueTypes as type (type)}
        {@const isVisible = visibleTypes.has(type)}
        {@const typeCount = testData.filter((item) => item.marketEventType === type).length}
        <label
          class="hover:bg-accent/50 flex cursor-pointer items-center space-x-2 rounded-md border p-2"
          class:bg-accent={isVisible}
        >
          <input
            type="checkbox"
            checked={isVisible}
            onchange={() => toggleEventType(type)}
            class="rounded border-gray-300"
          />
          <div class="min-w-0 flex-1">
            <div class="truncate text-sm font-medium">
              {MARKET_EVENT_TYPE_LABELS[type] || type}
            </div>
            <div class="text-muted-foreground text-xs">
              {typeCount} 条记录
            </div>
          </div>
        </label>
      {/each}
    </div>

    <div class="text-muted-foreground mt-4 text-sm">
      已选择 {statistics.visibleEventTypes} / {uniqueTypes.length} 种事件类型，
      显示 {statistics.visibleEventsCount} / {testData.length} 条记录
      （平均每时段 {statistics.averageEventsPerSlot} 条记录）
    </div>

    <!-- 滚动进度指示器和控制按钮（使用派生状态） -->
    {#if canScroll}
      <div class="mt-2 flex items-center gap-2 text-xs text-muted-foreground">
        <span>滚动进度:</span>
        <div class="flex-1 bg-muted h-1 rounded-full overflow-hidden">
          <div
            class="bg-primary h-full transition-all duration-200"
            style="width: {scrollProgress}%"
          ></div>
        </div>
        <span>{Math.round(scrollProgress)}%</span>

        <!-- 滚动控制按钮 -->
        <div class="flex gap-1">
          <button
            class="px-2 py-1 text-xs bg-muted hover:bg-accent rounded"
            onclick={() => scrollLeft = 0}
            disabled={scrollLeft === 0}
            title="滚动到开始"
          >
            ⏮
          </button>
          <button
            class="px-2 py-1 text-xs bg-muted hover:bg-accent rounded"
            onclick={() => scrollLeft = Math.max(0, scrollLeft - clientWidth * 0.5)}
            disabled={scrollLeft === 0}
            title="向左滚动"
          >
            ◀
          </button>
          <button
            class="px-2 py-1 text-xs bg-muted hover:bg-accent rounded"
            onclick={() => scrollLeft = Math.min(maxScrollLeft, scrollLeft + clientWidth * 0.5)}
            disabled={scrollLeft >= maxScrollLeft}
            title="向右滚动"
          >
            ▶
          </button>
          <button
            class="px-2 py-1 text-xs bg-muted hover:bg-accent rounded"
            onclick={() => scrollLeft = maxScrollLeft}
            disabled={scrollLeft >= maxScrollLeft}
            title="滚动到结束"
          >
            ⏭
          </button>
        </div>
      </div>
    {/if}

    <!-- 性能监控信息（开发模式显示） -->
    {#if import.meta.env.DEV}
      <div class="mt-2 text-xs text-muted-foreground/70 font-mono">
        性能: {performanceMetrics.scrollEvents} 滚动事件 |
        渲染时间: {Math.round(performanceMetrics.renderTime)}ms |
        可见: {isVisible ? '是' : '否'}
      </div>
    {/if}
  </div>

  <ResizablePaneGroup direction="horizontal" class="min-h-[600px] rounded-lg border">
    <!-- 时间线区域 -->
    <ResizablePane defaultSize={75} minSize={10}>
      <!-- svelte-ignore a11y_no_noninteractive_element_interactions -->
      <!-- svelte-ignore a11y_no_noninteractive_tabindex -->
      <section
        class="flex h-full overflow-x-auto scroll-auto p-4"
        class:select-none={isDragging}
        class:transition-none={isDragging}
        class:shadow-lg={isDragging}
        class:cursor-grab={!isDragging}
        class:cursor-grabbing={isDragging}
        bind:this={timelineContainer}
        use:dragAction={defaultDragOptions}
        aria-label="时间线拖动区域"
        aria-describedby="timeline-instructions"
        tabindex="0"
      >
        <div id="timeline-instructions" class="sr-only">
          使用鼠标拖动或方向键来平移视图。按Home键回到开始，按End键到达结尾。
        </div>
        <div class="w-full">
          {#if visibleUniqueTypes.length === 0}
            <!-- 空状态 -->
            <div class="flex flex-col items-center justify-center py-12 text-center">
              <div class="bg-muted mb-4 flex h-12 w-12 items-center justify-center rounded-full">
                <svg
                  class="text-muted-foreground h-6 w-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  ></path>
                </svg>
              </div>
              <p class="text-muted-foreground text-sm">暂无时间轴数据</p>
            </div>
          {:else}
            <!-- 表头：类型列标题 -->
            <div class="mb-6 flex">
              <!-- 固定区域：时间列和时间轴占位 -->
              <div class="flex flex-shrink-0">
                <div class="w-24"></div>
                <!-- 时间列占位 -->
                <div class="w-1"></div>
                <!-- 时间轴占位 -->
              </div>

              <!-- 表头内容 -->
              <div class="flex-1 pl-4">
                <div class="flex gap-3 pt-2">
                  {#each visibleUniqueTypes as type (type)}
                    <div class="flex-1 text-center {getColumnMinWidth(type)}">
                      <h3
                        class="text-foreground bg-accent/30 border-border/20 rounded-lg border px-3 py-2 text-sm font-semibold whitespace-nowrap"
                      >
                        {MARKET_EVENT_TYPE_LABELS[type] || type}
                      </h3>
                    </div>
                  {/each}
                </div>
              </div>
            </div>

            <!-- 时间行 -->
            {#each allTimeSlots as timeSlot (timeSlot)}
              {@const timeSlotItems = itemsByTimeSlot[timeSlot] || []}
              {@const totalItemsInSlot = timeSlotItems.length}
              <div class="relative mb-6 flex">
                <!-- 时间显示（左侧） -->
                <div class="flex w-24 flex-shrink-0 flex-col justify-start pr-4 text-right">
                  <div
                    class="text-foreground bg-accent/20 mb-1 rounded px-2 py-1 font-mono text-xs font-medium"
                  >
                    {formatTime(timeSlot)}
                  </div>
                  <div class="text-muted-foreground mb-1 font-mono text-xs">
                    {formatDate(timeSlot)}
                  </div>
                  {#if totalItemsInSlot > 0}
                    <div class="text-muted-foreground text-xs">
                      {totalItemsInSlot} 条记录
                    </div>
                  {/if}
                </div>

                <!-- 时间轴节点 -->
                <div class="flex w-1 flex-shrink-0 items-start justify-center pt-2">
                  <div
                    class="border-background dark:border-card flex size-4 items-center justify-center rounded-full border-2 shadow-sm"
                    class:bg-primary={totalItemsInSlot > 0}
                    class:bg-muted={totalItemsInSlot === 0}
                  ></div>
                </div>

                <!-- 类型列 -->
                <div class="flex-1 pl-4">
                  <div class="flex items-start gap-3 pt-2">
                    {#each visibleUniqueTypes as type (type)}
                      {@const typeItems = (itemsByTimeSlot[timeSlot] || []).filter(
                        (item) => item.marketEventType === type
                      )}
                      <div class="flex flex-1 flex-col space-y-2 {getColumnMinWidth(type)}">
                        {#if typeItems.length > 0}
                          {#each typeItems as item (item.id)}
                            <div class="event-card">
                              <EventCardFactory
                                event={item}
                                size="sm"
                                variant="compact"
                                useSpecificCard={true}
                                isHighlighted={$cardInteractionStore.selectedEventId === item.id}
                                onCardClick={handleCardClick}
                              />
                            </div>
                          {/each}
                        {/if}
                      </div>
                    {/each}
                  </div>
                </div>
              </div>
            {/each}
          {/if}
        </div>
      </section>
    </ResizablePane>

    <!-- 增加<ResizableHandle /> 用于拖动调整时间线区域和卡片区域的比例 -->
    <ResizableHandle withHandle />

    <ResizablePane defaultSize={25}>
      <div class="flex h-full items-center justify-center p-6">
        <span class="font-semibold">Content</span>
      </div>
    </ResizablePane>
  </ResizablePaneGroup>
</div>

<!-- 详细卡片模态框 -->
<EventCardDetailModal
  open={$cardInteractionStore.showDetailModal}
  event={$cardInteractionStore.selectedEvent}
  onClose={handleModalClose}
/>
