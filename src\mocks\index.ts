// MSW初始化文件
// 同步初始化模式，确保首页数据加载正常

import { mswManager } from './manager';

// 初始化状态管理
let initializationPromise: Promise<void> | null = null;
let isInitialized = false;

async function initMocks(): Promise<void> {
  // 避免重复初始化
  if (isInitialized) {
    console.log('MSW: 已经初始化完成，API 拦截正常工作');
    return;
  }

  // 如果正在初始化，等待现有的 Promise 完成
  if (initializationPromise) {
    console.log('MSW: 正在初始化中，等待完成以确保首页数据加载...');
    return initializationPromise;
  }

  // 检查环境条件
  if (
    !(
      import.meta.env.DEV ||
      import.meta.env.MODE === 'test' ||
      import.meta.env.VITE_USE_MSW === 'true'
    )
  ) {
    console.log('MSW: 当前环境不需要启用，将使用真实 API');
    isInitialized = true; // 标记为已初始化，避免重复检查
    return;
  }

  // 创建同步初始化 Promise
  initializationPromise = (async () => {
    try {
      // 检查浏览器支持
      if (!('serviceWorker' in navigator)) {
        throw new Error('浏览器不支持 Service Worker，无法启用 MSW');
      }

      console.log('MSW: 开始同步初始化，确保首页数据正常加载...');

      // 等待 MSW 完全初始化
      await mswManager.initialize();

      // 额外等待一小段时间，确保 Service Worker 完全就绪
      await new Promise((resolve) => setTimeout(resolve, 100));

      isInitialized = true;
      console.log('MSW: 同步初始化完成，首页数据加载已就绪');
    } catch (error) {
      console.error('MSW: 同步初始化失败，首页数据加载可能受影响:', error);
      // 重置状态，允许重试
      initializationPromise = null;
      throw error;
    }
  })();

  return initializationPromise;
}

// 导出状态查询函数
export function getMSWInitializationStatus() {
  return {
    isInitialized,
    isInitializing: initializationPromise !== null && !isInitialized,
    canRetry: !isInitialized && initializationPromise === null,
  };
}

// 导出重试函数
export async function retryMSWInitialization(): Promise<void> {
  if (isInitialized) {
    console.warn('MSW: 已经初始化，无需重试');
    return;
  }

  // 重置状态
  initializationPromise = null;
  isInitialized = false;

  // 重新初始化
  return initMocks();
}

/**
 * 手动停止 MSW（用于开发调试）
 */
export function stopMSW() {
  mswManager.stop();
  isInitialized = false;
  initializationPromise = null;
}

/**
 * 重启 MSW（用于开发调试）
 */
export function restartMSW() {
  stopMSW();
  return initMocks();
}

/**
 * 获取 MSW 状态（用于开发调试）
 */
export function getMSWStatus() {
  return {
    ...mswManager.getStatus(),
    ...getMSWInitializationStatus(),
  };
}

// 暴露初始化函数
export default initMocks;
