<script lang="ts">
  import CalendarIcon from "@lucide/svelte/icons/calendar";
  import type { DateRange } from "bits-ui";
  import {
    CalendarDate,
    DateFormatter,
    type DateValue,
    getLocalTimeZone
  } from "@internationalized/date";

  import { CoinMultiSelect } from '$lib/components/features/ui';
  import { Button, buttonVariants } from '$lib/components/ui/button';
  import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
  import { Label } from '$lib/components/ui/label';
  import * as Popover from '$lib/components/ui/popover';
  import { RangeCalendar } from '$lib/components/ui/range-calendar';
  import { Select, SelectContent, SelectItem, SelectTrigger } from '$lib/components/ui/select';
  import type {
    HistoricalQueryFormState,
    HistoricalQueryParams,
    MarketEventType,
  } from '$lib/types';
  import { cn } from '$lib/utils';
  import { createModuleLogger } from '$lib/utils/logger';

  const historicalFormLogger = createModuleLogger('historical-query-form');

  // 数据类型选项列表（根据API规范调整）
  const typeOptions: { value: MarketEventType; label: string }[] = [
    { value: 'LIQUIDATION_ORDER', label: '清算订单' },
    { value: 'TWAP', label: 'TWAP' },
  ];

  // 阈值选项列表
  const thresholdOptions: { value: 0 | 1 | 2 | 4; label: string }[] = [
    { value: 0, label: 'Notable' },
    { value: 1, label: 'Big' },
    { value: 2, label: 'Massive' },
    { value: 4, label: 'Enormous' },
  ];

  // 组件 Props 接口定义
  interface Props {
    loading?: boolean;
    initialParams?: Partial<HistoricalQueryParams>;
    onQuery?: (params: HistoricalQueryParams) => void;
    onReset?: () => void;
  }

  // 组件属性 - Svelte 5 语法
  let {
    loading = false,
    initialParams = {},
    onQuery,
    onReset
  }: Props = $props();

  // 生成默认日期范围（最近一周，包含今天）
  const getDefaultDateRange = () => {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - 6); // 往前推6天，加上今天共7天

    return {
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0],
    };
  };

  const defaultDates = getDefaultDateRange();

  // 日期格式化器
  const df = new DateFormatter("zh-CN", {
    dateStyle: "medium"
  });

  // 日期转换函数
  function stringToCalendarDate(dateStr: string): CalendarDate | undefined {
    if (!dateStr) return undefined;
    try {
      const [year, month, day] = dateStr.split('-').map(Number);
      return new CalendarDate(year, month, day);
    } catch {
      return undefined;
    }
  }

  function calendarDateToString(date: DateValue): string {
    return `${date.year}-${String(date.month).padStart(2, '0')}-${String(date.day).padStart(2, '0')}`;
  }

  // 表单状态 - Svelte 5 语法
  let formState = $state<HistoricalQueryFormState>({
    selectedCoins: initialParams.selectedCoins || [],
    startDate: initialParams.startDate || defaultDates.startDate,
    endDate: initialParams.endDate || defaultDates.endDate,
    selectedDataTypes: initialParams.dataTypes || 'LIQUIDATION_ORDER',
    selectedThreshold: initialParams.threshold || null,
    isQuerying: false,
    error: null,
    validationErrors: {},
    isDirty: false,
  });

  // 日期范围选择器状态 - Svelte 5 语法
  let dateRange = $state<DateRange>({
    start: undefined,
    end: undefined
  });

  // 数据类型选择器状态（用于 Select 组件的字符串值）- Svelte 5 语法
  let selectedDataTypeValue = $state('LIQUIDATION_ORDER');

  // 阈值选择器状态（用于 Select 组件的字符串值）- Svelte 5 语法
  let selectedThresholdValue = $state<string | undefined>(undefined);

  // 初始化日期范围状态
  $effect(() => {
    dateRange = {
      start: stringToCalendarDate(formState.startDate),
      end: stringToCalendarDate(formState.endDate)
    };
    selectedDataTypeValue = formState.selectedDataTypes || 'LIQUIDATION_ORDER';
    selectedThresholdValue = formState.selectedThreshold !== null ? String(formState.selectedThreshold) : undefined;
  });

  // 响应式更新表单状态 - Svelte 5 语法
  $effect(() => {
    formState.isQuerying = loading;
  });

  // 同步数据类型选择状态 - Svelte 5 语法
  $effect(() => {
    if (formState.selectedDataTypes !== selectedDataTypeValue) {
      selectedDataTypeValue = formState.selectedDataTypes || 'LIQUIDATION_ORDER';
    }
  });

  // 处理币种选择变化
  function handleCoinSelectionChange(selectedCoins: string[]) {
    formState.selectedCoins = selectedCoins;
    clearValidationError('selectedCoins');
    historicalFormLogger.debug('Coin selection changed', { selectedCoins });
  }

  // 处理币种添加
  function handleCoinAdd(coin: any) {
    historicalFormLogger.debug('Coin added', { coin });
  }

  // 处理币种移除
  function handleCoinRemove(coinSymbol: string) {
    historicalFormLogger.debug('Coin removed', { coinSymbol });
  }

  // 处理数据类型选择变化
  function handleDataTypeSelectionChange(selectedType: string) {
    formState.selectedDataTypes = selectedType as MarketEventType;
    clearValidationError('selectedDataTypes');
    historicalFormLogger.debug('Data type selection changed', { selectedType });
  }

  // 处理阈值选择变化
  function handleThresholdSelectionChange(selectedThreshold: string | undefined) {
    if (selectedThreshold === undefined) {
      formState.selectedThreshold = null;
    } else {
      formState.selectedThreshold = Number(selectedThreshold) as 0 | 1 | 2 | 4;
    }
    clearValidationError('selectedThreshold');
    historicalFormLogger.debug('Threshold selection changed', { selectedThreshold: formState.selectedThreshold });
  }

  // 处理日期范围变化
  function handleDateRangeChange(value: DateRange | undefined) {
    if (value?.start) {
      formState.startDate = calendarDateToString(value.start);
    }
    if (value?.end) {
      formState.endDate = calendarDateToString(value.end);
    }

    clearValidationError('startDate');
    clearValidationError('endDate');

    historicalFormLogger.debug('Date range changed', {
      startDate: formState.startDate,
      endDate: formState.endDate,
    });
  }

  // 验证表单
  function validateForm(): boolean {
    const errors: Record<string, string> = {};

    if (formState.selectedCoins.length === 0) {
      errors.selectedCoins = '请至少选择一个币种';
    }

    if (!formState.startDate) {
      errors.startDate = '请选择开始日期';
    }

    if (!formState.endDate) {
      errors.endDate = '请选择结束日期';
    }

    if (formState.startDate && formState.endDate) {
      const startDate = new Date(formState.startDate);
      const endDate = new Date(formState.endDate);

      if (startDate > endDate) {
        errors.dateRange = '开始日期不能晚于结束日期';
      }

      // 检查日期范围是否过大（不超过1年）
      const daysDiff = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24);
      if (daysDiff > 365) {
        errors.dateRange = '查询日期范围不能超过1年';
      }
    }

    if (!formState.selectedDataTypes) {
      errors.selectedDataTypes = '请选择一种数据类型';
    }

    formState.validationErrors = errors;
    return Object.keys(errors).length === 0;
  }

  // 清除验证错误
  function clearValidationError(field: string) {
    if (formState.validationErrors[field]) {
      const { [field]: removed, ...rest } = formState.validationErrors;
      formState.validationErrors = rest;
    }
  }

  // 处理查询提交
  function handleQuery() {
    historicalFormLogger.info('Submitting historical query', { formState });

    if (!validateForm()) {
      historicalFormLogger.warn('Form validation failed', { errors: formState.validationErrors });
      return;
    }

    const queryParams: HistoricalQueryParams = {
      selectedCoins: formState.selectedCoins,
      startDate: formState.startDate,
      endDate: formState.endDate,
      dataTypes: formState.selectedDataTypes!,
      threshold: formState.selectedThreshold || undefined,
      sortBy: 'date',
      sortOrder: 'desc',
    };

    onQuery?.(queryParams);
  }

  // 处理重置表单
  function handleReset() {
    historicalFormLogger.info('Resetting historical query form');

    formState = {
      selectedCoins: [],
      startDate: '',
      endDate: '',
      selectedDataTypes: null,
      selectedThreshold: null,
      isQuerying: false,
      error: null,
      validationErrors: {},
      isDirty: false,
    };

    dateRange = {
      start: undefined,
      end: undefined
    };
    selectedDataTypeValue = 'LIQUIDATION_ORDER';
    selectedThresholdValue = undefined;

    onReset?.();
  }

  // 获取表单摘要信息
  function getFormSummary(): string {
    const parts: string[] = [];

    if (formState.selectedCoins.length > 0) {
      parts.push(`币种: ${formState.selectedCoins.join(', ')}`);
    }

    if (formState.startDate && formState.endDate) {
      parts.push(`时间: ${formState.startDate} 至 ${formState.endDate}`);
    }

    if (formState.selectedDataTypes) {
      const selectedOption = typeOptions.find(
        (option) => option.value === formState.selectedDataTypes
      );
      parts.push(`类型: ${selectedOption?.label || formState.selectedDataTypes}`);
    }

    if (formState.selectedThreshold !== null) {
      const selectedThresholdOption = thresholdOptions.find(
        (option) => option.value === formState.selectedThreshold
      );
      parts.push(`阈值: ${selectedThresholdOption?.label || formState.selectedThreshold}`);
    }

    return parts.length > 0 ? parts.join(' | ') : '未设置查询条件';
  }
</script>

<Card class="mb-6">
  <CardHeader class="pb-4">
    <CardTitle class="text-xl font-semibold">历史数据查询条件</CardTitle>
  </CardHeader>
  <CardContent class="pt-0">
    <div class="space-y-6">
      <!-- 查询条件表单 -->
      <div class="flex flex-wrap gap-4">
        <!-- 币种多选框 -->
        <div class="flex-1 min-w-[280px] space-y-2">
          <Label for="coins">币种选择 *</Label>
          <CoinMultiSelect
            selectedCoins={formState.selectedCoins}
            placeholder="请选择币种（可多选）"
            maxSelections={10}
            onSelectionChange={handleCoinSelectionChange}
            onCoinAdd={handleCoinAdd}
            onCoinRemove={handleCoinRemove}
          />
          {#if formState.validationErrors.selectedCoins}
            <p class="text-destructive text-sm">{formState.validationErrors.selectedCoins}</p>
          {/if}
        </div>

        <!-- 时间范围选择器 -->
        <div class="flex-1 space-y-2">
          <Label for="dateRange">时间范围 *</Label>
          <Popover.Root>
            <Popover.Trigger
              class={cn(
                buttonVariants({ variant: "outline" }),
                "w-full justify-start text-left font-normal",
                !dateRange.start && "text-muted-foreground"
              )}
            >
              <CalendarIcon class="mr-2 h-4 w-4" />
              {#if dateRange && dateRange.start}
                {#if dateRange.end}
                  {df.format(dateRange.start.toDate(getLocalTimeZone()))} - {df.format(dateRange.end.toDate(getLocalTimeZone()))}
                {:else}
                  {df.format(dateRange.start.toDate(getLocalTimeZone()))}
                {/if}
              {:else}
                选择日期范围
              {/if}
            </Popover.Trigger>
            <Popover.Content class="w-auto p-0" align="start">
              <RangeCalendar
                bind:value={dateRange}
                numberOfMonths={2}
                onValueChange={handleDateRangeChange}
              />
            </Popover.Content>
          </Popover.Root>

          {#if formState.validationErrors.startDate || formState.validationErrors.endDate || formState.validationErrors.dateRange}
            <p class="text-destructive text-sm">
              {formState.validationErrors.startDate ||
                formState.validationErrors.endDate ||
                formState.validationErrors.dateRange}
            </p>
          {/if}
        </div>

        <!-- 数据类型单选框 -->
        <div class="flex-1 min-w-[200px] space-y-2">
          <Label for="dataTypes">数据类型 *</Label>
          <Select
            type="single"
            bind:value={selectedDataTypeValue}
            onValueChange={handleDataTypeSelectionChange}
          >
            <SelectTrigger class="w-full flex-shrink-0 text-sm">
              {typeOptions.find((option) => option.value === selectedDataTypeValue)?.label ||
                '请选择数据类型'}
            </SelectTrigger>
            <SelectContent>
              {#each typeOptions as option (option.value)}
                <SelectItem value={option.value} label={option.label}>
                  {option.label}
                </SelectItem>
              {/each}
            </SelectContent>
          </Select>
          {#if formState.validationErrors.selectedDataTypes}
            <p class="text-destructive text-sm">{formState.validationErrors.selectedDataTypes}</p>
          {/if}
        </div>

        <!-- 阈值选择器 -->
        <div class="flex-1 min-w-[150px] space-y-2">
          <Label for="threshold">阈值</Label>
          <Select
            type="single"
            bind:value={selectedThresholdValue}
            onValueChange={handleThresholdSelectionChange}
          >
            <SelectTrigger class="w-full flex-shrink-0 text-sm">
              {thresholdOptions.find((option) => option.value === formState.selectedThreshold)?.label ||
                '请选择阈值'}
            </SelectTrigger>
            <SelectContent>
              {#each thresholdOptions as option (option.value)}
                <SelectItem value={String(option.value)} label={option.label}>
                  {option.label}
                </SelectItem>
              {/each}
            </SelectContent>
          </Select>
          {#if formState.validationErrors.selectedThreshold}
            <p class="text-destructive text-sm">{formState.validationErrors.selectedThreshold}</p>
          {/if}
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-3">
          <Button onclick={handleQuery} disabled={loading} class="min-w-[100px]">
            {loading ? '查询中...' : '开始查询'}
          </Button>
          <Button variant="outline" onclick={handleReset} disabled={loading}>重置条件</Button>
        </div>

        <!-- 查询条件摘要 -->
        <div class="text-muted-foreground text-sm">
          {getFormSummary()}
        </div>
      </div>
    </div>
  </CardContent>
</Card>

