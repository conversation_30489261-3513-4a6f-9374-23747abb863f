<!-- src/routes/+error.svelte -->
<script lang="ts">
  import { goto } from '$app/navigation';
  import { page } from '$app/stores';
  import { Button } from '$lib/components/ui/button';
  import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
  } from '$lib/components/ui/card';
  import { createModuleLogger } from '$lib/utils/logger';

  const errorLogger = createModuleLogger('error-page');

  // 获取错误信息
  $: status = $page.status;
  $: message = $page.error?.message || '未知错误';

  // 根据状态码显示不同的错误信息
  $: errorInfo = getErrorInfo(status);

  function getErrorInfo(status: number) {
    switch (status) {
      case 404:
        return {
          title: '页面未找到',
          description: '抱歉，您访问的页面不存在。',
          emoji: '🔍',
          suggestions: ['检查 URL 是否正确', '返回首页重新开始', '使用侧边栏导航菜单'],
        };
      case 500:
        return {
          title: '服务器错误',
          description: '服务器遇到了一个错误，请稍后再试。',
          emoji: '⚠️',
          suggestions: ['刷新页面重试', '检查网络连接', '联系技术支持'],
        };
      case 403:
        return {
          title: '访问被拒绝',
          description: '您没有权限访问此页面。',
          emoji: '🚫',
          suggestions: ['检查您的登录状态', '联系管理员获取权限', '返回首页'],
        };
      default:
        return {
          title: '出现错误',
          description: '应用程序遇到了一个意外错误。',
          emoji: '❌',
          suggestions: ['刷新页面重试', '返回首页', '联系技术支持'],
        };
    }
  }

  // 导航状态
  let isNavigating = false;

  // 导航函数 - 使用 SvelteKit 原生方法
  const goHome = async () => {
    isNavigating = true;
    try {
      // 使用 SvelteKit 的 goto 进行 SPA 导航，避免页面重新加载
      await goto('/', {
        replaceState: true, // 替换当前历史记录，不留下错误页面
        noScroll: false, // 滚动到页面顶部
        keepFocus: false, // 重置焦点
        invalidateAll: true, // 重新加载所有数据
      });
    } catch (error) {
      errorLogger.error('Navigation failed, using fallback method', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        targetUrl: '/',
        method: 'goto',
      });
      // 如果 SvelteKit 导航失败，使用备用方案
      window.location.replace('/');
    } finally {
      isNavigating = false;
    }
  };

  const goBack = async () => {
    // 检查是否有历史记录，如果没有则回到首页
    if (window.history.length > 1) {
      window.history.back();
    } else {
      await goHome();
    }
  };

  const refresh = () => {
    window.location.reload();
  };
</script>

<svelte:head>
  <title>错误 {status} - Svelte Demo</title>
</svelte:head>

<div class="flex min-h-[80vh] items-center justify-center p-4">
  <Card class="w-full max-w-md">
    <CardHeader class="text-center">
      <div class="mx-auto mb-4 text-6xl">{errorInfo.emoji}</div>
      <CardTitle class="text-destructive text-2xl font-bold">
        错误 {status}
      </CardTitle>
      <CardDescription class="text-lg">
        {errorInfo.title}
      </CardDescription>
    </CardHeader>

    <CardContent class="space-y-6">
      <p class="text-muted-foreground text-center">
        {errorInfo.description}
      </p>

      {#if message && message !== errorInfo.description}
        <div class="bg-muted rounded-md p-3">
          <p class="text-muted-foreground text-sm">
            <strong>详细信息：</strong>
            {message}
          </p>
        </div>
      {/if}

      <div class="space-y-3">
        <h4 class="font-medium">建议操作：</h4>
        <ul class="text-muted-foreground space-y-1 text-sm">
          {#each errorInfo.suggestions as suggestion}
            <li class="flex items-center gap-2">
              <span class="bg-muted-foreground h-1 w-1 rounded-full"></span>
              {suggestion}
            </li>
          {/each}
        </ul>
      </div>

      <div class="flex flex-col gap-2 sm:flex-row">
        <Button onclick={goHome} disabled={isNavigating} class="flex-1">
          {#if isNavigating}
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="mr-2 h-4 w-4 animate-spin"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              stroke-width="2"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
            正在跳转...
          {:else}
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="mr-2 h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              stroke-width="2"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
              />
            </svg>
            返回首页
          {/if}
        </Button>

        <Button variant="outline" onclick={goBack} class="flex-1">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="mr-2 h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            stroke-width="2"
          >
            <path stroke-linecap="round" stroke-linejoin="round" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          返回上页
        </Button>
      </div>

      {#if status >= 500}
        <Button variant="ghost" onclick={refresh} class="w-full">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="mr-2 h-4 w-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            stroke-width="2"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
            />
          </svg>
          刷新页面
        </Button>
      {/if}
    </CardContent>
  </Card>
</div>
