// 统一导出所有服务
export * from './api';

// 便捷的服务实例导出
export { chartsApi } from './api';
export { dashboardApi } from './api';

// 数据服务 - 新的统一数据服务层
export * from './data';
export { dashboardDataService } from './data/dashboard';
export { dataQueryService } from './data/dataQuery';
export { liquidationDataService } from './data/liquidation';

// 工具服务
// export * from './utils'; // 暂时注释，待创建

// 向后兼容的导出（逐步废弃）
// @deprecated 请使用新的数据服务
export { dashboardDataService as dashboardService } from './data/dashboard';
export { dataQueryService as queryService } from './data/dataQuery';
