// src/lib/utils/logger.config.ts
import { dev } from '$app/environment';

import { LogLevel } from './logger';

/**
 * 日志配置常量
 */
export const LOGGER_CONFIG = {
  // 默认日志级别
  DEFAULT_LEVEL: dev ? LogLevel.DEBUG : LogLevel.WARN,

  // 生产环境日志级别
  PRODUCTION_LEVEL: LogLevel.WARN,

  // 开发环境日志级别
  DEVELOPMENT_LEVEL: LogLevel.DEBUG,

  // 性能监控日志级别
  PERFORMANCE_LEVEL: dev ? LogLevel.DEBUG : LogLevel.INFO,

  // API 相关日志级别
  API_LEVEL: dev ? LogLevel.DEBUG : LogLevel.WARN,

  // 主题相关日志级别
  THEME_LEVEL: dev ? LogLevel.DEBUG : LogLevel.ERROR,

  // 时区相关日志级别
  TIMEZONE_LEVEL: dev ? LogLevel.WARN : LogLevel.ERROR,

  // 格式化相关日志级别
  FORMATTERS_LEVEL: dev ? LogLevel.WARN : LogLevel.ERROR,

  // Store 相关日志级别
  STORE_LEVEL: dev ? LogLevel.DEBUG : LogLevel.WARN,

  // 错误页面日志级别
  ERROR_PAGE_LEVEL: LogLevel.ERROR,
} as const;

/**
 * 环境特定的日志配置
 */
export const getEnvironmentLogConfig = () => {
  const baseConfig = {
    timestamp: true,
    prettyPrint: dev,
  };

  if (dev) {
    return {
      ...baseConfig,
      level: LOGGER_CONFIG.DEVELOPMENT_LEVEL,
      prettyPrint: true,
    };
  } else {
    return {
      ...baseConfig,
      level: LOGGER_CONFIG.PRODUCTION_LEVEL,
      prettyPrint: false,
    };
  }
};

/**
 * 模块特定的日志级别配置
 */
export const MODULE_LOG_LEVELS = {
  performance: LOGGER_CONFIG.PERFORMANCE_LEVEL,
  api: LOGGER_CONFIG.API_LEVEL,
  store: LOGGER_CONFIG.STORE_LEVEL,
  theme: LOGGER_CONFIG.THEME_LEVEL,
  timezone: LOGGER_CONFIG.TIMEZONE_LEVEL,
  formatters: LOGGER_CONFIG.FORMATTERS_LEVEL,
  'error-page': LOGGER_CONFIG.ERROR_PAGE_LEVEL,
} as const;

/**
 * 获取模块特定的日志级别
 */
export function getModuleLogLevel(moduleName: string): LogLevel {
  return (
    MODULE_LOG_LEVELS[moduleName as keyof typeof MODULE_LOG_LEVELS] || LOGGER_CONFIG.DEFAULT_LEVEL
  );
}
