<script lang="ts">

  import { CurrencyIcon } from '$lib/components/features/ui';  import { Badge } from '$lib/components/ui/badge';
  import { Card, CardContent } from '$lib/components/ui/card';

  // 本地实现 getParameterValue 函数
  function getParameterValue(
    event: any,
    parameterId: string
  ): string | number | boolean | undefined {
    const parameter = event.parameters.find((p: any) => p.parameterId === parameterId);
    return parameter?.value;
  }

  import {
    type BaseEventCardProps,
    formatPercentage,
    formatPrice,
    formatTimeDiff,
    formatUsdAmount,
  } from '../utils';

  interface Props extends BaseEventCardProps {}

  const { event, isHighlighted = false, onCardClick }: Props = $props();

  // 提取交易量增长相关参数
  const base = getParameterValue(event, 'base') as string;
  const pair = getParameterValue(event, 'pair') as string;
  const priceDirection = getParameterValue(event, 'priceDirection') as number;
  const recentVolumeSumUsd = getParameterValue(event, 'recentVolumeSumUsd') as number;
  const percentAbnormal = getParameterValue(event, 'percentAbnormal') as number;
  const priceUsd = getParameterValue(event, 'priceUsd') as number;
  // 价格变化方向和样式
  const isPriceUp = priceDirection === 1;
  const priceChangeColor = isPriceUp
    ? 'text-green-600 dark:text-green-400'
    : 'text-red-600 dark:text-red-400';

  // 卡片样式 - 交易量增长 (VOLUME_INCREASE): 16rem - 中等复杂度
  const cardClasses = `
    transition-all duration-200 w-full
    min-w-36
    ${isHighlighted ? 'ring-primary border-primary/50 shadow-lg ring-2' : 'hover:shadow-md'}
    ${onCardClick ? 'cursor-pointer' : ''}
  `;

  // 处理卡片点击
  function handleCardClick() {
    onCardClick?.(event);
  }
</script>

<Card
  class={cardClasses}
  onclick={onCardClick ? handleCardClick : undefined}
  role={onCardClick ? 'button' : undefined}
  tabindex={onCardClick ? 0 : undefined}
>
  <CardContent class="p-3">
    <!-- 主要信息行：货币图标 + 基本信息 -->
    <div class="flex flex-wrap gap-2">
      <!-- 左侧：货币图标 + 币种信息 -->
      <div class="flex-shrink-0 self-start">
        <!-- 货币图标 -->
        <CurrencyIcon
          currencyId={event.currency}
          symbol={base}
          size="size-6"
          class="flex-shrink-0"
        />
      </div>

      <div class="flex-1">
        <!-- 第一行：状态标题 -->
        <div class="mb-1 flex flex-wrap items-center space-x-2">
          <span class="text-foreground text-sm font-bold">
            {base || '未知'}
          </span>
          <span class="text-muted-foreground text-sm">
            {pair || '未知'}
          </span>
          <span class="text-muted-foreground text-xs whitespace-nowrap">
            • {formatTimeDiff(event.date)}
          </span>
        </div>
        <!-- 关键数据文案 -->
        <div class="mt-2">
          <p class="text-foreground text-sm">
            <span class="font-semibold">
              ${base}
            </span>
            <span> volume increased by </span>
            <Badge
              variant="outline"
              class={`px-1.5 py-0.5 text-xs ${isPriceUp ? 'bg-green-600 text-white' : 'bg-red-600 text-white'}`}
            >
              {isPriceUp ? '📈' : '📉'}
              {formatPercentage(percentAbnormal)}
            </Badge>
            (<span class="font-semibold">{formatUsdAmount(recentVolumeSumUsd)}</span>) while the
            price hits
            <span class="font-semibold {priceChangeColor}">
              {formatPrice(priceUsd)}
            </span>
          </p>
        </div>
      </div>
    </div>
  </CardContent>
</Card>
