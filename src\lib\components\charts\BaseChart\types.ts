/**
 * 基础图表组件类型定义
 *
 * @category Types
 */

import type { EChartsOption } from 'echarts';

/**
 * 图表类型枚举
 */
export type ChartType = 'line' | 'bar' | 'pie' | 'scatter' | 'area' | 'radar' | 'funnel' | 'gauge';

/**
 * 基础图表属性接口
 */
export interface BaseChartProps {
  /** 图表类型 */
  chartType: ChartType;
  /** 图表数据 */
  data: any;
  /** ECharts 配置选项 */
  options?: EChartsOption;
  /** 图表标题 */
  title?: string;
  /** 图表副标题 */
  subtitle?: string;
  /** 图表高度 */
  height?: string;
  /** 图表宽度 */
  width?: string;
  /** 是否启用懒加载 */
  enableLazyLoad?: boolean;
  /** 是否启用自动调整大小 */
  enableResize?: boolean;
  /** 是否启用主题监听 */
  enableThemeWatch?: boolean;
  /** 防抖延迟时间 */
  debounceDelay?: number;
  /** 无障碍标签 */
  ariaLabel?: string;
}

/**
 * 图表事件接口
 */
export interface ChartEvents {
  /** 图表点击事件 */
  chartClick: {
    chartType: ChartType;
    name: string;
    value: any;
    [key: string]: any;
  };
  /** 图表就绪事件 */
  chartReady: {
    chart: any; // echarts.ECharts
  };
  /** 图表错误事件 */
  chartError: {
    error: Error;
  };
  /** 图表更新事件 */
  chartUpdate: {
    duration: number;
  };
}

/**
 * 图表配置构建器接口
 */
export interface ChartConfigBuilder {
  /** 构建图表配置 */
  buildConfig(data: any, options?: EChartsOption): EChartsOption;
  /** 验证数据 */
  validateData(data: any): boolean;
  /** 处理数据 */
  processData(data: any): any;
}

/**
 * 图表主题配置
 */
export interface ChartThemeConfig {
  /** 主色调 */
  primaryColor: string;
  /** 背景色 */
  backgroundColor: string;
  /** 文字颜色 */
  textColor: string;
  /** 网格线颜色 */
  gridColor: string;
  /** 坐标轴颜色 */
  axisColor: string;
  /** 图例颜色 */
  legendColor: string;
}

/**
 * 图表性能配置
 */
export interface ChartPerformanceConfig {
  /** 是否启用动画 */
  animation: boolean;
  /** 动画持续时间 */
  animationDuration: number;
  /** 是否启用渐进渲染 */
  progressive: boolean;
  /** 渐进渲染阈值 */
  progressiveThreshold: number;
  /** 是否启用数据采样 */
  sampling: boolean;
  /** 采样阈值 */
  samplingThreshold: number;
}

/**
 * 图表交互配置
 */
export interface ChartInteractionConfig {
  /** 是否启用缩放 */
  enableZoom: boolean;
  /** 是否启用刷选 */
  enableBrush: boolean;
  /** 是否启用数据区域缩放 */
  enableDataZoom: boolean;
  /** 是否启用图例交互 */
  enableLegendSelect: boolean;
  /** 是否启用工具箱 */
  enableToolbox: boolean;
}

/**
 * 图表导出配置
 */
export interface ChartExportConfig {
  /** 导出格式 */
  format: 'png' | 'jpg' | 'svg' | 'pdf';
  /** 导出质量 */
  quality: number;
  /** 导出背景色 */
  backgroundColor: string;
  /** 导出像素比 */
  pixelRatio: number;
}

/**
 * 图表状态接口
 */
export interface ChartState {
  /** 是否已初始化 */
  initialized: boolean;
  /** 是否正在加载 */
  loading: boolean;
  /** 是否有错误 */
  hasError: boolean;
  /** 错误信息 */
  error?: Error;
  /** 最后更新时间 */
  lastUpdated?: Date;
  /** 数据哈希 */
  dataHash?: string;
}

/**
 * 图表钩子返回值
 */
export interface ChartHookReturn {
  /** 图表实例 */
  chart: any; // echarts.ECharts | null
  /** 图表状态 */
  state: import('svelte/store').Writable<ChartState>;
  /** 强制更新方法 */
  forceUpdate: () => void;
  /** 调整大小方法 */
  resize: () => void;
  /** 导出图表方法 */
  exportChart: (config?: ChartExportConfig) => Promise<string>;
  /** 获取图表数据URL */
  getDataURL: (config?: ChartExportConfig) => string;
}

/**
 * 图表工具方法接口
 */
export interface ChartUtils {
  /** 生成数据哈希 */
  generateDataHash: (data: any, options?: any) => string;
  /** 验证图表配置 */
  validateConfig: (config: EChartsOption) => boolean;
  /** 合并配置 */
  mergeConfigs: (base: EChartsOption, override: EChartsOption) => EChartsOption;
  /** 格式化数据 */
  formatData: (data: any, chartType: ChartType) => any;
  /** 计算图表尺寸 */
  calculateSize: (container: HTMLElement) => { width: number; height: number };
}
