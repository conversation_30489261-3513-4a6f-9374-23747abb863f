# SvelteKit 导航解决方案

## 问题背景

在错误页面中使用 `window.location.href` 进行导航会导致：
- 页面完全重新加载
- 短暂白屏（2-5秒）
- MSW 重新初始化
- 数据重新获取

## SvelteKit 原生解决方案

### 1. 使用 `goto()` 函数（推荐）

```typescript
import { goto } from '$app/navigation';

const goHome = async () => {
  try {
    await goto('/', { 
      replaceState: true,  // 替换当前历史记录
      noScroll: false,     // 滚动到页面顶部
      keepFocus: false,    // 重置焦点
      invalidateAll: true  // 重新加载所有数据
    });
  } catch (error) {
    // 备用方案
    window.location.replace('/');
  }
};
```

**优势：**
- ✅ SPA 导航，无页面重新加载
- ✅ 保持应用状态
- ✅ 即时响应，无白屏
- ✅ 智能数据缓存

### 2. 使用 `invalidate()` 选择性更新

```typescript
import { invalidate } from '$app/navigation';

// 只重新加载特定数据
await invalidate('/api/dashboard/stats');

// 或重新加载所有数据
await invalidate(() => true);
```

### 3. 使用 `preloadData()` 预加载

```typescript
import { preloadData, goto } from '$app/navigation';

const goHome = async () => {
  // 预加载目标页面数据
  await preloadData('/');
  // 然后导航
  await goto('/');
};
```

## 完整的错误页面解决方案

```svelte
<!-- src/routes/+error.svelte -->
<script lang="ts">
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import { Button } from '$lib/components/ui/button';

  let isNavigating = false;

  const goHome = async () => {
    isNavigating = true;
    try {
      await goto('/', { 
        replaceState: true,
        invalidateAll: true
      });
    } catch (error) {
      console.error('导航失败:', error);
      window.location.replace('/');
    } finally {
      isNavigating = false;
    }
  };
</script>

<Button onclick={goHome} disabled={isNavigating}>
  {#if isNavigating}
    正在跳转...
  {:else}
    返回首页
  {/if}
</Button>
```

## 其他 SvelteKit 导航特性

### 1. 程序化导航选项

```typescript
// 基本导航
await goto('/path');

// 高级选项
await goto('/path', {
  replaceState: true,    // 替换历史记录
  noScroll: true,        // 不滚动
  keepFocus: true,       // 保持焦点
  invalidateAll: false,  // 不重新加载数据
  state: { key: 'value' } // 传递状态
});
```

### 2. 导航守卫

```typescript
import { beforeNavigate, afterNavigate } from '$app/navigation';

beforeNavigate(({ from, to, cancel }) => {
  // 导航前的逻辑
  if (shouldCancel) {
    cancel();
  }
});

afterNavigate(({ from, to }) => {
  // 导航后的逻辑
});
```

### 3. 页面状态管理

```typescript
import { pushState, replaceState } from '$app/navigation';

// 更新 URL 而不导航
pushState('/new-url', { data: 'value' });
replaceState('/new-url', { data: 'value' });
```

## 性能对比

| 方法 | 页面重新加载 | 白屏时间 | 数据重新获取 | 用户体验 |
|------|-------------|----------|-------------|----------|
| `window.location.href` | ✅ 是 | 2-5秒 | ✅ 是 | ❌ 差 |
| `window.location.replace` | ✅ 是 | 2-5秒 | ✅ 是 | ❌ 差 |
| `goto()` | ❌ 否 | 0秒 | 可选 | ✅ 优秀 |

## 最佳实践

1. **优先使用 `goto()`** - 在 SvelteKit 应用中进行导航
2. **合理使用 `invalidateAll`** - 只在需要时重新加载数据
3. **提供加载状态** - 改善用户体验
4. **错误处理** - 提供备用导航方案
5. **预加载关键页面** - 使用 `preloadData()` 提升性能

## 结论

SvelteKit 的 `goto()` 函数提供了完美的解决方案来避免错误页面导航时的白屏问题。它利用了 SPA 的优势，提供流畅的用户体验，同时保持了应用的性能和状态。
