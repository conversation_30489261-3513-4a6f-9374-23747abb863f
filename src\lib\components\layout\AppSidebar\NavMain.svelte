<!-- src/lib/components/layout/AppSidebar/NavMain.svelte -->
<script lang="ts">
  import ChevronRightIcon from '@lucide/svelte/icons/chevron-right';

  import { goto } from '$app/navigation';
  import { page } from '$app/stores';
  // shadcn-svelte 组件导入
  import * as Collapsible from '$lib/components/ui/collapsible/index.js';
  import { useSidebar } from '$lib/components/ui/sidebar/context.svelte.js';
  import * as Sidebar from '$lib/components/ui/sidebar/index.js';
  import { openTab } from '$lib/stores/ui';

  import { getTabInfoFromPath } from './config.js';
  import type { NavItem } from './types.js';
  import { isNavItemActive as checkNavItemActive } from './utils.js';

  interface Props {
    items: NavItem[];
  }

  const { items }: Props = $props();

  // 获取侧边栏状态
  const sidebar = useSidebar();

  // 为每个菜单项维护独立的展开状态
  const menuStates = $state<Record<string, boolean>>({});

  /**
   * 初始化菜单状态
   */
  function initializeMenuStates() {
    items.forEach((item) => {
      if (item.items && item.items.length > 0) {
        // 确保每个菜单项都有一个布尔值状态
        const shouldOpen = item.defaultOpen || checkNavItemActive(item, $page.url.pathname);
        menuStates[item.title] = shouldOpen;
      }
    });
  }

  // 初始化菜单状态
  initializeMenuStates();

  // 跟踪上一次的路径，只在路径变化时自动展开菜单
  let previousPath = $state<string | null>(null);

  // 监听路由变化，更新菜单状态
  $effect(() => {
    const currentPath = $page.url.pathname;

    // 只在路径真正变化时才自动展开菜单（排除初始化）
    if (previousPath !== null && currentPath !== previousPath) {
      items.forEach((item) => {
        if (item.items && item.items.length > 0) {
          // 如果当前路径匹配该菜单项，则展开它
          if (checkNavItemActive(item, currentPath)) {
            menuStates[item.title] = true;
          }
        }
      });
    }

    // 更新上一次的路径
    previousPath = currentPath;
  });

  // 监听侧边栏状态变化，优化菜单展开逻辑
  $effect(() => {
    // 当侧边栏从收起状态变为展开状态时，保持当前活跃菜单的展开状态
    if (sidebar.state === 'expanded') {
      const currentPath = $page.url.pathname;
      items.forEach((item) => {
        if (item.items && item.items.length > 0) {
          // 如果当前路径匹配该菜单项，确保它保持展开状态
          if (checkNavItemActive(item, currentPath)) {
            menuStates[item.title] = true;
          }
        }
      });
    }
  });

  /**
   * 检查子导航项是否为活跃状态
   */
  function isSubItemActive(url: string): boolean {
    return $page.url.pathname === url;
  }

  /**
   * 处理一级菜单项点击事件
   * 如果侧边栏已收起，则先展开侧边栏和菜单，暂停导航行为
   * 如果侧边栏已展开，则正常执行导航
   */
  function handleTopLevelMenuClick(item: NavItem, url?: string): boolean {
    // 检查侧边栏是否收起
    if (sidebar.state === 'collapsed') {
      // 展开侧边栏
      sidebar.setOpen(true);
      // 如果有子菜单，也展开子菜单
      if (item.items && item.items.length > 0) {
        menuStates[item.title] = true;
      }
      // 返回 false 表示暂停导航行为
      return false;
    }

    // 侧边栏已展开，允许继续导航
    if (url) {
      handleNavigation(url);
    }
    return true;
  }

  /**
   * 处理菜单展开状态变化
   * 当Collapsible状态改变时调用
   */
  function handleMenuOpenChange(item: NavItem, isOpen: boolean) {
    // 如果侧边栏收起，先展开侧边栏
    if (sidebar.state === 'collapsed') {
      sidebar.setOpen(true);
      // 确保菜单保持展开状态
      menuStates[item.title] = true;
      return;
    }

    // 侧边栏展开时，如果菜单正在展开且需要导航，则执行导航
    if (isOpen && item.url && item.url !== $page.url.pathname) {
      handleNavigation(item.url);
    }

    // 更新菜单状态
    menuStates[item.title] = isOpen;
  }

  /**
   * 处理导航点击事件
   */
  async function handleNavigation(url: string) {
    try {
      // 获取标签页信息并创建/激活标签页
      const tabInfo = getTabInfoFromPath(url);
      openTab(tabInfo);

      // 导航到目标页面
      await goto(url);
    } catch (error) {
      console.error('Navigation failed:', error);
    }
  }
</script>

<Sidebar.Group>
  <Sidebar.GroupLabel>主要功能</Sidebar.GroupLabel>
  <Sidebar.Menu>
    {#each items as item (item.title)}
      {#if item.items && item.items.length > 0}
        <!-- 有子项的可折叠导航项 -->
        {@const menuKey = item.title}
        <Collapsible.Root
          bind:open={menuStates[menuKey]}
          onOpenChange={(isOpen) => handleMenuOpenChange(item, isOpen)}
          class="group/collapsible"
        >
          {#snippet child({ props })}
            <Sidebar.MenuItem {...props}>
              <Collapsible.Trigger>
                {#snippet child({ props })}
                  <Sidebar.MenuButton
                    {...props}
                    tooltipContent={item.title}
                    data-active={checkNavItemActive(item, $page.url.pathname)}
                  >
                    {#if item.icon}
                      {@const IconComponent = item.icon}
                      <IconComponent />
                    {/if}
                    <span>{item.title}</span>
                    {#if item.badge}
                      <Sidebar.MenuBadge>{item.badge}</Sidebar.MenuBadge>
                    {/if}
                    <ChevronRightIcon
                      class="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"
                    />
                  </Sidebar.MenuButton>
                {/snippet}
              </Collapsible.Trigger>
              <Collapsible.Content>
                <Sidebar.MenuSub>
                  {#each item.items ?? [] as subItem (subItem.title)}
                    <Sidebar.MenuSubItem>
                      <Sidebar.MenuSubButton
                        data-active={isSubItemActive(subItem.url)}
                        onclick={() => handleNavigation(subItem.url)}
                      >
                        {#snippet child({ props })}
                          <button type="button" {...props} title={subItem.description}>
                            <span>{subItem.title}</span>
                          </button>
                        {/snippet}
                      </Sidebar.MenuSubButton>
                    </Sidebar.MenuSubItem>
                  {/each}
                </Sidebar.MenuSub>
              </Collapsible.Content>
            </Sidebar.MenuItem>
          {/snippet}
        </Collapsible.Root>
      {:else}
        <!-- 无子项的直接导航项 -->
        <Sidebar.MenuItem>
          <Sidebar.MenuButton
            tooltipContent={item.title}
            data-active={checkNavItemActive(item, $page.url.pathname)}
            onclick={() => {
              // 处理一级菜单点击逻辑
              if (item.url) {
                handleTopLevelMenuClick(item, item.url);
              }
            }}
          >
            {#if item.icon}
              {@const IconComponent = item.icon}
              <IconComponent />
            {/if}
            <span>{item.title}</span>
            {#if item.badge}
              <Sidebar.MenuBadge>{item.badge}</Sidebar.MenuBadge>
            {/if}
          </Sidebar.MenuButton>
        </Sidebar.MenuItem>
      {/if}
    {/each}
  </Sidebar.Menu>
</Sidebar.Group>
