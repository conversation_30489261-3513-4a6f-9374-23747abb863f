/**
 * 键盘快捷键管理系统
 *
 * @category Stores
 */

import { writable } from 'svelte/store';

import { goto } from '$app/navigation';

import { notifications } from './notifications';

export interface KeyboardShortcut {
  key: string;
  ctrl?: boolean;
  alt?: boolean;
  shift?: boolean;
  description: string;
  action: () => void;
}

function createKeyboardStore() {
  const { subscribe, set, update } = writable<KeyboardShortcut[]>([]);

  // 默认快捷键
  const defaultShortcuts: KeyboardShortcut[] = [
    // 注意：侧边栏切换现在由 shadcn-svelte 内置快捷键处理 (Ctrl+B)
    // {
    //   key: 'b',
    //   ctrl: true,
    //   description: '切换侧边栏',
    //   action: () => {
    //     // 新的侧边栏使用 shadcn-svelte 内置切换功能
    //     notifications.info('侧边栏已切换');
    //   }
    // },
    {
      key: '1',
      ctrl: true,
      description: '跳转到仪表板',
      action: () => {
        goto('/');
        notifications.info('已跳转到仪表板');
      },
    },
    {
      key: '2',
      ctrl: true,
      description: '跳转到清算监控',
      action: () => {
        goto('/liquidation');
        notifications.info('已跳转到清算监控');
      },
    },
    {
      key: '3',
      ctrl: true,
      description: '跳转到数据查询',
      action: () => {
        goto('/query');
        notifications.info('已跳转到数据查询');
      },
    },
    {
      key: 'r',
      ctrl: true,
      shift: true,
      description: '刷新当前页面数据',
      action: () => {
        // 触发页面数据刷新
        window.dispatchEvent(new CustomEvent('refresh-data'));
        notifications.info('正在刷新数据...');
      },
    },
    {
      key: 'k',
      ctrl: true,
      description: '显示快捷键帮助',
      action: () => {
        showShortcutHelp();
      },
    },
  ];

  // 初始化默认快捷键
  set(defaultShortcuts);

  function showShortcutHelp() {
    const shortcuts = defaultShortcuts
      .map((s) => {
        const keys = [];
        if (s.ctrl) keys.push('Ctrl');
        if (s.alt) keys.push('Alt');
        if (s.shift) keys.push('Shift');
        keys.push(s.key.toUpperCase());
        return `${keys.join(' + ')}: ${s.description}`;
      })
      .join('\n');

    notifications.info('键盘快捷键', shortcuts, 10000);
  }

  function handleKeydown(event: KeyboardEvent) {
    // 忽略在输入框中的按键
    if (
      event.target instanceof HTMLInputElement ||
      event.target instanceof HTMLTextAreaElement ||
      event.target instanceof HTMLSelectElement
    ) {
      return;
    }

    const shortcuts = defaultShortcuts;

    for (const shortcut of shortcuts) {
      const ctrlMatch = shortcut.ctrl ? event.ctrlKey : !event.ctrlKey;
      const altMatch = shortcut.alt ? event.altKey : !event.altKey;
      const shiftMatch = shortcut.shift ? event.shiftKey : !event.shiftKey;
      const keyMatch = event.key.toLowerCase() === shortcut.key.toLowerCase();

      if (ctrlMatch && altMatch && shiftMatch && keyMatch) {
        event.preventDefault();
        shortcut.action();
        break;
      }
    }
  }

  return {
    subscribe,

    /**
     * 初始化键盘快捷键监听
     */
    init: () => {
      if (typeof window !== 'undefined') {
        window.addEventListener('keydown', handleKeydown);
      }
    },

    /**
     * 销毁键盘快捷键监听
     */
    destroy: () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('keydown', handleKeydown);
      }
    },

    /**
     * 添加自定义快捷键
     */
    addShortcut: (shortcut: KeyboardShortcut) => {
      update((shortcuts) => [...shortcuts, shortcut]);
    },

    /**
     * 移除快捷键
     */
    removeShortcut: (key: string) => {
      update((shortcuts) => shortcuts.filter((s) => s.key !== key));
    },

    /**
     * 显示快捷键帮助
     */
    showHelp: showShortcutHelp,
  };
}

export const keyboardStore = createKeyboardStore();
