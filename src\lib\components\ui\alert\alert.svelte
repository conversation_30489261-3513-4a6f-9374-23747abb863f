<script lang="ts" module>
  import { tv, type VariantProps } from 'tailwind-variants';

  export const alertVariants = tv({
    base: 'relative grid w-full grid-cols-[0_1fr] items-start gap-y-0.5 rounded-lg border px-4 py-3 text-sm has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] has-[>svg]:gap-x-3 [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current',
    variants: {
      variant: {
        default: 'bg-card text-card-foreground border-border',
        destructive:
          'border-destructive/50 text-destructive bg-destructive/10 *:data-[slot=alert-description]:text-destructive/90 [&>svg]:text-current',
        warning:
          'border-yellow-500/50 text-yellow-800 bg-yellow-50 dark:border-yellow-500/50 dark:text-yellow-200 dark:bg-yellow-950/50 *:data-[slot=alert-description]:text-yellow-700 dark:*:data-[slot=alert-description]:text-yellow-300 [&>svg]:text-current',
        success:
          'border-green-500/50 text-green-800 bg-green-50 dark:border-green-500/50 dark:text-green-200 dark:bg-green-950/50 *:data-[slot=alert-description]:text-green-700 dark:*:data-[slot=alert-description]:text-green-300 [&>svg]:text-current',
        info: 'border-blue-500/50 text-blue-800 bg-blue-50 dark:border-blue-500/50 dark:text-blue-200 dark:bg-blue-950/50 *:data-[slot=alert-description]:text-blue-700 dark:*:data-[slot=alert-description]:text-blue-300 [&>svg]:text-current',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  });

  export type AlertVariant = VariantProps<typeof alertVariants>['variant'];
</script>

<script lang="ts">
  import type { HTMLAttributes } from 'svelte/elements';

  import { cn, type WithElementRef } from '$lib/utils.js';

  let {
    ref = $bindable(null),
    class: className,
    variant = 'default',
    children,
    ...restProps
  }: WithElementRef<HTMLAttributes<HTMLDivElement>> & {
    variant?: AlertVariant;
  } = $props();
</script>

<div
  bind:this={ref}
  data-slot="alert"
  class={cn(alertVariants({ variant }), className)}
  {...restProps}
  role="alert"
>
  {@render children?.()}
</div>
