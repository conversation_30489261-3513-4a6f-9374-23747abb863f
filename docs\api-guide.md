# API 文档指南

本项目使用 TypeDoc 自动生成 API 文档，提供了完整的类型定义、接口说明和使用示例。

## 📚 文档结构

### [自动生成的 API 文档](./api/README.md)

完整的 TypeScript API 文档，包含所有公开的类、接口、函数和常量。

#### 主要模块

1. **[Services 模块](./api/services/README.md)**

   - 数据服务类和方法
   - API 调用封装
   - 错误处理机制

2. **[Stores 模块](./api/stores/README.md)**

   - 状态管理 stores
   - 响应式状态和方法
   - 状态更新函数

3. **[Types 模块](./api/types/README.md)**

   - TypeScript 类型定义
   - 接口和类型别名
   - 数据结构说明

4. **[Constants 模块](./api/constants/README.md)**
   - 应用常量定义
   - 配置参数
   - 枚举值

## 🔍 如何使用 API 文档

### 查找特定功能

1. **服务功能**: 在 [Services](./api/services/README.md) 模块中查找
2. **状态管理**: 在 [Stores](./api/stores/README.md) 模块中查找
3. **数据类型**: 在 [Types](./api/types/README.md) 模块中查找
4. **配置常量**: 在 [Constants](./api/constants/README.md) 模块中查找

### 阅读文档技巧

- **类和接口**: 查看属性和方法说明
- **函数**: 查看参数类型和返回值
- **示例代码**: 参考 `@example` 标签中的用法
- **类型关系**: 查看继承和实现关系

## 🛠️ 文档维护

### 更新 API 文档

```bash
# 生成最新的 API 文档
npm run docs:api

# 监听文件变化并自动更新
npm run docs:api:watch

# 本地预览文档
npm run docs:serve
```

### 添加文档注释

在代码中使用 JSDoc 注释来改善文档质量：

````typescript
/**
 * 函数描述
 *
 * 详细说明函数的功能和用途
 *
 * @param param1 - 参数1的说明
 * @param param2 - 参数2的说明
 * @returns 返回值说明
 *
 * @example
 * ```typescript
 * const result = myFunction('value1', 'value2');
 * console.log(result);
 * ```
 *
 * @category Services
 */
export function myFunction(param1: string, param2: string): string {
  return `${param1}-${param2}`;
}
````

### 文档标签说明

- `@param` - 参数说明
- `@returns` - 返回值说明
- `@example` - 使用示例
- `@category` - 分类标签
- `@deprecated` - 废弃标记
- `@since` - 版本信息
- `@see` - 相关链接

## 📖 相关文档

- [组件文档](./components/README.md) - 组件使用指南
- [开发指南](./development/README.md) - 开发流程和规范
- [项目概览](../README.md) - 项目整体介绍

## 🔗 快速链接

### 常用服务

- [DashboardDataService](./api/services/classes/DashboardDataService.md) - 仪表板数据服务
- [DataQueryService](./api/services/classes/DataQueryService.md) - 数据查询服务
- [LiquidationDataService](./api/services/classes/LiquidationDataService.md) - 爆仓数据服务

### 主要 Stores

- [dashboardStore](./api/stores/variables/dashboardStore.md) - 仪表板状态管理
- [dataQueryStore](./api/stores/variables/dataQueryStore.md) - 数据查询状态管理
- [liquidationStore](./api/stores/variables/liquidationStore.md) - 爆仓数据状态管理

### 核心类型

- [DashboardStats](./api/types/interfaces/DashboardStats.md) - 仪表板统计数据
- [ChartData](./api/types/interfaces/ChartData.md) - 图表数据结构
- [QueryParams](./api/types/interfaces/QueryParams.md) - 查询参数

### 重要常量

- [API_ENDPOINTS](./api/constants/variables/API_ENDPOINTS.md) - API 端点常量
- [CHART_COLORS](./api/constants/variables/CHART_COLORS.md) - 图表颜色配置
- [TIME_ZONES](./api/constants/variables/TIME_ZONES.md) - 时区配置
