<!-- src/lib/components/liquidation/charts/PercentStackedAreaChart.svelte -->
<script lang="ts">
  import type { EChartsOption } from 'echarts';
  import * as echarts from 'echarts';
  import { createEventDispatcher, onDestroy, onMount } from 'svelte';

  import type { LiquidationData } from '$lib/types';
  import { formatDate, formatNumber } from '$lib/utils/formatters';

  const dispatch = createEventDispatcher();

  export let options: EChartsOption = {};
  export let data: LiquidationData[] = [];
  export let title: string = '多/空清算金额占比趋势';
  export let subtitle: string = '';
  export let groupBy: 'side' | 'coinType' = 'side';

  let chartDom: HTMLDivElement;
  let myChart: echarts.ECharts;

  // 处理数据，按时间和分组维度聚合
  function processData(
    data: LiquidationData[],
    groupBy: 'side' | 'coinType'
  ): {
    times: string[];
    series: { name: string; data: number[] }[];
    rawValues: { [key: string]: number[] };
  } {
    if (!data || data.length === 0) {
      return { times: [], series: [], rawValues: {} };
    }

    // 按天或小时对时间进行分组
    const timeFormat = data.length > 50 ? 'date' : 'datetime';
    const timeMap = new Map<string, Map<string, number>>();

    // 初始化所有可能的分组值
    const groupValues = new Set<string>();

    // 遍历数据，进行时间和分组聚合
    data.forEach((item) => {
      const timeKey = formatDate(item.datetime, timeFormat);
      const groupKey = groupBy === 'side' ? (item.side === 1 ? '多头' : '空头') : item.coinType;

      groupValues.add(groupKey);

      if (!timeMap.has(timeKey)) {
        timeMap.set(timeKey, new Map<string, number>());
      }

      const groupMap = timeMap.get(timeKey)!;
      groupMap.set(groupKey, (groupMap.get(groupKey) || 0) + item.amountUsd);
    });

    // 排序时间
    const times = Array.from(timeMap.keys()).sort();

    // 保存原始值用于提示框
    const rawValues: { [key: string]: number[] } = {};

    // 为每个分组创建系列数据并计算百分比
    const allGroupValues = Array.from(groupValues);

    // 先计算每个时间点的总和
    const totalsByTime = times.map((time) => {
      const groupMap = timeMap.get(time)!;
      let total = 0;
      allGroupValues.forEach((groupValue) => {
        total += groupMap.get(groupValue) || 0;
      });
      return total;
    });

    // 为每个分组创建百分比系列
    const series = allGroupValues.map((groupValue) => {
      const rawData: number[] = [];
      const percentData = times.map((time, index) => {
        const groupMap = timeMap.get(time)!;
        const value = groupMap.get(groupValue) || 0;
        rawData.push(value);
        return totalsByTime[index] === 0 ? 0 : (value / totalsByTime[index]) * 100;
      });

      // 保存原始值
      rawValues[groupValue] = rawData;

      return {
        name: groupValue,
        data: percentData,
      };
    });

    return { times, series, rawValues };
  }

  const baseChartOptions: EChartsOption = {
    title: {
      text: title,
      subtext: subtitle,
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
      formatter: function (params: any, rawValues: any) {
        let result = params[0].name + '<br/>';

        // 显示各系列的百分比和原始值
        params.forEach((param: any) => {
          const percent = param.value.toFixed(1);
          const rawValue = rawValues[param.seriesName]
            ? rawValues[param.seriesName][param.dataIndex]
            : 0;
          result += `${param.marker} ${param.seriesName}: ${percent}% ($${formatNumber(rawValue, 2)})<br/>`;
        });

        return result;
      },
    },
    legend: {
      bottom: '0%',
      data: [],
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '16%',
      top: '15%',
      containLabel: true,
    },
    dataZoom: [
      {
        type: 'inside',
        start: 0,
        end: 100,
        zoomOnMouseWheel: true,
        moveOnMouseMove: true,
        moveOnMouseWheel: false,
      },
      {
        type: 'slider',
        start: 0,
        end: 100,
        bottom: '2%',
        height: 20,
        handleIcon:
          'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23.1h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
        handleSize: '80%',
      },
    ],
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: [],
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}%',
      },
      max: 100,
    },
    series: [],
  };

  let chartInitialized = false;

  function initializeChart() {
    if (chartInitialized) return;
    myChart = echarts.init(chartDom);
    updateChart();
    chartInitialized = true;

    myChart.on('click', (params) => {
      dispatch('chartClick', {
        chartType: 'percentStackedArea',
        name: params.seriesName,
        value: params.value,
        dataIndex: params.dataIndex,
      });
    });
  }

  function updateChart() {
    if (!myChart) return;

    const processedData = processData(data, groupBy);

    // 多头/空头颜色配置
    const colorMap = {
      多头: '#52c41a',
      空头: '#f5222d',
      Major: '#1890ff',
      Altcoin: '#722ed1',
    };

    const seriesConfig = processedData.series.map((s) => {
      return {
        name: s.name,
        type: 'line',
        stack: 'Total',
        areaStyle: {},
        emphasis: {
          focus: 'series',
        },
        data: s.data,
        color: colorMap[s.name as keyof typeof colorMap],
        smooth: true,
      };
    });

    // 自定义提示框函数，显示原始值和百分比
    const customTooltip = {
      formatter: function (params: any) {
        let result = params[0].name + '<br/>';

        params.forEach((param: any) => {
          const percent = param.value.toFixed(1);
          const rawValue = processedData.rawValues[param.seriesName]
            ? processedData.rawValues[param.seriesName][param.dataIndex]
            : 0;
          result += `${param.marker} ${param.seriesName}: ${percent}% ($${formatNumber(rawValue, 2)})<br/>`;
        });

        return result;
      },
    };

    myChart.setOption(
      {
        ...baseChartOptions,
        ...options,
        title: {
          ...baseChartOptions.title,
          text: title,
          subtext: subtitle,
        },
        tooltip: customTooltip,
        xAxis: {
          ...baseChartOptions.xAxis,
          data: processedData.times,
        },
        legend: {
          ...baseChartOptions.legend,
          data: processedData.series.map((s) => s.name),
        },
        series: seriesConfig,
      },
      { notMerge: false }
    );
  }

  onMount(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            initializeChart();
            observer.unobserve(chartDom);
          }
        });
      },
      { threshold: 0.1 }
    );

    observer.observe(chartDom);

    const resizeChart = () => {
      if (myChart) {
        myChart.resize();
      }
    };

    window.addEventListener('resize', resizeChart);

    onDestroy(() => {
      window.removeEventListener('resize', resizeChart);
      if (myChart) {
        myChart.dispose();
      }
    });
  });

  $: if (myChart && chartInitialized && (data || options || title || subtitle || groupBy)) {
    updateChart();
  }
</script>

<div
  bind:this={chartDom}
  style="width: 100%; height: 100%;"
  aria-label="{title} - {subtitle}"
></div>
