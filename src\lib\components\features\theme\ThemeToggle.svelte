<!-- src/lib/components/features/theme/ThemeToggle.svelte -->
<script lang="ts">
  import { mode, toggleMode } from 'mode-watcher';
  import Sun from '@lucide/svelte/icons/sun';
  import Moon from '@lucide/svelte/icons/moon';

  import { But<PERSON> } from '$lib/components/ui/button';
  import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
  } from '$lib/components/ui/tooltip';

  // 使用 Svelte 5 的 $derived 语法替换 $: 响应式语句
  // mode.current 提供当前主题值
  const isDark = $derived(mode.current === 'dark');
</script>

<TooltipProvider>
  <Tooltip>
    <TooltipTrigger>
      <Button variant="ghost" size="icon" onclick={toggleMode} class="h-9 w-9">
        {#if isDark}
          <!-- 太阳图标 (浅色模式) -->
          <Sun class="h-4 w-4" />
        {:else}
          <!-- 月亮图标 (深色模式) -->
          <Moon class="h-4 w-4" />
        {/if}
      </Button>
    </TooltipTrigger>
    <TooltipContent>
      <p>{isDark ? '切换到浅色模式' : '切换到深色模式'}</p>
    </TooltipContent>
  </Tooltip>
</TooltipProvider>

