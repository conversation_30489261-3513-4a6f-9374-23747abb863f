// 时间相关常量
import type { TimezoneConfig } from '$lib/stores/features/timezone';

/**
 * 扩展的时区配置列表
 * 包含常用时区和详细的显示信息
 */
export const TIME_ZONES: TimezoneConfig[] = [
  // 特殊选项
  {
    id: 'browser',
    displayName: '浏览器本地时区',
    offset: '自动检测',
    description: '使用浏览器检测到的本地时区',
    isCommon: true,
  },

  // 常用时区
  {
    id: 'UTC',
    displayName: '协调世界时',
    offset: 'UTC+00:00',
    description: 'UTC, GMT',
    isCommon: true,
  },
  {
    id: 'Asia/Shanghai',
    displayName: '北京时间',
    offset: 'UTC+08:00',
    description: '中国标准时间 (CST)',
    isCommon: true,
  },
  {
    id: 'America/New_York',
    displayName: '纽约时间',
    offset: 'UTC-05:00/-04:00',
    description: '美国东部时间 (EST/EDT)',
    isCommon: true,
  },
  {
    id: 'Europe/London',
    displayName: '伦敦时间',
    offset: 'UTC+00:00/+01:00',
    description: '英国时间 (GMT/BST)',
    isCommon: true,
  },
  {
    id: 'Asia/Tokyo',
    displayName: '东京时间',
    offset: 'UTC+09:00',
    description: '日本标准时间 (JST)',
    isCommon: true,
  },
  {
    id: 'America/Los_Angeles',
    displayName: '洛杉矶时间',
    offset: 'UTC-08:00/-07:00',
    description: '美国太平洋时间 (PST/PDT)',
    isCommon: true,
  },
  {
    id: 'Europe/Paris',
    displayName: '巴黎时间',
    offset: 'UTC+01:00/+02:00',
    description: '中欧时间 (CET/CEST)',
    isCommon: true,
  },
  {
    id: 'Asia/Hong_Kong',
    displayName: '香港时间',
    offset: 'UTC+08:00',
    description: '香港标准时间 (HKT)',
    isCommon: true,
  },
  {
    id: 'Asia/Singapore',
    displayName: '新加坡时间',
    offset: 'UTC+08:00',
    description: '新加坡标准时间 (SGT)',
    isCommon: true,
  },

  // 其他重要时区
  {
    id: 'America/Chicago',
    displayName: '芝加哥时间',
    offset: 'UTC-06:00/-05:00',
    description: '美国中部时间 (CST/CDT)',
    isCommon: false,
  },
  {
    id: 'America/Denver',
    displayName: '丹佛时间',
    offset: 'UTC-07:00/-06:00',
    description: '美国山地时间 (MST/MDT)',
    isCommon: false,
  },
  {
    id: 'Europe/Berlin',
    displayName: '柏林时间',
    offset: 'UTC+01:00/+02:00',
    description: '中欧时间 (CET/CEST)',
    isCommon: false,
  },
  {
    id: 'Europe/Moscow',
    displayName: '莫斯科时间',
    offset: 'UTC+03:00',
    description: '莫斯科标准时间 (MSK)',
    isCommon: false,
  },
  {
    id: 'Asia/Seoul',
    displayName: '首尔时间',
    offset: 'UTC+09:00',
    description: '韩国标准时间 (KST)',
    isCommon: false,
  },
  {
    id: 'Asia/Kolkata',
    displayName: '新德里时间',
    offset: 'UTC+05:30',
    description: '印度标准时间 (IST)',
    isCommon: false,
  },
  {
    id: 'Asia/Dubai',
    displayName: '迪拜时间',
    offset: 'UTC+04:00',
    description: '海湾标准时间 (GST)',
    isCommon: false,
  },
  {
    id: 'Australia/Sydney',
    displayName: '悉尼时间',
    offset: 'UTC+10:00/+11:00',
    description: '澳大利亚东部时间 (AEST/AEDT)',
    isCommon: false,
  },
  {
    id: 'Pacific/Auckland',
    displayName: '奥克兰时间',
    offset: 'UTC+12:00/+13:00',
    description: '新西兰标准时间 (NZST/NZDT)',
    isCommon: false,
  },
  {
    id: 'America/Sao_Paulo',
    displayName: '圣保罗时间',
    offset: 'UTC-03:00/-02:00',
    description: '巴西时间 (BRT/BRST)',
    isCommon: false,
  },
] as const;

/**
 * 获取常用时区列表
 */
export const COMMON_TIME_ZONES = TIME_ZONES.filter((tz) => tz.isCommon);

/**
 * 获取所有时区列表
 */
export const ALL_TIME_ZONES = TIME_ZONES;

/**
 * 兼容性：保持原有的简单时区配置格式
 * @deprecated 建议使用 TIME_ZONES 或 COMMON_TIME_ZONES
 */
export const LEGACY_TIME_ZONES = [
  { value: 'UTC', label: 'UTC' },
  { value: 'Asia/Shanghai', label: '北京时间 (UTC+8)' },
  { value: 'America/New_York', label: '纽约时间 (UTC-5/-4)' },
  { value: 'Europe/London', label: '伦敦时间 (UTC+0/+1)' },
  { value: 'Asia/Tokyo', label: '东京时间 (UTC+9)' },
] as const;

export const TIME_RANGES = {
  DASHBOARD: [
    { value: 'hour', label: '最近1小时' },
    { value: 'day', label: '最近24小时' },
    { value: 'week', label: '最近7天' },
    { value: 'month', label: '最近30天' },
    { value: 'custom', label: '自定义' },
  ],

  DATA_QUERY: [
    { value: 'today', label: '今天' },
    { value: 'yesterday', label: '昨天' },
    { value: 'week', label: '最近7天' },
    { value: 'month', label: '最近30天' },
    { value: 'custom', label: '自定义范围' },
  ],
} as const;

export const DATE_FORMATS = {
  DATE: 'YYYY-MM-DD',
  DATETIME: 'YYYY-MM-DD HH:mm:ss',
  TIME: 'HH:mm:ss',
  MONTH: 'YYYY-MM',
  YEAR: 'YYYY',
  ISO: 'YYYY-MM-DDTHH:mm:ss.sssZ',
} as const;

export const REFRESH_INTERVALS = {
  REAL_TIME: 1000, // 1秒
  FAST: 5000, // 5秒
  NORMAL: 30000, // 30秒
  SLOW: 60000, // 1分钟
  VERY_SLOW: 300000, // 5分钟
} as const;

export const TIME_UNITS = {
  SECOND: 1000,
  MINUTE: 60 * 1000,
  HOUR: 60 * 60 * 1000,
  DAY: 24 * 60 * 60 * 1000,
  WEEK: 7 * 24 * 60 * 60 * 1000,
  MONTH: 30 * 24 * 60 * 60 * 1000,
  YEAR: 365 * 24 * 60 * 60 * 1000,
} as const;
