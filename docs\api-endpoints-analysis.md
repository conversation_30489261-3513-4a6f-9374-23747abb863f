# Svelte 组件 HTTP 端点和数据结构分析报告

> 📅 生成时间：2025-06-15  
> 📂 分析范围：`src/lib/components/` 目录下所有 Svelte 组件

## 📊 概述

本文档详细分析了项目中所有 Svelte 组件使用的 HTTP 端点、请求/响应数据结构以及 API 调用模式。

## 🔗 HTTP 端点清单

### 1. 仪表板相关端点

| 端点 | 方法 | 用途 | 组件使用 |
|------|------|------|----------|
| `/api/dashboard/stats` | POST | 获取仪表板统计数据 | Dashboard.svelte |
| `/api/dashboard/overview` | GET | 获取仪表板概览数据 | Dashboard.svelte |

### 2. 图表数据端点

| 端点 | 方法 | 用途 | 组件使用 |
|------|------|------|----------|
| `/api/charts/all` | POST | 获取所有图表数据 | Dashboard.svelte |
| `/api/charts/bar` | GET | 获取柱状图数据 | ChartPanel.svelte |
| `/api/charts/line` | GET | 获取折线图数据 | ChartPanel.svelte |
| `/api/charts/pie` | GET | 获取饼图数据 | ChartPanel.svelte |
| `/api/charts/scatter` | GET | 获取散点图数据 | ChartPanel.svelte |

### 3. 数据查询端点

| 端点 | 方法 | 用途 | 组件使用 |
|------|------|------|----------|
| `/api/data/query` | POST | 执行数据查询 | DataQuery.svelte |
| `/api/data/export` | GET | 导出数据 | DataQuery.svelte |

### 4. 🚀 加密货币清算数据端点

| 端点 | 方法 | 用途 | 组件使用 |
|------|------|------|----------|
| `/api/liquidation/snapshot` | POST | 获取清算快照数据 | LiquidationDashboard.svelte |
| `/api/liquidation/trend` | GET | 获取清算趋势数据 | LiquidationDashboard.svelte |
| `/api/liquidation/trend-detailed` | GET | 获取详细清算趋势数据 | LiquidationDashboard.svelte |

## 📋 详细数据结构

### 清算快照数据 API

**端点：** `POST /api/liquidation/snapshot`

**请求结构：**
```typescript
interface SnapshotRequest {
  timeRange: '1h' | '4h' | '12h' | '24h';
}
```

**响应结构：**
```typescript
interface SnapshotResponse {
  status: 'success' | 'error';
  data: {
    data: LiquidationData[];
    stats: LiquidationStats;
  };
  message?: string;
}

interface LiquidationData {
  datetime: string;
  symbol: string;
  side: 'long' | 'short';
  amount: number;
  price: number;
  coinType: 'mainstream' | 'altcoin';
}

interface LiquidationStats {
  totalAmount: number;
  totalCount: number;
  longAmount: number;
  shortAmount: number;
  longShortRatio: number;
}
```

### 图表数据 API

**端点：** `POST /api/charts/all`

**请求结构：**
```typescript
interface ChartDataRequest {
  startTime: string;
  endTime: string;
  selectedTimeZone: string;
}
```

**响应结构：**
```typescript
interface ChartDataResponse {
  status: 'success' | 'error';
  data: {
    barChartData: BarChartItem[];
    lineChartData: LineChartItem[];
    pieChartData: PieChartItem[];
    scatterChartData: ScatterChartItem[];
  };
  message?: string;
}
```

### 仪表板统计数据 API

**端点：** `POST /api/dashboard/stats`

**请求结构：**
```typescript
interface DashboardStatsRequest {
  startTime: string;
  endTime: string;
  selectedTimeZone: string;
}
```

**响应结构：**
```typescript
interface DashboardStatsResponse {
  status: 'success' | 'error';
  data: DashboardStats;
  message?: string;
}

interface DashboardStats {
  totalUsers: number;
  monthlyRevenue: number;
  conversionRate: number;
  activeUsers: number;
}
```

### 数据查询 API

**端点：** `POST /api/data/query`

**请求结构：**
```typescript
interface QueryRequest {
  keyword?: string;
  status: string;
  dateRange: [string, string];
  page: number;
  pageSize: number;
}
```

**响应结构：**
```typescript
interface QueryResponse {
  status: 'success' | 'error';
  data: {
    items: QueryResultItem[];
    pagination: {
      currentPage: number;
      totalPages: number;
      totalItems: number;
      itemsPerPage: number;
    };
  };
  message?: string;
}
```

## 🏗️ 组件 API 使用模式

### Dashboard.svelte
- **数据获取方式：** 通过 `dashboardStore.loadDashboardData()` 间接调用
- **API 端点：** `/api/dashboard/stats`, `/api/charts/all`
- **特色功能：**
  - 自动刷新机制（可配置间隔）
  - 并行加载统计数据和图表数据
  - 统一错误处理

### LiquidationDashboard.svelte
- **数据获取方式：** 通过 `liquidationStore` 状态管理
- **API 端点：** `/api/liquidation/snapshot`, `/api/liquidation/trend-detailed`
- **特色功能：**
  - 多时间范围筛选（1h, 4h, 12h, 24h）
  - 双视图模式（全市场 vs 主流/山寨对比）
  - 实时自动刷新（1-15分钟可配置）
  - 时间粒度控制（1h, 4h, 1d, 1w）

### DataQuery.svelte
- **数据获取方式：** 通过 `dataQueryStore.executeQuery()` 调用
- **API 端点：** `/api/data/query`
- **特色功能：**
  - 分页查询支持
  - 多条件筛选
  - 响应式布局（桌面/移动端适配）

## 🔧 技术架构特点

### 1. 统一错误处理
所有 API 调用都实现了统一的错误处理机制：
```typescript
try {
  const response = await fetch(url, options);
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }
  return await response.json();
} catch (error) {
  // 统一错误处理逻辑
  throw error;
}
```

### 2. MSW 集成
开发环境使用 Mock Service Worker 进行数据模拟：
- 业务代码保持纯净，只关注正常 API 调用
- MSW 自动拦截请求并返回模拟数据
- 支持条件启用（开发/测试环境）
- 网络层拦截，更接近真实环境

### 4. 状态管理模式
通过 Svelte stores 实现状态管理：
- 派生 stores 提供细粒度状态访问
- 统一的加载状态和错误状态管理
- 支持自动刷新和批量数据更新

## 📈 加密货币清算数据特色

### 实时监控能力
- **刷新频率：** 支持 1分钟到15分钟的自动刷新
- **数据延迟：** 近实时数据更新
- **监控范围：** 全市场清算事件

### 多维度数据分析
- **时间维度：** 快照（1h-24h）+ 趋势（7d-90d）
- **币种维度：** 主流币 vs 山寨币对比
- **方向维度：** 多头 vs 空头清算分析
- **金额维度：** 按订单规模分布统计

### 可视化图表支持
- **KPI 卡片：** 核心清算指标概览
- **饼图：** 多/空清算金额占比
- **双Y轴折线图：** 主流币 vs 山寨币对比
- **100%堆叠柱状图：** 清算金额比例趋势

## 🔍 API 配置管理

### 环境变量配置
```bash
# .env.local
VITE_API_BASE_URL=/api     # API 基础路径
VITE_USE_MSW=true          # 强制启用 MSW
```

### 常量定义
```typescript
// src/lib/constants/api.ts
export const API_ENDPOINTS = {
  DASHBOARD_STATS: '/dashboard/stats',
  LIQUIDATION_SNAPSHOT: '/liquidation/snapshot',
  // ... 其他端点
} as const;

export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_BASE_URL || '/api',
  TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3,
} as const;
```

## 📝 总结

本项目的 API 架构具有以下优势：

1. **统一性：** 所有组件遵循统一的 API 调用模式
2. **可靠性：** 完善的错误处理和 MSW 数据模拟
3. **可维护性：** 清晰的分层架构和状态管理
4. **可扩展性：** 模块化的服务层设计
5. **开发友好：** MSW 集成提供良好的开发体验

特别是在加密货币清算数据处理方面，实现了实时监控、多维度分析和丰富的可视化功能，为用户提供了专业的市场分析工具。
