// src/lib/utils/logger.test.ts
import { beforeEach, describe, expect, it, vi } from 'vitest';

import { createModuleLogger, Logger, logger, LogLevel } from './logger';

// Mock console methods
const mockConsole = {
  debug: vi.fn(),
  info: vi.fn(),
  log: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
};

// Mock browser environment
vi.mock('$app/environment', () => ({
  browser: true,
  dev: true,
}));

describe('Logger', () => {
  beforeEach(() => {
    // Reset all mocks
    Object.values(mockConsole).forEach((mock) => mock.mockClear());

    // Mock console methods
    global.console = mockConsole as any;
  });

  describe('Logger class', () => {
    it('should create logger with default config', () => {
      const testLogger = new Logger();
      expect(testLogger.getLevel()).toBe(LogLevel.DEBUG);
    });

    it('should create logger with custom config', () => {
      const testLogger = new Logger({ level: LogLevel.WARN });
      expect(testLogger.getLevel()).toBe(LogLevel.WARN);
    });

    it('should log messages at appropriate levels', () => {
      const testLogger = new Logger({ level: LogLevel.INFO });

      // Should log INFO and above
      testLogger.info('Info message');
      testLogger.warn('Warning message');
      testLogger.error('Error message');

      // Should not log DEBUG (below threshold)
      testLogger.debug('Debug message');

      expect(mockConsole.info).toHaveBeenCalledWith('Info message');
      expect(mockConsole.warn).toHaveBeenCalledWith('Warning message');
      expect(mockConsole.error).toHaveBeenCalledWith('Error message');
      expect(mockConsole.debug).not.toHaveBeenCalled();
    });

    it('should include context in log messages', () => {
      const testLogger = new Logger({ level: LogLevel.DEBUG });

      testLogger.info('Test message', { userId: 123, action: 'login' });

      expect(mockConsole.info).toHaveBeenCalledWith('Test message', {
        userId: 123,
        action: 'login',
      });
    });

    it('should create child logger with inherited context', () => {
      const parentLogger = new Logger({
        level: LogLevel.DEBUG,
        context: { service: 'api' },
      });

      const childLogger = parentLogger.child({ module: 'auth' });

      childLogger.info('Child message', { action: 'validate' });

      expect(mockConsole.info).toHaveBeenCalledWith('Child message', {
        service: 'api',
        module: 'auth',
        action: 'validate',
      });
    });

    it('should allow setting log level dynamically', () => {
      const testLogger = new Logger({ level: LogLevel.DEBUG });

      testLogger.debug('Debug message 1');
      expect(mockConsole.debug).toHaveBeenCalledWith('Debug message 1');

      testLogger.setLevel(LogLevel.WARN);
      mockConsole.debug.mockClear();

      testLogger.debug('Debug message 2');
      expect(mockConsole.debug).not.toHaveBeenCalled();

      testLogger.warn('Warning message');
      expect(mockConsole.warn).toHaveBeenCalledWith('Warning message');
    });
  });

  describe('Module loggers', () => {
    it('should create module logger with correct context', () => {
      const moduleLogger = createModuleLogger('test-module');

      moduleLogger.info('Module message');

      expect(mockConsole.info).toHaveBeenCalledWith('Module message', { module: 'test-module' });
    });

    it('should create module logger with additional context', () => {
      const moduleLogger = createModuleLogger('test-module', { version: '1.0.0' });

      moduleLogger.info('Module message');

      expect(mockConsole.info).toHaveBeenCalledWith('Module message', {
        module: 'test-module',
        version: '1.0.0',
      });
    });
  });

  describe('Default logger instance', () => {
    it('should have default logger available', () => {
      expect(logger).toBeDefined();
      expect(logger.getLevel()).toBe(LogLevel.DEBUG);
    });

    it('should log with default logger', () => {
      logger.info('Default logger test');

      expect(mockConsole.info).toHaveBeenCalledWith('Default logger test', { name: 'svelte-demo' });
    });
  });

  describe('Log levels', () => {
    it('should respect log level hierarchy', () => {
      const testLogger = new Logger({ level: LogLevel.WARN });

      // Below threshold - should not log
      testLogger.trace('Trace message');
      testLogger.debug('Debug message');
      testLogger.info('Info message');

      // At or above threshold - should log
      testLogger.warn('Warning message');
      testLogger.error('Error message');
      testLogger.fatal('Fatal message');

      expect(mockConsole.debug).not.toHaveBeenCalled();
      expect(mockConsole.info).not.toHaveBeenCalled();
      expect(mockConsole.warn).toHaveBeenCalledWith('Warning message');
      expect(mockConsole.error).toHaveBeenCalledWith('Error message');
      expect(mockConsole.error).toHaveBeenCalledWith('Fatal message'); // Fatal uses console.error
    });
  });
});
