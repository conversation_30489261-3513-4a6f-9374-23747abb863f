<!-- src/lib/components/ui/error-display/ErrorDisplay.svelte -->
<script lang="ts">
  import CopyIcon from '@lucide/svelte/icons/copy';
  import RefreshCwIcon from '@lucide/svelte/icons/refresh-cw';

  import { Alert, AlertDescription } from '$lib/components/ui/alert';
  import { Button } from '$lib/components/ui/button';
  import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
  } from '$lib/components/ui/card';
  import { Separator } from '$lib/components/ui/separator';
  import { ApiError } from '$lib/services/api/base';

  // Props
  export let error: Error | ApiError | string;
  export let title = '出现错误';
  export let showRetry = true;
  export let showDetails = false;
  export let showCopy = true;
  export let variant: 'card' | 'alert' | 'inline' = 'card';
  export let size: 'sm' | 'md' | 'lg' = 'md';

  // Events - Svelte 5 style
  export let onRetry: () => void = () => {};
  export let onDismiss: () => void = () => {};
  export let onCopy: (text: string) => void = () => {};

  // 处理错误对象
  $: errorObj = typeof error === 'string' ? new Error(error) : error;
  $: isApiError = errorObj instanceof ApiError;

  // 获取用户友好的错误信息
  $: userMessage = getUserFriendlyMessage(errorObj);
  $: technicalMessage = getTechnicalMessage(errorObj);
  $: suggestions = getRecoverySuggestions(errorObj);
  $: errorIcon = getErrorIcon(errorObj);

  function getUserFriendlyMessage(err: Error | ApiError): string {
    if (err instanceof ApiError) {
      return err.getUserFriendlyMessage();
    }

    // 常见错误类型的友好提示
    if (err.name === 'TypeError' && err.message.includes('fetch')) {
      return '网络连接失败，请检查您的网络连接';
    }

    if (err.message.includes('timeout')) {
      return '请求超时，请稍后重试';
    }

    if (err.message.includes('JSON')) {
      return '数据格式错误，请刷新页面重试';
    }

    return '系统遇到了一个问题，请稍后重试';
  }

  function getTechnicalMessage(err: Error | ApiError): string {
    if (err instanceof ApiError && err.statusCode) {
      return `${err.message} (状态码: ${err.statusCode})`;
    }
    return err.message;
  }

  function getRecoverySuggestions(err: Error | ApiError): string[] {
    const suggestions: string[] = [];

    if (err instanceof ApiError) {
      if (err.isNetworkError()) {
        suggestions.push('检查网络连接是否正常');
        suggestions.push('尝试刷新页面');
      } else if (err.statusCode === 401) {
        suggestions.push('请重新登录');
        suggestions.push('检查登录状态是否过期');
      } else if (err.statusCode === 403) {
        suggestions.push('联系管理员获取权限');
        suggestions.push('确认您有执行此操作的权限');
      } else if (err.statusCode === 404) {
        suggestions.push('检查请求的资源是否存在');
        suggestions.push('返回首页重新开始');
      } else if (err.statusCode === 429) {
        suggestions.push('稍等片刻后重试');
        suggestions.push('减少操作频率');
      } else if (err.isServerError()) {
        suggestions.push('稍后重试');
        suggestions.push('联系技术支持');
      }
    } else {
      suggestions.push('刷新页面重试');
      suggestions.push('检查网络连接');
      suggestions.push('清除浏览器缓存');
    }

    return suggestions;
  }

  function getErrorIcon(err: Error | ApiError): string {
    if (err instanceof ApiError) {
      if (err.isNetworkError()) return '🌐';
      if (err.statusCode === 401) return '🔐';
      if (err.statusCode === 403) return '🚫';
      if (err.statusCode === 404) return '🔍';
      if (err.statusCode === 429) return '⏱️';
      if (err.isServerError()) return '⚠️';
    }
    return '❌';
  }

  function handleRetry() {
    onRetry();
  }

  function handleDismiss() {
    onDismiss();
  }

  function handleCopy() {
    const errorText = `
错误信息: ${technicalMessage}
时间: ${new Date().toLocaleString()}
页面: ${window.location.href}
用户代理: ${navigator.userAgent}
    `.trim();

    navigator.clipboard.writeText(errorText).then(() => {
      onCopy(errorText);
    });
  }

  // 样式类
  $: containerClass = {
    card: 'w-full',
    alert: 'w-full',
    inline: 'w-full',
  }[variant];

  $: sizeClass = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
  }[size];
</script>

{#if variant === 'card'}
  <Card class={containerClass}>
    <CardHeader class="text-center">
      <div class="mx-auto mb-2 text-4xl">{errorIcon}</div>
      <CardTitle class={sizeClass}>{title}</CardTitle>
      <CardDescription>{userMessage}</CardDescription>
    </CardHeader>

    <CardContent class="space-y-4">
      <!-- 技术详情 -->
      {#if showDetails}
        <Alert>
          <AlertDescription>
            <strong>技术详情:</strong>
            <pre>{technicalMessage}</pre>
          </AlertDescription>
        </Alert>
      {/if}

      <!-- 恢复建议 -->
      {#if suggestions.length > 0}
        <div class="space-y-2">
          <h4 class="text-sm font-medium">建议解决方案:</h4>
          <ul class="text-muted-foreground list-inside list-disc space-y-1 text-sm">
            {#each suggestions as suggestion}
              <li>{suggestion}</li>
            {/each}
          </ul>
        </div>
      {/if}

      <!-- 操作按钮 -->
      <div class="flex flex-wrap gap-2">
        {#if showRetry}
          <Button onclick={handleRetry} variant="default" size="sm">
            <RefreshCwIcon class="mr-2 h-4 w-4" />
            重试
          </Button>
        {/if}

        {#if showCopy}
          <Button onclick={handleCopy} variant="outline" size="sm">
            <CopyIcon class="mr-2 h-4 w-4" />
            复制错误信息
          </Button>
        {/if}

        <Button onclick={handleDismiss} variant="ghost" size="sm">关闭</Button>
      </div>
    </CardContent>
  </Card>
{:else if variant === 'alert'}
  <Alert class={containerClass}>
    <div class="flex items-start gap-3">
      <div class="text-2xl">{errorIcon}</div>
      <div class="flex-1 space-y-2">
        <AlertDescription>
          <strong>{title}:</strong>
          {userMessage}
        </AlertDescription>

        {#if showDetails}
          <div class="text-muted-foreground text-xs">
            {technicalMessage}
          </div>
        {/if}

        {#if suggestions.length > 0}
          <div class="text-sm">
            <strong>建议:</strong>
            {suggestions[0]}
          </div>
        {/if}

        <div class="flex gap-2">
          {#if showRetry}
            <Button onclick={handleRetry} variant="outline" size="sm">重试</Button>
          {/if}
          <Button onclick={handleDismiss} variant="ghost" size="sm">关闭</Button>
        </div>
      </div>
    </div>
  </Alert>
{:else if variant === 'inline'}
  <div
    class="border-destructive/20 bg-destructive/10 flex items-center gap-2 rounded-md border p-3 text-sm"
  >
    <span class="text-lg">{errorIcon}</span>
    <span class="flex-1">{userMessage}</span>
    {#if showRetry}
      <Button onclick={handleRetry} variant="outline" size="sm">重试</Button>
    {/if}
  </div>
{/if}
