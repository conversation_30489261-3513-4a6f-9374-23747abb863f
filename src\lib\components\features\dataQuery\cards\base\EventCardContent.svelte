<script lang="ts">
  import { CardContent } from '$lib/components/ui/card';
  import type { MarketEvent } from '$lib/types';

  // 本地实现辅助函数
  function getParameterValue(
    event: MarketEvent,
    parameterId: string
  ): string | number | boolean | undefined {
    const parameter = event.parameters.find((p: any) => p.parameterId === parameterId);
    return parameter?.value;
  }

  function formatEventDate(timestamp: number): string {
    return new Date(timestamp * 1000).toLocaleString('zh-CN');
  }
  import type { Snippet } from 'svelte';

  import { type CardSize, type CardVariant,formatTimeDiff, formatUsdAmount } from '../utils';

  interface Props {
    event: MarketEvent;
    size?: CardSize;
    variant?: CardVariant;
    keyFields?: string[];
    customContent?: boolean;
    children?: Snippet;
  }

  const {
    event,
    size = 'md',
    variant = 'default',
    keyFields = [],
    customContent = false,
    children,
  }: Props = $props();

  // 根据尺寸设置样式
  const sizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
  };

  // 根据变体设置间距
  const variantClasses = {
    default: 'space-y-3',
    compact: 'space-y-2',
    detailed: 'space-y-4',
  };

  // 获取通用字段
  const amountUsd = getParameterValue(event, 'amountUsd');
  const exchangeLabel = getParameterValue(event, 'exchangeLabel');
  const exchangeLabels = getParameterValue(event, 'exchangeLabels');
  const base = getParameterValue(event, 'base');
  const pair = getParameterValue(event, 'pair');

  // 显示的交易所信息
  const exchangeInfo = exchangeLabels || exchangeLabel;
</script>

<CardContent class="pt-0 {variantClasses[variant]}">
  {#if !customContent}
    <!-- 基本信息网格 -->
    <div class="grid grid-cols-1 gap-3 {sizeClasses[size]} sm:grid-cols-2">
      <!-- ID信息 -->
      <div class="space-y-1">
        <div class="text-muted-foreground">ID</div>
        <div class="text-foreground truncate font-mono text-xs" title={event.id}>
          {event.id}
        </div>
      </div>

      <!-- 时间信息 -->
      <div class="space-y-1">
        <div class="text-muted-foreground">时间</div>
        <div class="text-foreground font-mono text-xs">
          {formatTimeDiff(event.date)}
        </div>
      </div>
    </div>

    <!-- 关键信息 -->
    <div class="space-y-2">
      <!-- 金额信息 -->
      {#if amountUsd}
        <div class="space-y-1">
          <div class="text-muted-foreground {sizeClasses[size]}">金额 (USD)</div>
          <div class="text-foreground bg-muted rounded px-2 py-1 font-mono {sizeClasses[size]}">
            {formatUsdAmount(amountUsd)}
          </div>
        </div>
      {/if}

      <!-- 交易所信息 -->
      {#if exchangeInfo}
        <div class="space-y-1">
          <div class="text-muted-foreground {sizeClasses[size]}">交易所</div>
          <div class="text-foreground bg-muted rounded px-2 py-1 {sizeClasses[size]}">
            {exchangeInfo}
          </div>
        </div>
      {/if}

      <!-- 货币/交易对信息 -->
      {#if pair || base}
        <div class="space-y-1">
          <div class="text-muted-foreground {sizeClasses[size]}">
            {pair ? '交易对' : '货币'}
          </div>
          <div class="text-foreground bg-muted rounded px-2 py-1 {sizeClasses[size]}">
            {pair || base}
          </div>
        </div>
      {/if}
    </div>

    <!-- 详细时间信息（仅在详细模式下显示） -->
    {#if variant === 'detailed'}
      <div class="space-y-1">
        <div class="text-muted-foreground {sizeClasses[size]}">详细时间</div>
        <div class="text-foreground bg-muted rounded px-2 py-1 font-mono {sizeClasses[size]}">
          {formatEventDate(event.date)}
        </div>
      </div>
    {/if}
  {/if}

  <!-- 自定义内容插槽 -->
  {#if children}
    {@render children()}
  {/if}
</CardContent>
