<script lang="ts">
  // shadcn-svelte 组件导入
  import CalendarIcon from '@lucide/svelte/icons/calendar';
  import ChevronLeftIcon from '@lucide/svelte/icons/chevron-left';
  import ChevronRightIcon from '@lucide/svelte/icons/chevron-right';
  import PlusIcon from '@lucide/svelte/icons/plus';
  import XIcon from '@lucide/svelte/icons/x';
  import {
    DateFormatter,
    type DateValue,
    getLocalTimeZone,
    today
  } from '@internationalized/date';
  import { cn } from '$lib/utils';
  import { Button, buttonVariants } from '$lib/components/ui/button';
  import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
  import { Calendar } from '$lib/components/ui/calendar';
  import { Label } from '$lib/components/ui/label';
  import * as Popover from '$lib/components/ui/popover';
  import * as Select from '$lib/components/ui/select';
  import * as Tabs from '$lib/components/ui/tabs';

  import { dataQueryStore } from '$lib/stores/features/dataQuery';
  import type { MarketEventType, QueryMode } from '$lib/types/business/market';
  import type { CoinOption } from '$lib/types';

  import { CoinMultiSelect, TypeMultiSelect } from '../ui';

  interface Props {
    expanded: boolean;
  }

  const { expanded }: Props = $props();

  // 日期格式化器
  const df = new DateFormatter('zh-CN', {
    dateStyle: 'long'
  });

  // 日期选择状态 - 默认为今天
  let selectedDate: DateValue | undefined = $state(today(getLocalTimeZone()));
  const dateString = $derived(
    selectedDate ? df.format(selectedDate.toDate(getLocalTimeZone())) : ''
  );

  // 快速日期选择选项
  const dateItems = [
    { value: 0, label: '今天' },
    { value: -1, label: '昨天' },
    { value: -7, label: '一周前' },
    { value: -30, label: '一个月前' }
  ];

  // 阈值选择状态
  let selectedThreshold: string | undefined = $state();

  // 阈值选项
  const thresholdItems = [
    { value: '0', label: '阈值 0' },
    { value: '1', label: '阈值 1' },
    { value: '2', label: '阈值 2' },
    { value: '4', label: '阈值 4' }
  ];

  // 响应式变量用于事件类型选择（基础查询模式）
  let selectedType = $state($dataQueryStore.queryParams.basic.marketEventType as MarketEventType[]);

  // 查询模式状态
  let queryMode = $state($dataQueryStore.queryParams.queryMode);

  // 阈值选项（使用类型定义中的常量）
  const thresholdOptions = [
    { value: 0, label: '阈值 0' },
    { value: 1, label: '阈值 1' },
    { value: 2, label: '阈值 2' },
    { value: 4, label: '阈值 4' }
  ];

  // 切换筛选区域展开/收起
  function toggleExpanded() {
    dataQueryStore.toggleFilterExpanded();
  }

  // 切换查询模式
  function handleQueryModeChange(mode: string) {
    const queryModeValue = mode as QueryMode;
    queryMode = queryModeValue;
    dataQueryStore.setQueryMode(queryModeValue);
  }

  // 执行查询
  function submitQuery() {
    // 先验证表单
    const errors = validateForm();
    if (errors.length > 0) {
      showValidationErrors(errors);
      return;
    }

    // 清除之前的错误信息
    validationErrors = [];

    // 执行查询
    dataQueryStore.executeQuery();
  }

  // 重置表单
  function handleReset() {
    dataQueryStore.reset();
    selectedDate = today(getLocalTimeZone()); // 重置为今天
    selectedThreshold = undefined;
    selectedType = [];
  }

  // 前一天导航
  function handlePreviousDay() {
    if (selectedDate) {
      const newDate = selectedDate.subtract({ days: 1 });
      selectedDate = newDate;

      // 立即同步日期到 store
      const dateStr = newDate.toString();
      dataQueryStore.setDate(dateStr);

      // 延迟触发查询，确保 store 已更新
      setTimeout(() => {
        if (queryMode === 'basic') {
          const basicParams = $dataQueryStore.queryParams.basic;
          if (basicParams.base?.length && basicParams.marketEventType?.length) {
            submitQuery();
          }
        } else {
          const advancedParams = $dataQueryStore.queryParams.advanced;
          if (advancedParams.base?.length && advancedParams.filters?.length) {
            submitQuery();
          }
        }
      }, 0);
    }
  }

  // 后一天导航
  function handleNextDay() {
    if (selectedDate) {
      const newDate = selectedDate.add({ days: 1 });
      selectedDate = newDate;

      // 立即同步日期到 store
      const dateStr = newDate.toString();
      dataQueryStore.setDate(dateStr);

      // 延迟触发查询，确保 store 已更新
      setTimeout(() => {
        if (queryMode === 'basic') {
          const basicParams = $dataQueryStore.queryParams.basic;
          if (basicParams.base?.length && basicParams.marketEventType?.length) {
            submitQuery();
          }
        } else {
          const advancedParams = $dataQueryStore.queryParams.advanced;
          if (advancedParams.base?.length && advancedParams.filters?.length) {
            submitQuery();
          }
        }
      }, 0);
    }
  }

  // 处理币种选择变化
  function handleCoinSelectionChange(selectedCoins: string[]) {
    dataQueryStore.setSelectedCoins(selectedCoins);
  }

  // 处理添加币种
  function handleCoinAdd(option: CoinOption) {
    dataQueryStore.addSelectedCoin(option.value);
  }

  // 处理移除币种
  function handleCoinRemove(coinSymbol: string) {
    dataQueryStore.removeSelectedCoin(coinSymbol);
  }

  // 处理移除类型
  function handleTypeRemove(typeValue: string) {
    const newTypes = selectedType.filter((type) => type !== typeValue);
    selectedType = newTypes;
  }

  // 获取类型的首字母缩写
  function getTypeAbbreviation(type: string): string {
    return type
      .split('_')
      .map((word) => word.charAt(0))
      .join('');
  }

  // 计算可显示的 Badge 数量（基于实际容器宽度）
  let containerRef = $state<HTMLDivElement | undefined>(undefined);
  let visibleBadgeCount = $state(0);
  const hiddenCount = $derived(Math.max(0, selectedType.length - visibleBadgeCount));

  // 计算可见的 Badge 数量
  function calculateVisibleBadges() {
    if (!containerRef || selectedType.length === 0) {
      visibleBadgeCount = selectedType.length;
      return;
    }

    const containerWidth = containerRef.offsetWidth;
    const containerPadding = 24; // px-3 = 12px * 2
    const selectTriggerMinWidth = 120; // 选择器最小宽度
    const badgeGap = 4; // gap-1 = 4px
    const hiddenBadgeWidth = 40; // "+n" badge 大概宽度

    const availableWidth = containerWidth - containerPadding - selectTriggerMinWidth;
    let currentWidth = 0;
    let count = 0;

    // 遍历每个 badge，计算能放下多少个
    for (let i = 0; i < selectedType.length; i++) {
      const abbreviation = getTypeAbbreviation(selectedType[i]);
      // 估算 badge 宽度：文字宽度 + padding + X按钮
      const estimatedBadgeWidth = abbreviation.length * 8 + 32 + 16; // 粗略估算

      if (currentWidth + estimatedBadgeWidth + badgeGap <= availableWidth) {
        currentWidth += estimatedBadgeWidth + badgeGap;
        count++;
      } else {
        // 如果还有剩余项，需要为 "+n" badge 预留空间
        if (i < selectedType.length - 1) {
          if (currentWidth + hiddenBadgeWidth <= availableWidth) {
            // 可以放下 "+n" badge，但需要减少一个普通 badge
            count = Math.max(0, count - 1);
          }
        }
        break;
      }
    }

    visibleBadgeCount = count;
  }

  // 监听容器大小变化和选中项变化
  $effect(() => {
    if (containerRef) {
      calculateVisibleBadges();
    }
  });

  // 监听窗口大小变化
  $effect(() => {
    if (typeof window !== 'undefined') {
      const handleResize = () => calculateVisibleBadges();
      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }
  });

  // 监听日期变化并更新 store
  $effect(() => {
    if (selectedDate) {
      const dateStr = selectedDate.toString(); // 格式：yyyy-MM-dd
      dataQueryStore.setDate(dateStr);
    }
  });

  // 监听阈值变化并更新 store
  $effect(() => {
    if (selectedThreshold !== undefined) {
      const threshold = selectedThreshold ? Number(selectedThreshold) : undefined;
      dataQueryStore.setThreshold(threshold);
    }
  });

  // 监听事件类型变化并更新 store（仅基础查询模式）
  $effect(() => {
    if (queryMode === 'basic' && selectedType !== $dataQueryStore.queryParams.basic.marketEventType) {
      dataQueryStore.setMarketEventType(selectedType);
    }
  });

  // 同步 store 变化到本地状态
  $effect(() => {
    if (queryMode === 'basic') {
      selectedType = $dataQueryStore.queryParams.basic.marketEventType;
    }
  });

  // 监听查询模式变化
  $effect(() => {
    queryMode = $dataQueryStore.queryParams.queryMode;
  });

  // 派生状态：当前模式下的币种列表
  const currentSelectedCoins = $derived(
    queryMode === 'basic'
      ? $dataQueryStore.queryParams.basic.base
      : $dataQueryStore.queryParams.advanced.base
  );



  // 验证状态
  let validationErrors = $state<string[]>([]);

  // 派生状态：查询按钮是否禁用
  const isQueryDisabled = $derived(() => {
    if (queryMode === 'basic') {
      const basicParams = $dataQueryStore.queryParams.basic;
      return !basicParams.base?.length || !basicParams.date || !basicParams.marketEventType?.length;
    } else {
      const advancedParams = $dataQueryStore.queryParams.advanced;
      return !advancedParams.base?.length || !advancedParams.date || !advancedParams.filters?.length;
    }
  });

  // 检测重复事件类型
  function detectDuplicateEventTypes(): string[] {
    const errors: string[] = [];
    const eventTypeCounts = new Map<MarketEventType, number>();
    
    // 统计每个事件类型的出现次数
    $dataQueryStore.queryParams.advanced.filters.forEach((filter, index) => {
      if (filter.marketEventType) {
        const count = eventTypeCounts.get(filter.marketEventType) || 0;
        eventTypeCounts.set(filter.marketEventType, count + 1);
        
        // 如果发现重复，记录错误
        if (count > 0) {
          const eventTypeLabel = getEventTypeLabel(filter.marketEventType);
          errors.push(`事件类型"${eventTypeLabel}"重复出现，请删除重复的过滤条件`);
        }
      }
    });
    
    return errors;
  }

  // 获取事件类型的中文标签
  function getEventTypeLabel(eventType: MarketEventType): string {
    const labels: Record<MarketEventType, string> = {
      'TWAP': 'TWAP检测',
      'LIQUIDATION_ORDER': '清算订单',
      'EXCHANGE_TRANSFER': '交易所转账',
      'ORDERBOOK_IMBALANCE': '订单簿失衡',
      'FUNDING_RATE_SWITCH': '资金费率切换',
      'EXTREME_FUNDING_RATE': '极端资金费率',
      'VOLUME_INCREASE': '交易量增长',
      'OPEN_INTEREST_VARIATION': '持仓量变化'
    };
    return labels[eventType] || eventType;
  }

  // 验证表单
  function validateForm(): string[] {
    const errors: string[] = [];

    // 基础验证
    if (currentSelectedCoins.length === 0) {
      errors.push('请至少选择一个币种');
    }

    if (!selectedDate) {
      errors.push('请选择查询日期');
    }

    // 根据查询模式进行不同的验证
    if (queryMode === 'basic') {
      if (selectedType.length === 0) {
        errors.push('请至少选择一个事件类型');
      }
    } else if (queryMode === 'advanced') {
      const advancedParams = $dataQueryStore.queryParams.advanced;
      if (!advancedParams.filters?.length) {
        errors.push('请添加至少一个过滤条件');
      } else {
        // 检测重复事件类型
        const duplicateErrors = detectDuplicateEventTypes();
        errors.push(...duplicateErrors);
        
        // 验证每个过滤条件
        advancedParams.filters.forEach((filter, index) => {
          if (!filter.marketEventType) {
            errors.push(`过滤条件 ${index + 1}: 请选择事件类型`);
          }
          if (filter.threshold === undefined || filter.threshold === null) {
            errors.push(`过滤条件 ${index + 1}: 请选择阈值`);
          }
        });
      }
    }

    return errors;
  }

  // 显示验证错误
  function showValidationErrors(errors: string[]) {
    validationErrors = errors;
    // 3秒后自动清除错误信息
    setTimeout(() => {
      validationErrors = [];
    }, 3000);
  }

  // 添加过滤条件（带验证）
  function handleAddFilter() {
    const existingTypes = $dataQueryStore.queryParams.advanced.filters.map(f => f.marketEventType);
    const allTypes: MarketEventType[] = [
      'TWAP', 'LIQUIDATION_ORDER', 'EXCHANGE_TRANSFER',
      'ORDERBOOK_IMBALANCE', 'FUNDING_RATE_SWITCH', 'EXTREME_FUNDING_RATE',
      'VOLUME_INCREASE', 'OPEN_INTEREST_VARIATION'
    ];

    // 找到第一个未使用的事件类型
    const availableType = allTypes.find(type => !existingTypes.includes(type));
    
    if (!availableType) {
      // 如果所有类型都已使用，显示错误提示
      showValidationErrors(['所有事件类型都已添加，无法添加更多过滤条件']);
      return;
    }

    dataQueryStore.addAdvancedFilter({ 
      marketEventType: availableType, 
      threshold: 0 
    });
  }

  // 处理重置表单（增强版）
  function handleResetEnhanced() {
    // 清除验证错误
    validationErrors = [];

    // 调用原有的重置逻辑
    handleReset();
  }
</script>

<!-- 可折叠的事件查询筛选区域 -->
<Card class="mb-6">
  <CardHeader class="pb-4">
    <div class="flex items-center justify-between">
      <div>
        <CardTitle class="text-xl font-semibold">市场事件查询</CardTitle>
        <p class="text-muted-foreground text-sm mt-1">查询指定日期的加密货币市场事件数据</p>
      </div>
      <Button variant="ghost" size="sm" onclick={toggleExpanded}>
        {expanded ? '收起筛选' : '展开筛选'}
      </Button>
    </div>

    <!-- 查询模式切换 -->
    {#if expanded}
      <div class="mt-4">
        <Tabs.Root value={queryMode} onValueChange={handleQueryModeChange}>
          <Tabs.List class="grid w-full grid-cols-2">
            <Tabs.Trigger value="basic">基础查询</Tabs.Trigger>
            <Tabs.Trigger value="advanced">高级查询</Tabs.Trigger>
          </Tabs.List>
        </Tabs.Root>
      </div>
    {/if}
  </CardHeader>
  {#if expanded}
    <CardContent class="pt-0">
      <Tabs.Root value={queryMode} onValueChange={handleQueryModeChange}>
        <!-- 基础查询内容 -->
        <Tabs.Content value="basic" class="space-y-6">
          <!-- 第一行：必需参数 -->
          <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
          <!-- 币种多选框 -->
          <div class="space-y-2">
            <Label for="coins" class="text-sm font-medium">
              币种选择 <span class="text-destructive">*</span>
            </Label>
            <CoinMultiSelect
              selectedCoins={currentSelectedCoins}
              placeholder="请选择币种（必需）"
              maxSelections={10}
              onSelectionChange={handleCoinSelectionChange}
              onCoinAdd={handleCoinAdd}
              onCoinRemove={handleCoinRemove}
            />
            <p class="text-muted-foreground text-xs">支持多选，最多10个币种</p>
          </div>

          <!-- 日期选择器 -->
          <div class="space-y-2">
            <Label for="date" class="text-sm font-medium">
              查询日期 <span class="text-destructive">*</span>
            </Label>
            <Popover.Root>
              <Popover.Trigger
                class={cn(
                  buttonVariants({
                    variant: 'outline',
                    class: 'w-full justify-start text-left font-normal'
                  }),
                  !selectedDate && 'text-muted-foreground'
                )}
              >
                <CalendarIcon class="mr-2 size-4" />
                {selectedDate ? df.format(selectedDate.toDate(getLocalTimeZone())) : '选择查询日期'}
              </Popover.Trigger>
              <Popover.Content class="flex w-auto flex-col space-y-2 p-2">
                <Select.Root
                  type="single"
                  bind:value={
                    () => dateString,
                    (v) => {
                      if (!v) return;
                      selectedDate = today(getLocalTimeZone()).add({ days: Number.parseInt(v) });
                    }
                  }
                >
                  <Select.Trigger>
                    {dateString || '快速选择'}
                  </Select.Trigger>
                  <Select.Content>
                    {#each dateItems as item (item.value)}
                      <Select.Item value={`${item.value}`}>{item.label}</Select.Item>
                    {/each}
                  </Select.Content>
                </Select.Root>
                <div class="rounded-md border">
                  <Calendar type="single" bind:value={selectedDate} />
                </div>
              </Popover.Content>
            </Popover.Root>
            <p class="text-muted-foreground text-xs">选择要查询的具体日期</p>
          </div>

          <!-- 事件类型筛选 -->
          <div class="space-y-2">
            <Label for="type" class="text-sm font-medium">
              事件类型 <span class="text-destructive">*</span>
            </Label>
            <div bind:this={containerRef}>
              <TypeMultiSelect
                selectedTypes={selectedType}
                placeholder="请选择事件类型（必需）"
                {visibleBadgeCount}
                {hiddenCount}
                onSelectionChange={(types) => {
                  selectedType = types as MarketEventType[];
                  dataQueryStore.setMarketEventType(types as MarketEventType[]);
                }}
                onTypeRemove={handleTypeRemove}
              />
            </div>
            <p class="text-muted-foreground text-xs">支持多选，至少选择一个类型</p>
          </div>
        </div>

        <!-- 第二行：可选参数 -->
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
          <!-- 阈值选择 -->
          <div class="space-y-2">
            <Label for="threshold" class="text-sm font-medium">阈值设置</Label>
            <Select.Root type="single" bind:value={selectedThreshold}>
              <Select.Trigger id="threshold" class="w-full">
                {selectedThreshold ? thresholdItems.find(item => item.value === selectedThreshold)?.label : '选择阈值（可选）'}
              </Select.Trigger>
              <Select.Content>
                <Select.Item value="">不设置阈值</Select.Item>
                {#each thresholdItems as item (item.value)}
                  <Select.Item value={item.value}>{item.label}</Select.Item>
                {/each}
              </Select.Content>
            </Select.Root>
            <p class="text-muted-foreground text-xs">可选参数，用于过滤事件数据</p>
          </div>

          <!-- 占位空间，保持布局平衡 -->
          <div></div>
        </div>
        </Tabs.Content>

        <!-- 高级查询内容 -->
        <Tabs.Content value="advanced" class="space-y-6">
          <!-- 共同参数：币种和日期 -->
          <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
            <!-- 币种多选框 -->
            <div class="space-y-2">
              <Label for="coins-advanced" class="text-sm font-medium">
                币种选择 <span class="text-destructive">*</span>
              </Label>
              <CoinMultiSelect
                selectedCoins={currentSelectedCoins}
                placeholder="请选择币种（必需）"
                maxSelections={10}
                onSelectionChange={handleCoinSelectionChange}
                onCoinAdd={handleCoinAdd}
                onCoinRemove={handleCoinRemove}
              />
              <p class="text-muted-foreground text-xs">支持多选，最多10个币种</p>
            </div>

            <!-- 日期选择器 -->
            <div class="space-y-2">
              <Label for="date-advanced" class="text-sm font-medium">
                查询日期 <span class="text-destructive">*</span>
              </Label>
              <Popover.Root>
                <Popover.Trigger
                  class={cn(
                    buttonVariants({
                      variant: 'outline',
                      class: 'w-full justify-start text-left font-normal'
                    }),
                    !selectedDate && 'text-muted-foreground'
                  )}
                >
                  <CalendarIcon class="mr-2 size-4" />
                  {selectedDate ? df.format(selectedDate.toDate(getLocalTimeZone())) : '选择查询日期'}
                </Popover.Trigger>
                <Popover.Content class="flex w-auto flex-col space-y-2 p-2">
                  <Select.Root
                    type="single"
                    bind:value={
                      () => dateString,
                      (v) => {
                        if (!v) return;
                        selectedDate = today(getLocalTimeZone()).add({ days: Number.parseInt(v) });
                      }
                    }
                  >
                    <Select.Trigger>
                      {dateString || '快速选择'}
                    </Select.Trigger>
                    <Select.Content>
                      {#each dateItems as item (item.value)}
                        <Select.Item value={`${item.value}`}>{item.label}</Select.Item>
                      {/each}
                    </Select.Content>
                  </Select.Root>
                  <div class="rounded-md border">
                    <Calendar type="single" bind:value={selectedDate} />
                  </div>
                </Popover.Content>
              </Popover.Root>
              <p class="text-muted-foreground text-xs">选择要查询的具体日期</p>
            </div>
          </div>

          <!-- 过滤条件管理 -->
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <Label class="text-sm font-medium">
                过滤条件 <span class="text-destructive">*</span>
              </Label>
              <Button
                variant="outline"
                size="sm"
                onclick={handleAddFilter}
              >
                <PlusIcon class="mr-2 size-4" />
                添加条件
              </Button>
            </div>

            <!-- 过滤条件列表 -->
            <div class="flex flex-wrap gap-3">
              {#each $dataQueryStore.queryParams.advanced.filters as filter (filter.id)}
                <div class="relative group p-3 border rounded-lg transition-all duration-200 hover:border-muted-foreground hover:shadow-sm">
                  <!-- 删除按钮 - 仅在hover时显示 -->
                  <button
                    class="absolute -top-2 -right-2 w-6 h-6 bg-destructive/80 hover:bg-destructive text-destructive-foreground rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10"
                    onclick={() => dataQueryStore.removeAdvancedFilter(filter.id)}
                    title="删除此过滤条件"
                  >
                    <XIcon class="size-3" />
                  </button>

                  <div class="flex items-center gap-3">
                    <!-- 事件类型选择 -->
                    <div class="flex-1">
                      <Select.Root
                        type="single"
                        value={filter.marketEventType}
                        onValueChange={(value) => {
                          if (value) {
                            dataQueryStore.updateAdvancedFilter(filter.id, { marketEventType: value as MarketEventType });
                          }
                        }}
                      >
                        <Select.Trigger class="w-full">
                          {filter.marketEventType ?
                            (filter.marketEventType === 'TWAP' ? 'TWAP检测' :
                             filter.marketEventType === 'LIQUIDATION_ORDER' ? '清算订单' :
                             filter.marketEventType === 'EXCHANGE_TRANSFER' ? '交易所转账' :
                             filter.marketEventType === 'ORDERBOOK_IMBALANCE' ? '订单簿失衡' :
                             filter.marketEventType === 'FUNDING_RATE_SWITCH' ? '资金费率切换' :
                             filter.marketEventType === 'EXTREME_FUNDING_RATE' ? '极端资金费率' :
                             filter.marketEventType === 'VOLUME_INCREASE' ? '交易量增长' :
                             filter.marketEventType === 'OPEN_INTEREST_VARIATION' ? '持仓量变化' :
                             filter.marketEventType) : '选择事件类型'}
                        </Select.Trigger>
                        <Select.Content>
                          <Select.Item value="TWAP">TWAP检测</Select.Item>
                          <Select.Item value="LIQUIDATION_ORDER">清算订单</Select.Item>
                          <Select.Item value="EXCHANGE_TRANSFER">交易所转账</Select.Item>
                          <Select.Item value="ORDERBOOK_IMBALANCE">订单簿失衡</Select.Item>
                          <Select.Item value="FUNDING_RATE_SWITCH">资金费率切换</Select.Item>
                          <Select.Item value="EXTREME_FUNDING_RATE">极端资金费率</Select.Item>
                          <Select.Item value="VOLUME_INCREASE">交易量增长</Select.Item>
                          <Select.Item value="OPEN_INTEREST_VARIATION">持仓量变化</Select.Item>
                        </Select.Content>
                      </Select.Root>
                    </div>

                    <!-- 阈值选择 -->
                    <div class="w-32">
                      <Select.Root
                        type="single"
                        value={filter.threshold.toString()}
                        onValueChange={(value) => {
                          if (value) {
                            dataQueryStore.updateAdvancedFilter(filter.id, { threshold: Number(value) });
                          }
                        }}
                      >
                        <Select.Trigger class="w-full">
                          阈值 {filter.threshold}
                        </Select.Trigger>
                        <Select.Content>
                          {#each thresholdOptions as option (option.value)}
                            <Select.Item value={option.value.toString()}>{option.label}</Select.Item>
                          {/each}
                        </Select.Content>
                      </Select.Root>
                    </div>
                  </div>
                </div>
              {/each}

              {#if $dataQueryStore.queryParams.advanced.filters.length === 0}
                <div class="text-center py-8 text-muted-foreground w-full">
                  <p>暂无过滤条件</p>
                  <p class="text-xs mt-1">点击"添加条件"按钮开始配置</p>
                </div>
              {/if}
            </div>
          </div>
        </Tabs.Content>

        <!-- 验证错误显示 -->
        {#if validationErrors.length > 0}
          <div class="bg-destructive/10 border border-destructive/20 rounded-lg p-4">
            <div class="flex items-start gap-3">
              <div class="text-destructive">
                <svg class="size-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div class="flex-1">
                <h4 class="text-sm font-medium text-destructive mb-2">表单验证失败</h4>
                <ul class="text-sm text-destructive/80 space-y-1">
                  {#each validationErrors as error}
                    <li>• {error}</li>
                  {/each}
                </ul>
              </div>
            </div>
          </div>
        {/if}

        <!-- 操作按钮区域 -->
        <div class="border-border flex flex-col gap-4 border-t pt-6 sm:flex-row sm:items-center sm:justify-between">
          <div class="text-muted-foreground text-sm">
            <span class="text-destructive">*</span> 标记为必填项
          </div>
          <div class="flex flex-wrap gap-3">
            <!-- 日期导航按钮 -->
            <div class="flex gap-1">
              <Button
                variant="outline"
                size="sm"
                onclick={handlePreviousDay}
                class="px-2"
                title="前一天"
              >
                <ChevronLeftIcon class="size-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onclick={handleNextDay}
                class="px-2"
                title="后一天"
              >
                <ChevronRightIcon class="size-4" />
              </Button>
            </div>

            <Button variant="outline" onclick={handleResetEnhanced} size="sm" class="min-w-[80px]">
              重置表单
            </Button>
            <Button
              onclick={submitQuery}
              size="sm"
              class="min-w-[80px]"
              disabled={isQueryDisabled() || $dataQueryStore.results.loading}
            >
              {#if $dataQueryStore.results.loading}
                <svg class="mr-2 size-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                查询中...
              {:else}
                开始查询
              {/if}
            </Button>
          </div>
        </div>
      </Tabs.Root>
    </CardContent>
  {/if}
</Card>


