/**
 * 类型定义统一导出
 *
 * 重构后的类型组织结构：
 * - core: 核心共享类型（API、分页、通用类型）
 * - business: 业务领域类型（清算、市场、查询）
 * - ui: UI 相关类型（图表、表单、布局）
 *
 * @category Types
 */

// 核心类型
export * from './core';

// 业务类型
export * from './business';

// UI 类型
export * from './ui';

// 向后兼容：重新导出常用类型
export type {
  // 查询相关
  BaseQueryParams,
  CoinInfo,
  CompoundQueryParams,
  HistoricalDataItem,
  // 清算相关
  LiquidationData,
  LiquidationRankItem,
  LiquidationRankResponse,
  LiquidationStats,
  // 市场相关
  MarketEvent,
  MarketEventType,
  QueryResults,
} from './business';
export type {
  AmountRange,
  ApiErrorInfo,
  // API 相关
  ApiResponse,
  CoinOption,
  DataTypeOption,
  DateRange,
  PaginatedResponse,
  PaginationParams,
  // 通用类型
  TimeRange,
} from './core';
export type {
  BarChartItem,
  ChartConfig,
  // 图表相关
  ChartData,
  // 布局相关
  DashboardStats,
  LineChartItem,
  NavigationItem,
  NotificationItem,
  PieChartItem,
} from './ui';
