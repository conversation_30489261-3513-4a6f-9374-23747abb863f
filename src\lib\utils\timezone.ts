import { get } from 'svelte/store';

import { browser } from '$app/environment';
import { currentTimezone } from '$lib/stores/features/timezone';

import { createModuleLogger } from './logger';

const timezoneLogger = createModuleLogger('timezone');

/**
 * 时区转换和格式化工具函数
 */

/**
 * 获取当前选中的时区
 */
export function getCurrentTimezone(): string {
  if (!browser) return 'UTC';
  return get(currentTimezone) || 'UTC';
}

/**
 * 将日期转换到指定时区
 * @param date 原始日期
 * @param timezone 目标时区，如果不提供则使用当前选中的时区
 * @returns 转换后的日期对象
 */
export function convertToTimezone(date: Date | string, timezone?: string): Date {
  const targetTimezone = timezone || getCurrentTimezone();
  const sourceDate = typeof date === 'string' ? new Date(date) : date;

  if (!browser || targetTimezone === 'browser') {
    return sourceDate;
  }

  try {
    // 使用 Intl.DateTimeFormat 获取时区偏移
    const formatter = new Intl.DateTimeFormat('en', {
      timeZone: targetTimezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    });

    const parts = formatter.formatToParts(sourceDate);
    const partsObj = parts.reduce(
      (acc, part) => {
        acc[part.type] = part.value;
        return acc;
      },
      {} as Record<string, string>
    );

    // 构建目标时区的日期
    const targetDate = new Date(
      parseInt(partsObj.year),
      parseInt(partsObj.month) - 1,
      parseInt(partsObj.day),
      parseInt(partsObj.hour),
      parseInt(partsObj.minute),
      parseInt(partsObj.second)
    );

    return targetDate;
  } catch (error) {
    timezoneLogger.warn('Failed to convert timezone', {
      error: error instanceof Error ? error.message : String(error),
      sourceDate: sourceDate.toISOString(),
      targetTimezone,
      stack: error instanceof Error ? error.stack : undefined,
    });
    return sourceDate;
  }
}

/**
 * 格式化日期时间到指定时区
 * @param date 日期对象或字符串
 * @param options 格式化选项
 * @param timezone 目标时区，如果不提供则使用当前选中的时区
 * @returns 格式化后的字符串
 */
export function formatDateInTimezone(
  date: Date | string,
  options: Intl.DateTimeFormatOptions = {},
  timezone?: string
): string {
  const targetTimezone = timezone || getCurrentTimezone();
  const sourceDate = typeof date === 'string' ? new Date(date) : date;

  if (isNaN(sourceDate.getTime())) {
    return '';
  }

  try {
    const defaultOptions: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
      timeZone: targetTimezone === 'browser' ? undefined : targetTimezone,
      ...options,
    };

    if (browser) {
      return new Intl.DateTimeFormat('zh-CN', defaultOptions).format(sourceDate);
    } else {
      // 在非浏览器环境中（如测试），返回简单格式
      return sourceDate.toLocaleString('zh-CN', defaultOptions);
    }
  } catch (error) {
    timezoneLogger.warn('Failed to format date in timezone', {
      error: error instanceof Error ? error.message : String(error),
      sourceDate: sourceDate.toISOString(),
      targetTimezone,
      options,
      stack: error instanceof Error ? error.stack : undefined,
    });
    return sourceDate.toLocaleString('zh-CN');
  }
}

/**
 * 获取时区的偏移信息
 * @param timezone 时区标识符
 * @param date 参考日期，默认为当前时间
 * @returns 时区偏移信息
 */
export function getTimezoneOffset(
  timezone: string,
  date: Date = new Date()
): {
  offset: string;
  offsetMinutes: number;
  isDST: boolean;
} {
  if (!browser) {
    return {
      offset: 'UTC+00:00',
      offsetMinutes: 0,
      isDST: false,
    };
  }

  try {
    // 获取 UTC 时间和目标时区时间
    const utcTime = date.getTime();
    const utcDate = new Date(utcTime);

    // 使用 Intl.DateTimeFormat 获取目标时区的时间
    const formatter = new Intl.DateTimeFormat('en', {
      timeZone: timezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    });

    const parts = formatter.formatToParts(date);
    const partsObj = parts.reduce(
      (acc, part) => {
        acc[part.type] = part.value;
        return acc;
      },
      {} as Record<string, string>
    );

    const targetDate = new Date(
      parseInt(partsObj.year),
      parseInt(partsObj.month) - 1,
      parseInt(partsObj.day),
      parseInt(partsObj.hour),
      parseInt(partsObj.minute),
      parseInt(partsObj.second)
    );

    // 计算偏移分钟数
    const offsetMinutes = (targetDate.getTime() - utcTime) / (1000 * 60);

    // 格式化偏移字符串
    const hours = Math.floor(Math.abs(offsetMinutes) / 60);
    const minutes = Math.abs(offsetMinutes) % 60;
    const sign = offsetMinutes >= 0 ? '+' : '-';
    const offset = `UTC${sign}${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;

    // 简单的夏令时检测（比较1月和7月的偏移）
    const januaryOffset = getTimezoneOffset(timezone, new Date(date.getFullYear(), 0, 1));
    const julyOffset = getTimezoneOffset(timezone, new Date(date.getFullYear(), 6, 1));
    const isDST =
      offsetMinutes !== januaryOffset.offsetMinutes && offsetMinutes !== julyOffset.offsetMinutes;

    return {
      offset,
      offsetMinutes,
      isDST,
    };
  } catch (error) {
    timezoneLogger.warn('Failed to get timezone offset', {
      error: error instanceof Error ? error.message : String(error),
      timezone,
      date: date.toISOString(),
      stack: error instanceof Error ? error.stack : undefined,
    });
    return {
      offset: 'UTC+00:00',
      offsetMinutes: 0,
      isDST: false,
    };
  }
}

/**
 * 获取时区的友好显示名称
 * @param timezone 时区标识符
 * @param locale 本地化语言，默认为中文
 * @returns 友好的时区名称
 */
export function getTimezoneName(timezone: string, locale: string = 'zh-CN'): string {
  if (!browser) return timezone;

  try {
    const formatter = new Intl.DateTimeFormat(locale, {
      timeZone: timezone,
      timeZoneName: 'long',
    });

    const parts = formatter.formatToParts(new Date());
    const timeZonePart = parts.find((part) => part.type === 'timeZoneName');

    return timeZonePart?.value || timezone;
  } catch (error) {
    timezoneLogger.warn('Failed to get timezone name', {
      error: error instanceof Error ? error.message : String(error),
      timezone,
      locale,
      stack: error instanceof Error ? error.stack : undefined,
    });
    return timezone;
  }
}

/**
 * 检查时区是否有效
 * @param timezone 时区标识符
 * @returns 是否为有效时区
 */
export function isValidTimezone(timezone: string): boolean {
  if (!browser) {
    // 在测试环境中，检查是否为已知的有效时区
    const validTimezones = [
      'UTC',
      'Asia/Shanghai',
      'America/New_York',
      'Europe/London',
      'Asia/Tokyo',
      'America/Los_Angeles',
      'Europe/Paris',
    ];
    return validTimezones.includes(timezone);
  }

  try {
    new Intl.DateTimeFormat('en', { timeZone: timezone });
    return true;
  } catch {
    return false;
  }
}

/**
 * 获取浏览器的本地时区
 * @returns 浏览器本地时区标识符
 */
export function getBrowserTimezone(): string {
  if (!browser) return 'UTC';

  try {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
  } catch {
    return 'UTC';
  }
}

/**
 * 将时间戳转换为指定时区的可读格式
 * @param timestamp 时间戳（毫秒）
 * @param format 格式类型
 * @param timezone 目标时区
 * @returns 格式化后的时间字符串
 */
export function formatTimestampInTimezone(
  timestamp: number,
  format: 'date' | 'time' | 'datetime' | 'relative' = 'datetime',
  timezone?: string
): string {
  const date = new Date(timestamp);
  const targetTimezone = timezone || getCurrentTimezone();

  if (format === 'relative') {
    return formatRelativeTime(timestamp);
  }

  const options: Intl.DateTimeFormatOptions = {};

  switch (format) {
    case 'date':
      options.year = 'numeric';
      options.month = '2-digit';
      options.day = '2-digit';
      break;
    case 'time':
      options.hour = '2-digit';
      options.minute = '2-digit';
      options.second = '2-digit';
      options.hour12 = false;
      break;
    case 'datetime':
    default:
      options.year = 'numeric';
      options.month = '2-digit';
      options.day = '2-digit';
      options.hour = '2-digit';
      options.minute = '2-digit';
      options.second = '2-digit';
      options.hour12 = false;
      break;
  }

  return formatDateInTimezone(date, options, targetTimezone);
}

/**
 * 格式化相对时间
 * @param timestamp 时间戳
 * @returns 相对时间字符串（如：2分钟前）
 */
export function formatRelativeTime(timestamp: number): string {
  const now = Date.now();
  const diff = now - timestamp;
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) {
    return `${days}天前`;
  } else if (hours > 0) {
    return `${hours}小时前`;
  } else if (minutes > 0) {
    return `${minutes}分钟前`;
  } else if (seconds > 0) {
    return `${seconds}秒前`;
  } else {
    return '刚刚';
  }
}

/**
 * 创建时区感知的日期范围
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @param timezone 时区
 * @returns 转换后的日期范围
 */
export function createTimezoneAwareDateRange(
  startDate: Date | string,
  endDate: Date | string,
  timezone?: string
): { start: Date; end: Date } {
  const targetTimezone = timezone || getCurrentTimezone();

  return {
    start: convertToTimezone(startDate, targetTimezone),
    end: convertToTimezone(endDate, targetTimezone),
  };
}
