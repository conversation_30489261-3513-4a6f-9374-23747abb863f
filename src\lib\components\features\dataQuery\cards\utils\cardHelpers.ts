import type { MarketEvent, MarketEventType } from '$lib/types';

// 本地实现 getParameterValue 函数
function getParameterValue(
  event: MarketEvent,
  parameterId: string
): string | number | boolean | undefined {
  const parameter = event.parameters.find((p: any) => p.parameterId === parameterId);
  return parameter?.value;
}

// 卡片尺寸类型
export type CardSize = 'sm' | 'md' | 'lg';

// 卡片变体类型
export type CardVariant = 'default' | 'compact' | 'detailed';

// Badge 变体类型
export type BadgeVariant = 'default' | 'secondary' | 'destructive' | 'outline';

// 基础卡片属性接口
export interface BaseEventCardProps {
  event: MarketEvent;
  isHighlighted?: boolean;
  size?: CardSize;
  variant?: CardVariant;
  showActions?: boolean;
  onCardClick?: (event: MarketEvent) => void;
}

// 卡片配置接口
export interface EventCardConfig {
  title: string;
  badgeVariant: BadgeVariant;
  colorClass: string;
  keyFields: string[];
  formatters: Record<string, (value: any) => string>;
}

// 获取事件的显示名称
export function getEventDisplayName(event: MarketEvent): string {
  const base = getParameterValue(event, 'base') as string;
  const pair = getParameterValue(event, 'pair') as string;
  const exchangeLabel = getParameterValue(event, 'exchangeLabel') as string;
  const exchangeLabels = getParameterValue(event, 'exchangeLabels') as string;

  if (base) {
    return base;
  } else if (pair) {
    return pair;
  } else if (exchangeLabels) {
    return `聚合数据 (${exchangeLabels})`;
  } else if (exchangeLabel) {
    return exchangeLabel;
  }

  return '未知事件';
}

// 获取事件类型对应的 Badge 变体
export function getEventTypeBadgeVariant(eventType: MarketEventType): BadgeVariant {
  const variantMap: Record<MarketEventType, BadgeVariant> = {
    LIQUIDATION_ORDER: 'destructive',
    TWAP: 'default',
    VOLUME_INCREASE: 'secondary',
    EXCHANGE_TRANSFER: 'outline',
    ORDERBOOK_IMBALANCE: 'secondary',
    FUNDING_RATE_SWITCH: 'default',
    EXTREME_FUNDING_RATE: 'destructive',
    OPEN_INTEREST_VARIATION: 'outline',
  };
  return variantMap[eventType] || 'outline';
}

// 获取事件类型对应的颜色类
export function getEventTypeColorClass(eventType: MarketEventType): string {
  const colorMap: Record<MarketEventType, string> = {
    LIQUIDATION_ORDER: 'text-red-600 dark:text-red-400',
    TWAP: 'text-blue-600 dark:text-blue-400',
    VOLUME_INCREASE: 'text-green-600 dark:text-green-400',
    EXCHANGE_TRANSFER: 'text-orange-600 dark:text-orange-400',
    ORDERBOOK_IMBALANCE: 'text-purple-600 dark:text-purple-400',
    FUNDING_RATE_SWITCH: 'text-indigo-600 dark:text-indigo-400',
    EXTREME_FUNDING_RATE: 'text-red-700 dark:text-red-300',
    OPEN_INTEREST_VARIATION: 'text-cyan-600 dark:text-cyan-400',
  };
  return colorMap[eventType] || 'text-gray-600 dark:text-gray-400';
}

// 获取卡片尺寸对应的样式类
export function getCardSizeClasses(size: CardSize): string {
  const sizeMap: Record<CardSize, string> = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
  };
  return sizeMap[size] || sizeMap.md;
}

// 检查事件是否为聚合数据
export function isAggregatedEvent(event: MarketEvent): boolean {
  return getParameterValue(event, 'aggregated') === true;
}

// 获取方向显示文本
export function getDirectionText(
  value: number | string | undefined,
  type: 'side' | 'direction' | 'priceDirection'
): string {
  if (value === undefined || value === null) return '未知';

  const numValue = typeof value === 'string' ? parseInt(value) : value;

  switch (type) {
    case 'side':
      return numValue === 1 ? '买入/多头' : numValue === 2 ? '卖出/空头' : '未知';
    case 'direction':
      return numValue === 1 ? '增加' : numValue === 2 ? '减少' : '未知';
    case 'priceDirection':
      return numValue === 1 ? '上涨' : numValue === 2 ? '下跌' : '未知';
    default:
      return '未知';
  }
}

// 获取市场类型显示文本
export function getMarketTypeText(value: number | string | undefined): string {
  if (value === undefined || value === null) return '未知';

  const numValue = typeof value === 'string' ? parseInt(value) : value;
  return numValue === 1 ? '现货' : numValue === 2 ? '永续合约' : '期货';
}

// 获取状态显示文本和颜色
export function getStatusDisplay(status: string | undefined): { text: string; colorClass: string } {
  if (!status) return { text: '未知', colorClass: 'text-gray-500' };

  const statusMap: Record<string, { text: string; colorClass: string }> = {
    ongoing: { text: '进行中', colorClass: 'text-blue-600 dark:text-blue-400' },
    started: { text: '已开始', colorClass: 'text-green-600 dark:text-green-400' },
    expired: { text: '已过期', colorClass: 'text-red-600 dark:text-red-400' },
    completed: { text: '已完成', colorClass: 'text-gray-600 dark:text-gray-400' },
  };

  return statusMap[status.toLowerCase()] || { text: status, colorClass: 'text-gray-500' };
}
