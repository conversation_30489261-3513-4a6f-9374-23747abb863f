<script lang="ts">
  import { cn } from '$lib/utils';

  export let value: number = 0;
  export let max: number = 100;

  let className: string = '';
  export { className as class };

  $: percentage = Math.min(Math.max((value / max) * 100, 0), 100);
</script>

<div
  class={cn('bg-primary/20 relative h-2 w-full overflow-hidden rounded-full', className)}
  role="progressbar"
  aria-valuenow={value}
  aria-valuemin={0}
  aria-valuemax={max}
>
  <div
    class="bg-primary h-full w-full flex-1 transition-all"
    style="transform: translateX(-{100 - percentage}%)"
  ></div>
</div>
