<script lang="ts">
  import {
    BaseEventCard,
    ExchangeTransferCard,
    ExtremeFundingRateCard,
    FundingRateSwitchCard,
    LiquidationOrderCard,
    OpenInterestVariationCard,
    OrderbookImbalanceCard,
    TwapDetectionCard,
    VolumeIncreaseCard,
  } from '$lib/components/features/dataQuery/cards';
  import { ResizableHandle,ResizablePane, ResizablePaneGroup } from '$lib/components/ui/resizable';
  import type { MarketEvent } from '$lib/types';

  // 组件配置常量
  const cardConfigs = {
    default: {
      size: 'md' as const,
      variant: 'default' as const,
      showActions: true,
    },
    detailed: {
      size: 'md' as const,
      variant: 'detailed' as const,
      showActions: true,
    },
    compact: {
      size: 'sm' as const,
      variant: 'compact' as const,
      showActions: false,
    },
    large: {
      size: 'lg' as const,
      variant: 'detailed' as const,
      showActions: true,
    },
  };

  // 测试数据
  const testEvents: MarketEvent[] = [
    // 您提供的新测试数据
    {
      id: '01JSKY3X725QG8AN81YGTJV7PD',
      marketEventType: 'LIQUIDATION_ORDER',
      currency: '01HWKTT6BZ7YKZZ545HTPD22N7',
      date: 1745499583,
      parameters: [
        {
          parameterId: 'side',
          value: 1,
        },
        {
          parameterId: 'exchange',
          value: 4096,
        },
        {
          parameterId: 'exchangeLabel',
          value: 'OKX',
        },
        {
          parameterId: 'threshold',
          value: 2,
        },
        {
          parameterId: 'base',
          value: 'FOXY',
        },
        {
          parameterId: 'pair',
          value: 'FOXY-USDT-SWAP',
        },
        {
          parameterId: 'priceUsd',
          value: 0.001887,
        },
        {
          parameterId: 'datetime',
          value: '2025-04-24T12:59:42.539000',
        },
        {
          parameterId: 'amount',
          value: 672000,
        },
        {
          parameterId: 'dailyVolumeRatio',
          value: 0.00041611941630712,
        },
        {
          parameterId: 'amountUsd',
          value: 1268.064,
        },
      ],
    },
    // 原有的 BTC 测试数据
    {
      id: '01JV70KA0VK0ESQ52AN5DX4M2Q',
      marketEventType: 'LIQUIDATION_ORDER',
      date: 1747213461,
      parameters: [
        { parameterId: 'side', value: 1 },
        { parameterId: 'exchange', value: 4096 },
        { parameterId: 'exchangeLabel', value: 'OKX' },
        { parameterId: 'base', value: 'BTC' },
        { parameterId: 'pair', value: 'BTC-USDT-SWAP' },
        { parameterId: 'priceUsd', value: 45000.5 },
        { parameterId: 'amount', value: 1.5 },
        { parameterId: 'amountUsd', value: 67500.75 },
      ],
      currency: '01J8QJG2T7RR1E2V0NFXFGF2RR',
    },
    // 添加一个多单爆仓示例
    {
      id: '01JSKY3X725QG8AN81YGTJV7PE',
      marketEventType: 'LIQUIDATION_ORDER',
      date: 1745499600,
      parameters: [
        { parameterId: 'side', value: 2 }, // 多单爆仓（红色）
        { parameterId: 'exchange', value: 4096 },
        { parameterId: 'exchangeLabel', value: 'OKX' },
        { parameterId: 'base', value: 'ETH' },
        { parameterId: 'pair', value: 'ETH-USDT-SWAP' },
        { parameterId: 'priceUsd', value: 2450.75 },
        { parameterId: 'amount', value: 0.8 },
        { parameterId: 'amountUsd', value: 1960.6 },
      ],
      currency: '01HAF64YPQCN0VFY2D0SRQQ0MD',
    },
    {
      id: '01JVHRTFGK1G87XEK7X8VXY1R0',
      marketEventType: 'TWAP',
      date: 1747574209,
      parameters: [
        { parameterId: 'twapId', value: '01JVHRTFCZ1779E2PG5GYPHFKW' },
        { parameterId: 'amountUsd', value: 4736.32 },
        { parameterId: 'threshold', value: 1 },
        { parameterId: 'side', value: 2 },
        { parameterId: 'market', value: 1 },
        { parameterId: 'duration', value: 4 },
        { parameterId: 'status', value: 'started' },
        { parameterId: 'base', value: 'ETH' },
      ],
      currency: '01HAF64YPQCN0VFY2D0SRQQ0MD',
    },
    // 添加您提供的新数据结构的 TWAP 检测示例
    {
      id: '01K1Q5NSYMT45VYVN17KVZRSVF',
      marketEventType: 'TWAP',
      date: Math.floor(1754198008000 / 1000), // 转换为秒
      parameters: [
        { parameterId: 'id', value: '01K1Q5NSYMT45VYVN17KVZRSVF' },
        { parameterId: 'last_updated', value: 1754198008000 },
        { parameterId: 'expired', value: 'null' },
        { parameterId: 'amount_usd', value: 1229.8655800000001 },
        { parameterId: 'created', value: 1754198008000 },
        { parameterId: 'delay', value: 23339 },
        { parameterId: 'coin', value: '01HAF64ZPM4V0KHW77X3AHV41G' },
        { parameterId: 'exchange', value: 1 },
        { parameterId: 'pair', value: 'CFXUSDT' },
        { parameterId: 'base', value: 'CFX' },
        { parameterId: 'side', value: 2 },
        { parameterId: 'size', value: 1619.0687162578165 },
        { parameterId: 'market', value: 2 },
        { parameterId: 'threshold', value: 1 },
      ],
      currency: '01HAF64ZPM4V0KHW77X3AHV41G',
    },
    // 添加另一个已过期的 TWAP 检测示例
    {
      id: '01K1Q5NSYMT45VYVN17KVZRSVG',
      marketEventType: 'TWAP',
      date: Math.floor(1754198008000 / 1000) - 3600, // 1小时前
      parameters: [
        { parameterId: 'id', value: '01K1Q5NSYMT45VYVN17KVZRSVG' },
        { parameterId: 'last_updated', value: 1754198008000 - 3600000 },
        { parameterId: 'expired', value: 1754198008000 - 1800000 }, // 30分钟前过期
        { parameterId: 'amount_usd', value: 2500.5 },
        { parameterId: 'created', value: 1754198008000 - 7200000 }, // 2小时前创建
        { parameterId: 'delay', value: 15000 },
        { parameterId: 'coin', value: '01J8QJG2T7RR1E2V0NFXFGF2RR' },
        { parameterId: 'exchange', value: 4096 },
        { parameterId: 'pair', value: 'BTCUSDT' },
        { parameterId: 'base', value: 'BTC' },
        { parameterId: 'side', value: 1 },
        { parameterId: 'size', value: 0.055 },
        { parameterId: 'market', value: 2 },
        { parameterId: 'threshold', value: 2 },
      ],
      currency: '01J8QJG2T7RR1E2V0NFXFGF2RR',
    },
    {
      id: '01JSKYXVDBFRBS8BZ4KWMYWJ3C',
      marketEventType: 'EXCHANGE_TRANSFER',
      currency: '01HEPMMG27T0J8Y1RKW74XHXTM',
      date: 1745500433,
      parameters: [
        { parameterId: 'sender', value: '******************************************' },
        { parameterId: 'recipient', value: '******************************************' },
        { parameterId: 'threshold', value: 0 },
        { parameterId: 'isExchangeToExchange', value: 'False' },
        { parameterId: 'isDepositWalletToExchange', value: 'True' },
        { parameterId: 'exchange', value: 512 },
        { parameterId: 'exchangeLabel', value: 'KRAKEN' },
        { parameterId: 'base', value: 'BEAM' },
        { parameterId: 'fromLabel', value: 'User Deposit Wallet' },
        { parameterId: 'toLabel', value: 'Hot Wallet' },
        { parameterId: 'toName', value: 'KRAKEN' },
        { parameterId: 'fromName', value: 'KRAKEN' },
        { parameterId: 'toAddress', value: '******************************************' },
        { parameterId: 'fromAddress', value: '******************************************' },
        {
          parameterId: 'txId',
          value: '0x125b8c1e69349abcad90af240c92a9b54ce8af5a95db7a699cc091c9dd0e937c',
        },
        { parameterId: 'exchangeSide', value: 2 }, // 存入/deposit
        { parameterId: 'datetime', value: '2025-04-24T13:09:59' },
        { parameterId: 'amount', value: 1002727 },
        { parameterId: 'amountUsd', value: 6871.9 },
        { parameterId: 'chain', value: 'Ethereum' },
      ],
    },
    // 添加一个转入的示例
    {
      id: '01JSKYXVDBFRBS8BZ4KWMYWJ3D',
      marketEventType: 'EXCHANGE_TRANSFER',
      currency: '01HAF64YPQCN0VFY2D0SRQQ0MD', // ETH
      date: 1745500500,
      parameters: [
        { parameterId: 'sender', value: '******************************************' },
        { parameterId: 'recipient', value: '0x8ba1f109551bD432803012645Hac189451b934' },
        { parameterId: 'threshold', value: 0 },
        { parameterId: 'isExchangeToExchange', value: 'False' },
        { parameterId: 'isDepositWalletToExchange', value: 'False' },
        { parameterId: 'exchange', value: 32 },
        { parameterId: 'exchangeLabel', value: 'COINBASE' },
        { parameterId: 'base', value: 'ETH' },
        { parameterId: 'fromLabel', value: 'Hot Wallet' },
        { parameterId: 'toLabel', value: 'User Withdrawal Wallet' },
        { parameterId: 'toName', value: 'External Wallet' },
        { parameterId: 'fromName', value: 'COINBASE' },
        { parameterId: 'toAddress', value: '0x8ba1f109551bD432803012645Hac189451b934' },
        { parameterId: 'fromAddress', value: '******************************************' },
        {
          parameterId: 'txId',
          value: '0x456b8c1e69349abcad90af240c92a9b54ce8af5a95db7a699cc091c9dd0e456c',
        },
        { parameterId: 'exchangeSide', value: 1 }, // 提取/withdrawal
        { parameterId: 'datetime', value: '2025-04-24T13:15:00' },
        { parameterId: 'amount', value: 2.5 },
        { parameterId: 'amountUsd', value: 6125.0 },
        { parameterId: 'chain', value: 'Ethereum' },
      ],
    },
    {
      id: '01JSKYG1QHVWE26XCVY3RKW8QX',
      marketEventType: 'ORDERBOOK_IMBALANCE',
      date: 1745499981,
      parameters: [
        { parameterId: 'deltaUsd', value: 5322.74 },
        { parameterId: 'side', value: 2 }, // 2 表示卖盘优势，对应附图中的 "more aggregated asks than bids"
        { parameterId: 'threshold', value: 0 },
        { parameterId: 'variationPercent', value: 41.53 }, // 对应附图中的 41.53%
        { parameterId: 'bidsSumUsd', value: 513800 }, // 对应附图中的 $513.80m
        { parameterId: 'asksSumUsd', value: 519530 }, // 对应附图中的 $519.53m
        { parameterId: 'bidsSum', value: 513800 },
        { parameterId: 'asksSum', value: 519530 },
        { parameterId: 'nbPairs', value: 5 },
        { parameterId: 'exchangeTopBidder', value: 4 },
        { parameterId: 'exchangeTopAsker', value: 512 },
        { parameterId: 'exchangeLabelTopBidder', value: 'BITGET' },
        { parameterId: 'exchangeLabelTopAsker', value: 'KRAKEN' },
        { parameterId: 'range', value: 30 },
        { parameterId: 'exchange', value: -1 },
        { parameterId: 'priceUsd', value: 0.08130785714285714 },
        { parameterId: 'aggregated', value: true },
        { parameterId: 'base', value: 'STRX' }, // 对应附图中的 STRX
        { parameterId: 'datetime', value: new Date(Date.now() - 60 * 60 * 1000).toISOString() }, // 1小时前
      ],
      currency: '01JP2G8RNR4JBJP81JS5JNJQ89',
    },
    {
      id: '01JSM0JMSAAST7BHZW7E02FV7V',
      marketEventType: 'VOLUME_INCREASE',
      date: 1745502163,
      parameters: [
        { parameterId: 'priceUsd', value: 0.07534 },
        { parameterId: 'previousPriceUsd', value: 0.07376 },
        { parameterId: 'exchange', value: 32 },
        { parameterId: 'exchangeLabel', value: 'COINBASE' },
        { parameterId: 'base', value: 'BIGTIME' },
        { parameterId: 'pair', value: 'BIGTIME/USDC' },
        { parameterId: 'threshold', value: 2 },
        { parameterId: 'aggregated', value: false },
        { parameterId: 'priceDirection', value: 1 },
        { parameterId: 'mean', value: 1427.61 },
        { parameterId: 'recentVolumeSumUsd', value: 67178.22 },
        { parameterId: 'percentAbnormal', value: 672.24 },
        { parameterId: 'isSpot', value: 1 },
        { parameterId: 'isDerivatives', value: 0 },
      ],
      currency: '01HCVQSJ0DG9QV5YNV9NP69KQ6',
    },
    {
      id: '01JTZ8PF84P6A1WM2585NHWX9B',
      marketEventType: 'OPEN_INTEREST_VARIATION',
      date: 1746953518,
      parameters: [
        { parameterId: 'base', value: 'SWELL' },
        { parameterId: 'pair', value: 'SWELL/USDT:USDT' },
        { parameterId: 'exchange', value: 4 },
        { parameterId: 'priceUsd', value: 0.01204 },
        { parameterId: 'direction', value: 2 },
        { parameterId: 'threshold', value: 0 },
        { parameterId: 'timeframe', value: '15m' },
        { parameterId: 'aggregated', value: false },
        { parameterId: 'oiVariation', value: -4411074 },
        { parameterId: 'absVariation', value: 2.73 },
        { parameterId: 'exchangeLabel', value: 'BITGET FUTURES' },
        { parameterId: 'oiVariationUsd', value: -53109.33 },
        { parameterId: 'currentOiAmount', value: 157232051 },
        { parameterId: 'previousOiAmount', value: 161643125 },
        { parameterId: 'variationPercent', value: -2.73 },
        { parameterId: 'currentOiValueUsd', value: 1893073.89 },
      ],
      currency: '01JC303X64R02BZP8P6CZD1BCE',
    },
    // 添加一个持仓量增加的示例（类似附图中的 TerraClassicUSD）
    {
      id: '01JTZ8PF84P6A1WM2585NHWX9C',
      marketEventType: 'OPEN_INTEREST_VARIATION',
      date: Date.now() / 1000 - 21 * 60, // 21分钟前
      parameters: [
        { parameterId: 'base', value: 'LUNC' },
        { parameterId: 'pair', value: 'LUNC/USDT:USDT' },
        { parameterId: 'exchange', value: 4 },
        { parameterId: 'priceUsd', value: 0.0147 },
        { parameterId: 'direction', value: 1 }, // 增加
        { parameterId: 'threshold', value: 0 },
        { parameterId: 'timeframe', value: '15m' },
        { parameterId: 'aggregated', value: true },
        { parameterId: 'oiVariation', value: 3250000 },
        { parameterId: 'absVariation', value: 44.69 },
        { parameterId: 'exchangeLabel', value: 'BITGET FUTURES, BINANCE FUTURES, OKX FUTURES' },
        { parameterId: 'oiVariationUsd', value: 3250000 },
        { parameterId: 'currentOiAmount', value: 715000000 },
        { parameterId: 'previousOiAmount', value: 511000000 },
        { parameterId: 'variationPercent', value: 44.69 },
        { parameterId: 'currentOiValueUsd', value: 10510000 },
      ],
      currency: '01HAF64KZERNEH9H5488BSX3XA', // TerraClassicUSD currency ID
    },
    {
      id: '01K19XJ4WCX0JF8RQPY81JFMT2',
      date: 1753753392,
      currency: '01HAF64KZERNEH9H5488BSX3XA',
      parameters: [
        {
          value: 0,
          parameterId: 'threshold',
        },
        {
          value: 'BITGET FUTURES',
          parameterId: 'exchangeLabel',
        },
        {
          value: 1,
          parameterId: 'side',
        },
        {
          value: 4,
          parameterId: 'exchange',
        },
        {
          value: '2025-07-24 03:43',
          parameterId: 'wasSince',
        },
        {
          value: 4,
          parameterId: 'daysSince',
        },
        {
          value: '2025-07-29T01:43:12.012235+00:00',
          parameterId: 'datetime',
        },
        {
          value: 'SNT/USDT:USDT',
          parameterId: 'pair',
        },
        {
          value: 'SNT',
          parameterId: 'base',
        },
        {
          value: 0.0039,
          parameterId: 'previousDailyFundingRate',
        },
        {
          value: -0.0033,
          parameterId: 'dailyFundingRate',
        },
        {
          value: false,
          parameterId: 'aggregated',
        },
        {
          value: -0.0001375,
          parameterId: 'fundingRate',
        },
        {
          value: 0.0001625,
          parameterId: 'previousFundingRate',
        },
        {
          value: -0.0003,
          parameterId: 'fundingRateDiff',
        },
      ],
      marketEventType: 'FUNDING_RATE_SWITCH',
    },
    {
      id: '01JSM0HHRQVJZ2MR50TG7XFK4B',
      marketEventType: 'EXTREME_FUNDING_RATE',
      currency: '01HAF64ZYT42GP9AK8MY45CK7T',
      date: 1745502127,
      parameters: [
        {
          parameterId: 'base',
          value: 'XDC',
        },
        {
          parameterId: 'pair',
          value: 'XDC/USDT:USDT',
        },
        {
          parameterId: 'side',
          value: 1,
        },
        {
          parameterId: 'datetime',
          value: '2025-04-24T13:42:07.895674+00:00',
        },
        {
          parameterId: 'exchange',
          value: 4,
        },
        {
          parameterId: 'wasSince',
          value: '2025-04-24 13:42',
        },
        {
          parameterId: 'daysSince',
          value: 0,
        },
        {
          parameterId: 'threshold',
          value: -1,
        },
        {
          parameterId: 'aggregated',
          value: false,
        },
        {
          parameterId: 'fundingRate',
          value: 0.0039,
        },
        {
          parameterId: 'exchangeLabel',
          value: 'BITGET FUTURES',
        },
        {
          parameterId: 'dailyFundingRate',
          value: 0.0936,
        },
        {
          parameterId: 'previousFundingRate',
          value: 0.00125,
        },
        {
          parameterId: 'previousDailyFundingRate',
          value: 0.03,
        },
      ],
    },
    {
      id: '01JSM0HHRQVJZ2MR50TG7XFK5C',
      marketEventType: 'EXTREME_FUNDING_RATE',
      currency: '01HAF64YPQCN0VFY2D0SRQQ0MD',
      date: 1745502200,
      parameters: [
        {
          parameterId: 'base',
          value: 'ETH',
        },
        {
          parameterId: 'pair',
          value: 'ETH/USDT:USDT',
        },
        {
          parameterId: 'side',
          value: -1,
        },
        {
          parameterId: 'datetime',
          value: '2025-04-24T13:43:20.123456+00:00',
        },
        {
          parameterId: 'exchange',
          value: 32,
        },
        {
          parameterId: 'exchangeLabel',
          value: 'COINBASE',
        },
        {
          parameterId: 'fundingRate',
          value: -0.00687,
        },
        {
          parameterId: 'dailyFundingRate',
          value: -0.16488,
        },
        {
          parameterId: 'previousFundingRate',
          value: 0.00125,
        },
        {
          parameterId: 'previousDailyFundingRate',
          value: 0.03,
        },
      ],
    },
  ];

  let selectedEvent: MarketEvent | null = $state(null);

  // 通用属性函数
  function getCommonProps(event: MarketEvent, config: any = cardConfigs.default) {
    return {
      event,
      isHighlighted: selectedEvent?.id === event.id,
      onCardClick: handleCardClick,
      ...config,
    };
  }

  // 按事件类型分组数据
  const eventsByType = $derived(() => {
    const groups = testEvents.reduce(
      (acc, event) => {
        const type = event.marketEventType;
        if (!acc[type]) acc[type] = [];
        acc[type].push(event);
        return acc;
      },
      {} as Record<string, MarketEvent[]>
    );

    return {
      liquidationOrders: groups['LIQUIDATION_ORDER'] || [],
      twapDetections: groups['TWAP_DETECTION'] || [],
      exchangeTransfers: groups['EXCHANGE_TRANSFER'] || [],
      orderbookImbalances: groups['ORDERBOOK_IMBALANCE'] || [],
      fundingRateSwitches: groups['FUNDING_RATE_SWITCH'] || [],
      extremeFundingRates: groups['EXTREME_FUNDING_RATE'] || [],
      volumeIncreases: groups['VOLUME_INCREASE'] || [],
      openInterestVariations: groups['OPEN_INTEREST_VARIATION'] || [],
      allEvents: testEvents,
    };
  });

  function handleCardClick(event: MarketEvent) {
    selectedEvent = selectedEvent?.id === event.id ? null : event;
  }
</script>

<div class="container mx-auto space-y-8 p-6">
  <div class="space-y-2">
    <h1 class="text-3xl font-bold tracking-tight">卡片组件测试</h1>
    <p class="text-muted-foreground">测试新的事件卡片组件系统</p>
  </div>

  <!-- 基础卡片测试 -->
  <section class="space-y-4">
    <h2 class="text-2xl font-semibold">基础卡片组件</h2>
    <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
      {#each eventsByType().allEvents as event}
        <BaseEventCard {...getCommonProps(event, cardConfigs.default)} />
      {/each}
    </div>
  </section>

  <!-- 清算订单专门测试 -->
  <section class="space-y-4">
    <h2 class="text-2xl font-semibold">清算订单卡片测试</h2>
    <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
      {#each eventsByType().liquidationOrders as event}
        <LiquidationOrderCard {...getCommonProps(event, cardConfigs.detailed)} />
      {/each}
    </div>
  </section>

  <!-- TWAP检测专门测试 -->
  <section class="space-y-4">
    <h2 class="text-2xl font-semibold">TWAP检测卡片测试</h2>
    <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
      {#each eventsByType().twapDetections as event}
        <TwapDetectionCard {...getCommonProps(event, cardConfigs.detailed)} />
      {/each}
    </div>
  </section>

  <!-- 交易所转账专门测试 -->
  <section class="space-y-4">
    <h2 class="text-2xl font-semibold">交易所转账卡片测试</h2>
    <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
      {#each eventsByType().exchangeTransfers as event}
        <ExchangeTransferCard {...getCommonProps(event, cardConfigs.detailed)} />
      {/each}
    </div>
  </section>

  <!-- 窄容器响应式测试 - 使用 Resizable 组件 -->
  <section class="space-y-4">
    <h2 class="text-2xl font-semibold">窄容器响应式测试 - 可调整宽度</h2>
    <p class="text-muted-foreground">拖拽调整容器宽度，实时观察卡片的响应式适配效果</p>

    <ResizablePaneGroup direction="horizontal" class="min-h-[400px] rounded-lg border">
      <!-- 可调整宽度的测试容器 -->
      <ResizablePane defaultSize={25} minSize={10} maxSize={50}>
        <div class="h-full space-y-4 p-4">
          <h3 class="text-lg font-medium">可调整宽度容器</h3>
          <p class="text-muted-foreground text-sm">当前显示交易所转账卡片</p>
          <div class="space-y-3 overflow-auto">
            {#each eventsByType().exchangeTransfers.slice(0, 2) as event}
              <ExchangeTransferCard {...getCommonProps(event, cardConfigs.compact)} />
            {/each}
          </div>
        </div>
      </ResizablePane>

      <ResizableHandle />

      <!-- 参考容器 - 显示其他类型卡片 -->
      <ResizablePane defaultSize={75}>
        <div class="h-full space-y-4 p-4">
          <h3 class="text-lg font-medium">参考容器 - 其他卡片类型</h3>
          <div class="grid grid-cols-1 gap-4 overflow-auto md:grid-cols-2 lg:grid-cols-3">
            {#each eventsByType().liquidationOrders.slice(0, 3) as event}
              <LiquidationOrderCard {...getCommonProps(event, cardConfigs.detailed)} />
            {/each}
            {#each eventsByType().volumeIncreases.slice(0, 2) as event}
              <VolumeIncreaseCard {...getCommonProps(event, cardConfigs.detailed)} />
            {/each}
          </div>
        </div>
      </ResizablePane>
    </ResizablePaneGroup>
  </section>

  <!-- 订单簿失衡专门测试 -->
  <section class="space-y-4">
    <h2 class="text-2xl font-semibold">订单簿失衡卡片测试</h2>
    <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
      {#each eventsByType().orderbookImbalances as event}
        <OrderbookImbalanceCard {...getCommonProps(event, cardConfigs.detailed)} />
      {/each}
    </div>
  </section>

  <!-- 资金费率切换专门测试 -->
  <section class="space-y-4">
    <h2 class="text-2xl font-semibold">资金费率切换卡片测试</h2>
    <p class="text-muted-foreground">重构后的 FundingRateSwitchCard 组件，参考附图设计</p>
    <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
      {#each eventsByType().fundingRateSwitches as event}
        <FundingRateSwitchCard {...getCommonProps(event, cardConfigs.detailed)} />
      {/each}
    </div>
  </section>

  <!-- 极端资金费率专门测试 -->
  <section class="space-y-4">
    <h2 class="text-2xl font-semibold">极端资金费率卡片测试</h2>
    <p class="text-muted-foreground">重构后的 ExtremeFundingRateCard 组件，参考 VeChain 附图设计</p>
    <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
      {#each eventsByType().extremeFundingRates as event}
        <ExtremeFundingRateCard {...getCommonProps(event, cardConfigs.detailed)} />
      {/each}
    </div>
  </section>

  <!-- 交易量增长专门测试 -->
  <section class="space-y-4">
    <h2 class="text-2xl font-semibold">交易量增长卡片测试</h2>
    <p class="text-muted-foreground">
      重构后的 VolumeIncreaseCard 组件，使用 CurrencyIcon 展示加密货币图标，参考 Chainlink 附图设计
    </p>
    <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
      {#each eventsByType().volumeIncreases as event}
        <VolumeIncreaseCard {...getCommonProps(event, cardConfigs.detailed)} />
      {/each}
    </div>
  </section>

  <!-- 持仓量变化专门测试 -->
  <section class="space-y-4">
    <h2 class="text-2xl font-semibold">持仓量变化卡片测试</h2>
    <p class="text-muted-foreground">
      重构后的 OpenInterestVariationCard 组件，使用 CurrencyIcon 展示加密货币图标，参考
      TerraClassicUSD 附图设计
    </p>
    <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
      {#each eventsByType().openInterestVariations as event}
        <OpenInterestVariationCard {...getCommonProps(event, cardConfigs.detailed)} />
      {/each}
    </div>
  </section>

  <!-- 不同尺寸测试 - 使用 Resizable 组件 -->
  <section class="space-y-4">
    <h2 class="text-2xl font-semibold">不同尺寸测试 - 可调整宽度</h2>
    <p class="text-muted-foreground">拖拽调整容器宽度，实时观察卡片的响应式适配效果</p>

    <ResizablePaneGroup direction="horizontal" class="min-h-[400px] rounded-lg border">
      <!-- 可调整宽度的测试容器 -->
      <ResizablePane defaultSize={25} minSize={10} maxSize={50}>
        <div class="h-full space-y-4 p-4">
          <h3 class="text-lg font-medium">可调整宽度容器</h3>
          <p class="text-muted-foreground text-sm">专门卡片类型的紧凑模式测试</p>
          <div class="space-y-3 overflow-auto">
            <!-- 清算订单卡片 -->
            {#each eventsByType().liquidationOrders.slice(0, 1) as event}
              <LiquidationOrderCard {...getCommonProps(event, cardConfigs.compact)} />
            {/each}

            <!-- TWAP检测卡片 -->
            {#each eventsByType().twapDetections.slice(0, 1) as event}
              <TwapDetectionCard {...getCommonProps(event, cardConfigs.compact)} />
            {/each}

            <!-- 交易所转账卡片 -->
            {#each eventsByType().exchangeTransfers.slice(0, 1) as event}
              <ExchangeTransferCard {...getCommonProps(event, cardConfigs.compact)} />
            {/each}

            <!-- 订单簿失衡卡片 -->
            {#each eventsByType().orderbookImbalances.slice(0, 1) as event}
              <OrderbookImbalanceCard {...getCommonProps(event, cardConfigs.compact)} />
            {/each}

            <!-- 资金费率切换卡片 -->
            {#each eventsByType().fundingRateSwitches.slice(0, 1) as event}
              <FundingRateSwitchCard {...getCommonProps(event, cardConfigs.compact)} />
            {/each}

            <!-- 极端资金费率卡片 -->
            {#each eventsByType().extremeFundingRates.slice(0, 1) as event}
              <ExtremeFundingRateCard {...getCommonProps(event, cardConfigs.compact)} />
            {/each}

            <!-- 交易量增长卡片 -->
            {#each eventsByType().volumeIncreases.slice(0, 1) as event}
              <VolumeIncreaseCard {...getCommonProps(event, cardConfigs.compact)} />
            {/each}

            <!-- 持仓量变化卡片 -->
            {#each eventsByType().openInterestVariations.slice(0, 1) as event}
              <OpenInterestVariationCard {...getCommonProps(event, cardConfigs.compact)} />
            {/each}
          </div>
        </div>
      </ResizablePane>

      <ResizableHandle />

      <!-- 参考容器 - 显示不同尺寸配置 -->
      <ResizablePane defaultSize={75}>
        <div class="h-full space-y-6 p-4">
          <h3 class="text-lg font-medium">参考容器 - 不同尺寸配置对比</h3>

          <!-- 中等尺寸 (md) 配置 -->
          <div class="space-y-3">
            <h4 class="text-md text-muted-foreground font-medium">中等尺寸 (md) - detailed 变体</h4>
            <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
              {#each eventsByType().liquidationOrders.slice(0, 2) as event}
                <LiquidationOrderCard {...getCommonProps(event, cardConfigs.detailed)} />
              {/each}
              {#each eventsByType().twapDetections.slice(0, 2) as event}
                <TwapDetectionCard {...getCommonProps(event, cardConfigs.detailed)} />
              {/each}
            </div>
          </div>

          <!-- 大尺寸 (lg) 配置 -->
          <div class="space-y-3">
            <h4 class="text-md text-muted-foreground font-medium">大尺寸 (lg) - detailed 变体</h4>
            <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
              {#each eventsByType().exchangeTransfers.slice(0, 1) as event}
                <ExchangeTransferCard {...getCommonProps(event, cardConfigs.large)} />
              {/each}
              {#each eventsByType().volumeIncreases.slice(0, 1) as event}
                <VolumeIncreaseCard {...getCommonProps(event, cardConfigs.large)} />
              {/each}
            </div>
          </div>

          <!-- 混合尺寸展示 -->
          <div class="space-y-3">
            <h4 class="text-md text-muted-foreground font-medium">混合尺寸展示</h4>
            <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
              {#each eventsByType().orderbookImbalances.slice(0, 1) as event}
                <OrderbookImbalanceCard {...getCommonProps(event, cardConfigs.detailed)} />
              {/each}
              {#each eventsByType().extremeFundingRates.slice(0, 1) as event}
                <ExtremeFundingRateCard {...getCommonProps(event, cardConfigs.detailed)} />
              {/each}
              {#each eventsByType().openInterestVariations.slice(0, 1) as event}
                <OpenInterestVariationCard {...getCommonProps(event, cardConfigs.large)} />
              {/each}
            </div>
          </div>
        </div>
      </ResizablePane>
    </ResizablePaneGroup>
  </section>

  <!-- 选中事件详情 -->
  {#if selectedEvent}
    <section class="space-y-4">
      <h2 class="text-2xl font-semibold">选中事件详情</h2>
      <div class="bg-muted/50 rounded-lg p-4">
        <pre class="overflow-auto text-sm">{JSON.stringify(selectedEvent, null, 2)}</pre>
      </div>
    </section>
  {/if}
</div>
