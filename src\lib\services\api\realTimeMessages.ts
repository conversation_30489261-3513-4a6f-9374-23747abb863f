// src/lib/services/api/realTimeMessages.ts

import type { RealTimeMessage } from '$lib/stores/features/realTimeMessages';
import type { ApiResponse } from '$lib/types';

import { BaseApiService } from './base';

/**
 * 实时消息查询参数接口
 */
export interface RealTimeMessagesQuery {
  /** 页码 */
  page?: number;
  /** 每页数量 */
  limit?: number;
  /** 消息类型过滤 */
  type?: string;
  /** 时间范围开始 */
  startTime?: string;
  /** 时间范围结束 */
  endTime?: string;
  /** 只获取未读消息 */
  unreadOnly?: boolean;
  /** 最后更新时间（用于增量获取） */
  lastUpdateTime?: string;
}

/**
 * 实时消息 API 响应接口
 */
export interface RealTimeMessagesResponse {
  /** 消息列表 */
  messages: RealTimeMessage[];
  /** 总数量 */
  total: number;
  /** 当前页码 */
  page: number;
  /** 每页数量 */
  limit: number;
  /** 是否有更多数据 */
  hasMore: boolean;
  /** 服务器时间戳 */
  serverTime: string;
}

/**
 * 实时消息 API 服务类
 * 负责与后端 API 进行实时消息数据的交互
 */
export class RealTimeMessagesApiService extends BaseApiService {
  /**
   * 获取实时消息列表
   */
  async getMessages(
    query: RealTimeMessagesQuery = {}
  ): Promise<ApiResponse<RealTimeMessagesResponse>> {
    const params = new URLSearchParams();

    // 构建查询参数
    if (query.page !== undefined) params.append('page', query.page.toString());
    if (query.limit !== undefined) params.append('limit', query.limit.toString());
    if (query.type) params.append('type', query.type);
    if (query.startTime) params.append('startTime', query.startTime);
    if (query.endTime) params.append('endTime', query.endTime);
    if (query.unreadOnly) params.append('unreadOnly', 'true');
    if (query.lastUpdateTime) params.append('lastUpdateTime', query.lastUpdateTime);

    const endpoint = `/real-time-messages${params.toString() ? `?${params.toString()}` : ''}`;
    return this.get<RealTimeMessagesResponse>(endpoint);
  }

  /**
   * 获取最新消息（用于轮询）
   * 只获取指定时间之后的新消息
   */
  async getLatestMessages(lastUpdateTime?: string): Promise<ApiResponse<RealTimeMessagesResponse>> {
    return this.getMessages({
      lastUpdateTime,
      limit: 50, // 限制单次获取数量
    });
  }

  /**
   * 标记消息为已读
   */
  async markAsRead(messageId: string): Promise<ApiResponse<{ success: boolean }>> {
    return this.put<{ success: boolean }>(`/real-time-messages/${messageId}/read`);
  }

  /**
   * 批量标记消息为已读
   */
  async markMultipleAsRead(
    messageIds: string[]
  ): Promise<ApiResponse<{ success: boolean; count: number }>> {
    return this.put<{ success: boolean; count: number }>('/real-time-messages/batch-read', {
      messageIds,
    });
  }

  /**
   * 标记所有消息为已读
   */
  async markAllAsRead(): Promise<ApiResponse<{ success: boolean; count: number }>> {
    return this.put<{ success: boolean; count: number }>('/real-time-messages/read-all');
  }

  /**
   * 删除消息
   */
  async deleteMessage(messageId: string): Promise<ApiResponse<{ success: boolean }>> {
    return this.delete<{ success: boolean }>(`/real-time-messages/${messageId}`);
  }

  /**
   * 批量删除消息
   */
  async deleteMultipleMessages(
    messageIds: string[]
  ): Promise<ApiResponse<{ success: boolean; count: number }>> {
    return this.post<{ success: boolean; count: number }>('/real-time-messages/batch-delete', {
      messageIds,
    });
  }

  /**
   * 清除所有消息
   */
  async clearAllMessages(): Promise<ApiResponse<{ success: boolean; count: number }>> {
    return this.delete<{ success: boolean; count: number }>('/real-time-messages/clear-all');
  }

  /**
   * 获取消息统计信息
   */
  async getMessageStats(): Promise<
    ApiResponse<{
      total: number;
      unread: number;
      byType: Record<string, number>;
      lastUpdateTime: string;
    }>
  > {
    return this.get('/real-time-messages/stats');
  }
}

/**
 * 创建实时消息 API 服务实例
 */
export const createRealTimeMessagesApiService = (baseURL?: string) =>
  new RealTimeMessagesApiService(baseURL);

/**
 * 默认实时消息 API 服务实例
 */
export const realTimeMessagesApiService = createRealTimeMessagesApiService();
