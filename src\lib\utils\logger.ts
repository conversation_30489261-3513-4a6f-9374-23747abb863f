// src/lib/utils/logger.ts
import { browser } from '$app/environment';
import { dev } from '$app/environment';

/**
 * 日志级别枚举
 */
export enum LogLevel {
  TRACE = 10,
  DEBUG = 20,
  INFO = 30,
  WARN = 40,
  ERROR = 50,
  FATAL = 60,
}

/**
 * 日志级别字符串映射
 */
export const LOG_LEVEL_NAMES: Record<LogLevel, string> = {
  [LogLevel.TRACE]: 'trace',
  [LogLevel.DEBUG]: 'debug',
  [LogLevel.INFO]: 'info',
  [LogLevel.WARN]: 'warn',
  [LogLevel.ERROR]: 'error',
  [LogLevel.FATAL]: 'fatal',
};

/**
 * 从环境变量获取日志级别
 */
function getLogLevelFromEnv(): LogLevel {
  if (browser) {
    const envLevel = import.meta.env.VITE_LOG_LEVEL?.toLowerCase();
    switch (envLevel) {
      case 'trace':
        return LogLevel.TRACE;
      case 'debug':
        return LogLevel.DEBUG;
      case 'info':
        return LogLevel.INFO;
      case 'warn':
        return LogLevel.WARN;
      case 'error':
        return LogLevel.ERROR;
      case 'fatal':
        return LogLevel.FATAL;
      default:
        return LogLevel.INFO; // 默认使用 INFO 级别，避免过多 debug 日志
    }
  }
  return LogLevel.INFO; // 默认使用 INFO 级别，避免过多 debug 日志
}

/**
 * 从环境变量获取是否启用美化打印
 */
function getPrettyPrintFromEnv(): boolean {
  if (browser) {
    const envPretty = import.meta.env.VITE_LOG_PRETTY_PRINT;
    if (envPretty !== undefined) {
      return envPretty === 'true' || envPretty === true;
    }
  }
  return dev;
}

/**
 * 日志配置接口
 */
export interface LoggerConfig {
  level: LogLevel;
  name?: string;
  prettyPrint?: boolean;
  timestamp?: boolean;
  context?: Record<string, unknown>;
}

/**
 * 日志条目接口
 */
export interface LogEntry {
  level: LogLevel;
  time: string;
  name?: string;
  msg: string;
  context?: Record<string, unknown>;
  [key: string]: unknown;
}

/**
 * 统一的 Logger 类
 *
 * 支持 SvelteKit 的 SSR 和客户端环境，提供结构化日志记录功能。
 * 在服务端使用 Pino，在客户端使用优化的控制台输出。
 *
 * @example
 * ```typescript
 * import { logger } from '$lib/utils/logger';
 *
 * // 基本使用
 * logger.info('应用启动');
 * logger.error('发生错误', { error: err });
 *
 * // 创建子 logger
 * const moduleLogger = logger.child({ module: 'dashboard' });
 * moduleLogger.debug('模块初始化完成');
 * ```
 */
export class Logger {
  private config: LoggerConfig;
  private pinoLogger: any = null;

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = {
      level: getLogLevelFromEnv(),
      prettyPrint: getPrettyPrintFromEnv(),
      timestamp: true,
      ...config,
    };

    this.initializeLogger();
  }

  /**
   * 初始化 Logger
   */
  private async initializeLogger() {
    if (!browser) {
      // 服务端：使用 Pino
      try {
        const pino = await import('pino');

        const pinoConfig: any = {
          level: LOG_LEVEL_NAMES[this.config.level],
          name: this.config.name,
          timestamp: this.config.timestamp,
        };

        if (this.config.prettyPrint) {
          const pinoPretty = await import('pino-pretty');
          pinoConfig.transport = {
            target: 'pino-pretty',
            options: {
              colorize: true,
              translateTime: 'SYS:standard',
              ignore: 'pid,hostname',
            },
          };
        }

        this.pinoLogger = pino.default(pinoConfig);
      } catch (error) {
        console.warn('Failed to initialize Pino logger, falling back to console:', error);
      }
    }
  }

  /**
   * 检查日志级别是否启用
   */
  private isLevelEnabled(level: LogLevel): boolean {
    return level >= this.config.level;
  }

  /**
   * 格式化日志消息（客户端使用）
   */
  private formatMessage(
    level: LogLevel,
    message: string,
    context?: Record<string, unknown>
  ): string {
    const timestamp = new Date().toISOString();
    const levelName = LOG_LEVEL_NAMES[level].toUpperCase();
    const name = this.config.name ? `[${this.config.name}]` : '';

    let formatted = `${timestamp} ${levelName}${name}: ${message}`;

    if (context && Object.keys(context).length > 0) {
      formatted += ` ${JSON.stringify(context)}`;
    }

    return formatted;
  }

  /**
   * 获取日志级别对应的控制台方法
   */
  private getConsoleMethod(level: LogLevel): (...args: any[]) => void {
    switch (level) {
      case LogLevel.TRACE:
      case LogLevel.DEBUG:
        return console.debug;
      case LogLevel.INFO:
        return console.info;
      case LogLevel.WARN:
        return console.warn;
      case LogLevel.ERROR:
      case LogLevel.FATAL:
        return console.error;
      default:
        return console.log;
    }
  }

  /**
   * 通用日志方法
   */
  private log(level: LogLevel, message: string, context?: Record<string, unknown>) {
    if (!this.isLevelEnabled(level)) return;

    if (!browser && this.pinoLogger) {
      // 服务端：使用 Pino
      const levelName = LOG_LEVEL_NAMES[level];
      const mergedContext = { ...this.config.context, ...context };

      if (Object.keys(mergedContext).length > 0) {
        this.pinoLogger[levelName](mergedContext, message);
      } else {
        this.pinoLogger[levelName](message);
      }
    } else {
      // 客户端：使用格式化的控制台输出
      const consoleMethod = this.getConsoleMethod(level);
      const mergedContext = { ...this.config.context, ...context };

      if (this.config.prettyPrint) {
        const formatted = this.formatMessage(level, message, mergedContext);
        consoleMethod(formatted);
      } else {
        if (Object.keys(mergedContext).length > 0) {
          consoleMethod(message, mergedContext);
        } else {
          consoleMethod(message);
        }
      }
    }
  }

  /**
   * TRACE 级别日志
   */
  trace(message: string, context?: Record<string, unknown>) {
    this.log(LogLevel.TRACE, message, context);
  }

  /**
   * DEBUG 级别日志
   */
  debug(message: string, context?: Record<string, unknown>) {
    this.log(LogLevel.DEBUG, message, context);
  }

  /**
   * INFO 级别日志
   */
  info(message: string, context?: Record<string, unknown>) {
    this.log(LogLevel.INFO, message, context);
  }

  /**
   * WARN 级别日志
   */
  warn(message: string, context?: Record<string, unknown>) {
    this.log(LogLevel.WARN, message, context);
  }

  /**
   * ERROR 级别日志
   */
  error(message: string, context?: Record<string, unknown>) {
    this.log(LogLevel.ERROR, message, context);
  }

  /**
   * FATAL 级别日志
   */
  fatal(message: string, context?: Record<string, unknown>) {
    this.log(LogLevel.FATAL, message, context);
  }

  /**
   * 创建子 Logger
   */
  child(context: Record<string, unknown>, options?: Partial<LoggerConfig>): Logger {
    const childConfig = {
      ...this.config,
      ...options,
      context: { ...this.config.context, ...context },
    };

    return new Logger(childConfig);
  }

  /**
   * 设置日志级别
   */
  setLevel(level: LogLevel) {
    this.config.level = level;
    if (this.pinoLogger) {
      this.pinoLogger.level = LOG_LEVEL_NAMES[level];
    }
  }

  /**
   * 获取当前日志级别
   */
  getLevel(): LogLevel {
    return this.config.level;
  }
}

/**
 * 默认 Logger 实例
 */
export const logger = new Logger({
  name: 'svelte-demo',
});

/**
 * 创建模块专用的 Logger
 */
export function createModuleLogger(moduleName: string, context?: Record<string, unknown>): Logger {
  // 动态导入配置以避免循环依赖
  let moduleLevel: LogLevel | undefined;
  try {
    const { getModuleLogLevel } = require('./logger.config');
    moduleLevel = getModuleLogLevel(moduleName);
  } catch {
    // 如果配置文件不可用，使用默认级别
    moduleLevel = undefined;
  }

  const config: Partial<LoggerConfig> = {
    context: { module: moduleName, ...context },
  };

  if (moduleLevel !== undefined) {
    config.level = moduleLevel;
  }

  return new Logger(config);
}

/**
 * 性能监控专用 Logger
 */
export const performanceLogger = createModuleLogger('performance');

/**
 * API 服务专用 Logger
 */
export const apiLogger = createModuleLogger('api');

/**
 * 状态管理专用 Logger
 */
export const storeLogger = createModuleLogger('store');

/**
 * 主题相关专用 Logger
 */
export const themeLogger = createModuleLogger('theme');

/**
 * MSW 专用 Logger
 */
export const mswLogger = createModuleLogger('msw');

/**
 * 错误处理专用 Logger
 */
export const errorLogger = createModuleLogger('error');

/**
 * 增强的错误日志记录函数
 */
export interface ErrorLogContext {
  /** 错误ID */
  errorId?: string;
  /** 错误类型 */
  errorType?: string;
  /** 用户ID */
  userId?: string;
  /** 会话ID */
  sessionId?: string;
  /** 页面URL */
  url?: string;
  /** 用户代理 */
  userAgent?: string;
  /** 组件名称 */
  component?: string;
  /** 操作名称 */
  operation?: string;
  /** 请求ID */
  requestId?: string;
  /** HTTP状态码 */
  statusCode?: number;
  /** 重试次数 */
  attempts?: number;
  /** 附加上下文 */
  context?: Record<string, any>;
}

/**
 * 记录错误日志的统一函数
 */
export function logError(
  error: Error | string,
  context: ErrorLogContext = {},
  logger: Logger = errorLogger
): void {
  const errorMessage = error instanceof Error ? error.message : error;
  const errorStack = error instanceof Error ? error.stack : undefined;

  const logContext = {
    timestamp: new Date().toISOString(),
    message: errorMessage,
    stack: errorStack,
    name: error instanceof Error ? error.name : 'UnknownError',
    ...context,
  };

  logger.error('Error occurred', logContext);
}

/**
 * 记录API错误的专用函数
 */
export function logApiError(
  error: Error | string,
  endpoint: string,
  method: string = 'GET',
  statusCode?: number,
  context: ErrorLogContext = {}
): void {
  logError(
    error,
    {
      ...context,
      errorType: 'api-error',
      operation: `${method} ${endpoint}`,
      statusCode,
    },
    apiLogger
  );
}

/**
 * 记录组件错误的专用函数
 */
export function logComponentError(
  error: Error | string,
  componentName: string,
  context: ErrorLogContext = {}
): void {
  logError(error, {
    ...context,
    errorType: 'component-error',
    component: componentName,
  });
}

/**
 * 记录状态管理错误的专用函数
 */
export function logStoreError(
  error: Error | string,
  storeName: string,
  operation: string,
  context: ErrorLogContext = {}
): void {
  logError(
    error,
    {
      ...context,
      errorType: 'store-error',
      component: storeName,
      operation,
    },
    storeLogger
  );
}
