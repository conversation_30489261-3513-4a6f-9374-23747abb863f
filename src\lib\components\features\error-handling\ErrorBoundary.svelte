<!-- src/lib/components/features/error-handling/ErrorBoundary.svelte -->
<script lang="ts">
  import { onDestroy, onMount } from 'svelte';

  import { createModuleLogger } from '$lib/utils/logger';
  import { errorManager } from '$lib/utils/errorManager';

  import { ErrorDisplay } from './index';
  import { ErrorRecoveryHelper } from './index';
  import type { ErrorBoundaryProps, ErrorInfo } from './types';

  // Props
  export let fallbackComponent: any = null;
  export let showErrorDetails = false;
  export let enableRecovery = true;
  export let onError: ((error: Error, errorInfo?: any) => void) | null = null;

  // 错误状态
  let hasError = false;
  let error: Error | null = null;
  let errorInfo: any = null;

  const errorLogger = createModuleLogger('error-boundary');

  /**
   * 判断是否需要显示错误页面
   */
  function shouldShowErrorPage(err: Error, info?: any): boolean {
    // 路由错误
    if (info?.type === 'route-error' || err.message.includes('route')) {
      return true;
    }

    // 严重的组件渲染错误
    if (info?.type === 'component-fatal-error') {
      return true;
    }

    // 404 错误等
    if (err.name === 'NotFound' || err.message.includes('404')) {
      return true;
    }

    return false;
  }

  /**
   * 处理错误 - 仅处理需要错误页面的错误
   */
  function handleError(err: Error, info?: any) {
    // 检查是否需要显示错误页面
    if (!shouldShowErrorPage(err, info)) {
      // 交给 errorManager 处理（显示通知）
      errorManager.captureError(err, info);
      return;
    }

    hasError = true;
    error = err;
    errorInfo = info;

    // 记录需要错误页面的严重错误
    errorLogger.error('Critical error requiring error page', {
      message: err.message,
      stack: err.stack,
      type: info?.type || 'route-error'
    });

    // 通知 errorManager 但不显示通知
    errorManager.captureError(err, { ...info, suppressNotification: true });

    // 调用外部错误处理器
    if (onError) {
      try {
        onError(err, info);
      } catch (handlerError) {
        errorLogger.error('Error in onError handler', { handlerError });
      }
    }
  }

  /**
   * 重置错误状态
   */
  function resetError() {
    hasError = false;
    error = null;
    errorInfo = null;
    errorLogger.info('ErrorBoundary reset');
  }

  /**
   * 处理错误边界触发事件
   */
  function handleErrorBoundaryTrigger(event: Event) {
    const customEvent = event as CustomEvent;
    const { error: triggerError, errorInfo: triggerErrorInfo } = customEvent.detail;
    handleError(triggerError, triggerErrorInfo);
  }

  onMount(() => {
    // 监听 errorManager 触发的错误边界事件
    window.addEventListener('error-boundary-trigger', handleErrorBoundaryTrigger as EventListener);

    errorLogger.debug('ErrorBoundary mounted and listening for error boundary triggers');
  });

  onDestroy(() => {
    // 清理事件监听器
    window.removeEventListener('error-boundary-trigger', handleErrorBoundaryTrigger as EventListener);

    errorLogger.debug('ErrorBoundary destroyed and event listeners removed');
  });

  // 导出处理函数供外部调用
  export { handleError };
</script>

{#if hasError && error}
  {#if fallbackComponent}
    <!-- 使用自定义的错误组件 -->
    <svelte:component this={fallbackComponent} {error} {errorInfo} {resetError} />
  {:else}
    <!-- 默认错误页面 -->
    <div class="flex min-h-[400px] items-center justify-center p-4">
      <div class="w-full max-w-2xl space-y-4">
        <ErrorDisplay
          {error}
          title="页面遇到了问题"
          showRetry={enableRecovery}
          showDetails={showErrorDetails}
          onRetry={resetError}
        />

        {#if enableRecovery}
          <ErrorRecoveryHelper
            {error}
            {errorInfo}
            {resetError}
          />
        {/if}
      </div>
    </div>
  {/if}
{:else}
  <!-- 正常渲染子组件 -->
  <slot />
{/if}
