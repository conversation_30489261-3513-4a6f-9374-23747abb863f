// src/lib/components/layout/AppSidebar/utils.ts

import type {
  NavItem,
  NavSubItem,
  ProjectItem,
  SidebarConfig,
  TeamInfo,
  UserInfo,
} from './types.js';

/**
 * 类型守卫：检查导航项是否有子项
 */
export function hasSubItems(item: NavItem): item is NavItem & { items: NavSubItem[] } {
  return Array.isArray(item.items) && item.items.length > 0;
}

/**
 * 类型守卫：检查导航项是否为叶子节点
 */
export function isLeafNavItem(item: NavItem): item is NavItem & { url: string } {
  return typeof item.url === 'string' && !hasSubItems(item);
}

/**
 * 类型守卫：检查项目是否有有效的状态
 */
export function hasValidStatus(
  project: ProjectItem
): project is ProjectItem & { status: NonNullable<ProjectItem['status']> } {
  return (
    project.status !== undefined && ['active', 'inactive', 'development'].includes(project.status)
  );
}

/**
 * 类型守卫：检查用户是否有头像
 */
export function hasAvatar(user: UserInfo): user is UserInfo & { avatar: string } {
  return typeof user.avatar === 'string' && user.avatar.length > 0;
}

/**
 * 类型守卫：检查团队是否有 Logo
 */
export function hasLogo(
  team: TeamInfo
): team is TeamInfo & { logo: NonNullable<TeamInfo['logo']> } {
  return team.logo !== undefined;
}

/**
 * 验证导航项配置的有效性
 */
export function validateNavItem(item: NavItem): boolean {
  // 检查必需字段
  if (!item.title || typeof item.title !== 'string') {
    return false;
  }

  // 如果有子项，验证子项
  if (item.items) {
    if (!Array.isArray(item.items)) {
      return false;
    }
    return item.items.every(validateNavSubItem);
  }

  // 如果没有子项，必须有 URL
  if (!item.items && !item.url) {
    return false;
  }

  return true;
}

/**
 * 验证导航子项配置的有效性
 */
export function validateNavSubItem(item: NavSubItem): boolean {
  return (
    typeof item.title === 'string' &&
    item.title.length > 0 &&
    typeof item.url === 'string' &&
    item.url.length > 0
  );
}

/**
 * 验证项目配置的有效性
 */
export function validateProjectItem(project: ProjectItem): boolean {
  return (
    typeof project.name === 'string' &&
    project.name.length > 0 &&
    typeof project.url === 'string' &&
    project.url.length > 0 &&
    (!project.status || ['active', 'inactive', 'development'].includes(project.status))
  );
}

/**
 * 验证用户信息配置的有效性
 */
export function validateUserInfo(user: UserInfo): boolean {
  return (
    typeof user.name === 'string' &&
    user.name.length > 0 &&
    typeof user.email === 'string' &&
    user.email.length > 0 &&
    (!user.status || ['online', 'offline', 'away'].includes(user.status))
  );
}

/**
 * 验证团队信息配置的有效性
 */
export function validateTeamInfo(team: TeamInfo): boolean {
  return typeof team.name === 'string' && team.name.length > 0;
}

/**
 * 安全地获取导航项的显示文本
 */
export function getNavItemDisplayText(item: NavItem): string {
  return item.title || '未命名导航项';
}

/**
 * 安全地获取项目的显示文本
 */
export function getProjectDisplayText(project: ProjectItem): string {
  return project.name || '未命名项目';
}

/**
 * 安全地获取用户的显示名称
 */
export function getUserDisplayName(user: UserInfo): string {
  return user.name || '未知用户';
}

/**
 * 安全地获取团队的显示名称
 */
export function getTeamDisplayName(team: TeamInfo): string {
  return team.name || '未知团队';
}

/**
 * 获取用户名的首字母（用于头像占位符）
 */
export function getUserInitials(user: UserInfo): string {
  const name = getUserDisplayName(user);
  return name.charAt(0).toUpperCase();
}

/**
 * 获取团队名的首字母（用于 Logo 占位符）
 */
export function getTeamInitials(team: TeamInfo): string {
  const name = getTeamDisplayName(team);
  return name.charAt(0).toUpperCase();
}

/**
 * 检查路径是否匹配导航项
 */
export function isPathActive(currentPath: string, targetPath: string): boolean {
  if (!currentPath || !targetPath) {
    return false;
  }

  // 移除查询参数进行比较
  const cleanCurrentPath = currentPath.split('?')[0];
  const cleanTargetPath = targetPath.split('?')[0];

  return cleanCurrentPath === cleanTargetPath;
}

/**
 * 检查导航项或其子项是否包含活跃路径
 */
export function isNavItemActive(item: NavItem, currentPath: string): boolean {
  // 检查主导航项
  if (item.url && isPathActive(currentPath, item.url)) {
    return true;
  }

  // 检查子导航项
  if (item.items) {
    return item.items.some((subItem) => isPathActive(currentPath, subItem.url));
  }

  return false;
}

/**
 * 验证完整的侧边栏配置
 */
export function validateSidebarConfig(config: SidebarConfig): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // 验证用户信息
  if (!validateUserInfo(config.user)) {
    errors.push('用户信息配置无效');
  }

  // 验证主导航
  if (!Array.isArray(config.navMain) || config.navMain.length === 0) {
    errors.push('主导航配置不能为空');
  } else {
    config.navMain.forEach((item, index) => {
      if (!validateNavItem(item)) {
        errors.push(`主导航项 ${index + 1} 配置无效`);
      }
    });
  }

  // 验证团队信息（可选）
  if (config.teams) {
    if (!Array.isArray(config.teams)) {
      errors.push('团队配置必须是数组');
    } else {
      config.teams.forEach((team, index) => {
        if (!validateTeamInfo(team)) {
          errors.push(`团队 ${index + 1} 配置无效`);
        }
      });
    }
  }

  // 验证项目信息（可选）
  if (config.projects) {
    if (!Array.isArray(config.projects)) {
      errors.push('项目配置必须是数组');
    } else {
      config.projects.forEach((project, index) => {
        if (!validateProjectItem(project)) {
          errors.push(`项目 ${index + 1} 配置无效`);
        }
      });
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * 创建安全的侧边栏配置（带默认值）
 */
export function createSafeConfig(config: Partial<SidebarConfig>): SidebarConfig {
  return {
    user: config.user || {
      name: '未知用户',
      email: '<EMAIL>',
      status: 'offline',
    },
    teams: config.teams || [],
    navMain: config.navMain || [],
    projects: config.projects || [],
    showSystemStatus: config.showSystemStatus ?? true,
    showThemeToggle: config.showThemeToggle ?? true,
  };
}
