<script lang="ts">
  import { CurrencyIcon } from '$lib/components/features/ui';
  import { Badge } from '$lib/components/ui/badge';
  import { Card, CardContent } from '$lib/components/ui/card';

  // 本地实现 getParameterValue 函数
  function getParameterValue(
    event: any,
    parameterId: string
  ): string | number | boolean | undefined {
    const parameter = event.parameters.find((p: any) => p.parameterId === parameterId);
    return parameter?.value;
  }

  import {
    type BaseEventCardProps,
    formatAmount,
    formatTimeDiff,
    formatUsdAmount,
    getDirectionText,
    getMarketTypeText,
    getStatusDisplay,
  } from '../utils';

  interface Props extends BaseEventCardProps {}

  const { event, isHighlighted = false, onCardClick }: Props = $props();

  // 提取TWAP检测相关参数
  const lastUpdated = getParameterValue(event, 'last_updated') as number;
  const expiredValue = getParameterValue(event, 'expired');
  const expired =
    expiredValue === null || expiredValue === 'null' ? null : (expiredValue as number);
  const amountUsd = (getParameterValue(event, 'amount_usd') ||
    getParameterValue(event, 'amountUsd')) as number;
  const created = getParameterValue(event, 'created') as number;
  const coin = getParameterValue(event, 'coin') as string;
  const pair = getParameterValue(event, 'pair') as string;
  const base = getParameterValue(event, 'base') as string;
  const side = getParameterValue(event, 'side') as number;
  const size = getParameterValue(event, 'size') as number;
  const market = getParameterValue(event, 'market') as number;

  // 获取方向和市场类型文本
  const sideText = getDirectionText(side, 'side');
  const marketText = getMarketTypeText(market);

  // 状态判断逻辑
  const isExpired = expired !== null;
  const isActive = !isExpired && lastUpdated;
  const status = isExpired ? 'expired' : isActive ? 'ongoing' : 'started';

  // 获取状态显示
  const statusDisplay = getStatusDisplay(status);

  // 状态对应的Badge变体
  const statusBadgeVariant =
    status === 'ongoing'
      ? 'default'
      : status === 'started'
        ? 'secondary'
        : status === 'expired'
          ? 'destructive'
          : 'outline';

  // 卡片样式 - TWAP检测 (TWAP_DETECTION): 20rem - 复杂网格布局
  const cardClasses = `
    transition-all duration-200 w-full
    min-w-80
    ${isHighlighted ? 'ring-primary border-primary/50 shadow-lg ring-2' : 'hover:shadow-md'}
    ${onCardClick ? 'cursor-pointer' : ''}
  `;

  // 处理卡片点击
  function handleCardClick() {
    onCardClick?.(event);
  }
</script>

<Card
  class={cardClasses}
  onclick={onCardClick ? handleCardClick : undefined}
  role={onCardClick ? 'button' : undefined}
  tabindex={onCardClick ? 0 : undefined}
>
  <CardContent class="p-3">
    <!-- 头部信息行 -->
    <div class="mb-3 flex items-center justify-between">
      <!-- 左侧：币种图标和基本信息 -->
      <div class="flex min-w-0 flex-1 items-center space-x-2">
        <!-- 币种图标 -->
        <CurrencyIcon
          currencyId={coin || event.currency || ''}
          symbol={base}
          size="size-6"
          class="flex-shrink-0"
        />

        <!-- 币种和交易对信息 -->
        <div class="min-w-0 flex-1">
          <div class="flex items-center space-x-2">
            <span class="text-foreground text-sm font-semibold">
              {base || '未知'}
            </span>
          </div>
          {#if pair}
            <div class="text-muted-foreground truncate text-xs">
              {pair}
            </div>
          {/if}
        </div>
      </div>

      <!-- 右侧：状态 (已移除) -->
    </div>

    <!-- 关键数据网格 -->
    <div class="grid grid-cols-2 gap-3 text-xs">
      <!-- 交易金额 (Badge样式) -->
      <div class="space-y-1">
        <div class="text-muted-foreground">交易金额</div>
        <Badge
          class="justify-center {side === 1 ? 'bg-green-600 text-white' : side === 2 ? 'bg-red-600 text-white' : 'bg-secondary text-secondary-foreground'}"
        >
          {formatUsdAmount(amountUsd)}
        </Badge>
      </div>

      <!-- 交易数量 -->
      {#if size}
        <div class="space-y-1">
          <div class="text-muted-foreground">交易数量</div>
          <div class="text-foreground font-medium">
            {formatAmount(size)}
          </div>
        </div>
      {/if}

      <!-- 开始时间 -->
      {#if created}
        <div class="space-y-1">
          <div class="text-muted-foreground">开始时间</div>
          <Badge
            class="justify-center {status === 'expired' ? 'bg-red-600 text-white' : 'bg-green-600 text-white'}"
          >
            {formatTimeDiff(Math.floor(created / 1000))}
          </Badge>
        </div>
      {/if}

      <!-- 类型 -->
      <div class="space-y-1">
        <div class="text-muted-foreground">类型</div>
        <div class="text-foreground font-medium">
          {marketText}
        </div>
      </div>
    </div>
  </CardContent>
</Card>
