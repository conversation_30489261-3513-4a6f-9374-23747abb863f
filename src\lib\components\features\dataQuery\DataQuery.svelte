<script lang="ts">
  import { onMount } from 'svelte';

  import { Badge } from '$lib/components/ui/badge';
  // shadcn-svelte 组件导入
  import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
  import { dataQueryStore } from '$lib/stores/features/dataQuery';

  import QueryForm from './QueryForm.svelte';
  import TimelineContainer from './TimelineContainer.svelte';

  // 组件挂载时加载数据
  onMount(() => {
    dataQueryStore.executeQuery();
  });
</script>

<div class="space-y-6">
  <div class="space-y-2">
    <h1 class="text-3xl font-bold tracking-tight">数据查询</h1>
    <p class="text-muted-foreground">在这里您可以根据各种条件查询和分析数据。</p>
  </div>

  <!-- 查询表单 -->
  <QueryForm expanded={$dataQueryStore.isFilterExpanded} />

  <!-- 查询结果 -->
  <Card class="relative min-h-[600px]">
    <CardHeader class="pb-4">
      <CardTitle class="flex items-center justify-between">
        <span>查询结果</span>
        {#if $dataQueryStore.results.items.length > 0}
          <Badge variant="secondary" class="text-xs">
            共 {$dataQueryStore.results.items.length} 条记录
          </Badge>
        {/if}
      </CardTitle>
    </CardHeader>
    <CardContent class="pt-0">
      {#if $dataQueryStore.results.loading}
        <div
          class="bg-background/80 absolute inset-0 z-10 flex items-center justify-center backdrop-blur-sm"
        >
          <div class="flex items-center space-x-2">
            <div
              class="border-primary h-4 w-4 animate-spin rounded-full border-2 border-t-transparent"
            ></div>
            <span class="text-muted-foreground text-sm">加载中...</span>
          </div>
        </div>
      {/if}

      <!-- 时间轴+卡片布局 -->
      <TimelineContainer
        items={$dataQueryStore.results.items}
        timelineExtendable={$dataQueryStore.timelineExtendable}
        loadingTimeline={$dataQueryStore.loadingTimeline}
        error={$dataQueryStore.error}
        onExtendTimeline={() => dataQueryStore.extendTimeline()}
      />
    </CardContent>
  </Card>
</div>
