# 日志系统文档

## 概述

本项目使用统一的日志系统，基于 Pino 库构建，支持 SvelteKit 的 SSR 和客户端环境。日志系统提供结构化日志记录、多级别日志、模块化 Logger 和环境特定配置。

## 特性

- ✅ **统一接口**: 所有模块使用相同的日志接口
- ✅ **结构化日志**: JSON 格式，便于解析和分析
- ✅ **多级别支持**: TRACE、DEBUG、INFO、WARN、ERROR、FATAL
- ✅ **环境适配**: 服务端使用 Pino，客户端使用优化的控制台输出
- ✅ **模块化**: 为不同模块提供专用 Logger
- ✅ **配置灵活**: 支持环境变量和运行时配置
- ✅ **性能优化**: 生产环境自动优化日志级别

## 快速开始

### 基本使用

```typescript
import { logger } from '$lib/utils/logger';

// 基本日志记录
logger.info('应用启动');
logger.warn('这是一个警告');
logger.error('发生错误');

// 带上下文的日志
logger.info('用户登录', { userId: 123, ip: '***********' });
logger.error('API 调用失败', { 
  endpoint: '/api/users',
  status: 500,
  error: 'Internal Server Error'
});
```

### 模块专用 Logger

```typescript
import { createModuleLogger } from '$lib/utils/logger';

// 创建模块专用 Logger
const moduleLogger = createModuleLogger('dashboard');

moduleLogger.info('仪表板初始化完成');
moduleLogger.debug('加载图表数据', { chartCount: 5 });
```

### 预定义模块 Logger

```typescript
import { 
  performanceLogger,
  apiLogger,
  storeLogger,
  themeLogger 
} from '$lib/utils/logger';

// 性能监控
performanceLogger.warn('慢操作检测', { 
  operation: 'dataLoad',
  duration: 1500 
});

// API 调用
apiLogger.info('API 请求成功', { 
  endpoint: '/api/dashboard',
  responseTime: 250 
});

// 状态管理
storeLogger.debug('状态更新', { 
  store: 'liquidation',
  action: 'refresh' 
});

// 主题切换
themeLogger.info('主题变更', { 
  from: 'light',
  to: 'dark' 
});
```

## 日志级别

| 级别 | 数值 | 用途 | 示例 |
|------|------|------|------|
| TRACE | 10 | 最详细的调试信息 | 函数进入/退出 |
| DEBUG | 20 | 开发调试信息 | 变量值、状态变化 |
| INFO | 30 | 一般信息 | 操作完成、状态报告 |
| WARN | 40 | 警告信息 | 性能问题 |
| ERROR | 50 | 错误信息 | 异常、失败操作 |
| FATAL | 60 | 致命错误 | 系统崩溃、严重故障 |

## 环境配置

### 环境变量

在 `.env` 文件中配置日志行为：

```bash
# 日志级别 (trace, debug, info, warn, error, fatal)
VITE_LOG_LEVEL=debug

# 是否启用美化打印
VITE_LOG_PRETTY_PRINT=true

# 性能监控
VITE_PERFORMANCE_MONITORING=true
VITE_PERFORMANCE_MONITORING_INTERVAL=10000
```

### 开发环境配置

```bash
# 开发环境推荐配置
VITE_LOG_LEVEL=debug
VITE_LOG_PRETTY_PRINT=true
VITE_PERFORMANCE_MONITORING=true
```

### 生产环境配置

```bash
# 生产环境推荐配置
VITE_LOG_LEVEL=warn
VITE_LOG_PRETTY_PRINT=false
VITE_PERFORMANCE_MONITORING=false
```

## 高级用法

### 创建子 Logger

```typescript
import { logger } from '$lib/utils/logger';

// 创建带有持久上下文的子 Logger
const userLogger = logger.child({ 
  userId: 123,
  sessionId: 'abc-def-ghi' 
});

// 所有日志都会包含用户上下文
userLogger.info('用户操作', { action: 'click', target: 'button' });
// 输出: { userId: 123, sessionId: 'abc-def-ghi', action: 'click', target: 'button' }
```

### 动态设置日志级别

```typescript
import { logger, LogLevel } from '$lib/utils/logger';

// 运行时调整日志级别
logger.setLevel(LogLevel.WARN);

// 检查当前级别
const currentLevel = logger.getLevel();
```

### 错误处理最佳实践

```typescript
import { apiLogger } from '$lib/utils/logger';

try {
  const response = await fetch('/api/data');
  apiLogger.info('API 请求成功', { 
    endpoint: '/api/data',
    status: response.status 
  });
} catch (error) {
  apiLogger.error('API 请求失败', {
    endpoint: '/api/data',
    error: error instanceof Error ? error.message : String(error),
    stack: error instanceof Error ? error.stack : undefined
  });
}
```

## 模块化 Logger 指南

### 推荐的模块 Logger

| 模块 | Logger | 用途 |
|------|--------|------|
| 性能监控 | `performanceLogger` | 性能指标、慢操作检测 |
| API 服务 | `apiLogger` | HTTP 请求、响应、错误 |
| 状态管理 | `storeLogger` | Store 状态变化、数据更新 |
| 主题系统 | `themeLogger` | 主题切换、样式更新 |
| 时区处理 | `timezoneLogger` | 时区转换、格式化错误 |
| 格式化 | `formattersLogger` | 数据格式化、转换错误 |
| 错误页面 | `errorLogger` | 页面错误、导航失败 |

### 创建新的模块 Logger

```typescript
import { createModuleLogger } from '$lib/utils/logger';

// 为新模块创建专用 Logger
const authLogger = createModuleLogger('auth', { 
  service: 'authentication' 
});

export { authLogger };
```

## 日志格式

### 开发环境输出格式

```
2025-06-23T03:06:45.652Z INFO[svelte-demo]: 应用启动
2025-06-23T03:06:45.671Z DEBUG[performance]: 操作完成 {"operation":"dataLoad","duration":250}
2025-06-23T03:06:45.676Z WARN[api]: API 响应慢 {"endpoint":"/api/users","responseTime":1500}
2025-06-23T03:06:45.682Z ERROR[store]: 状态更新失败 {"store":"dashboard","error":"Network timeout"}
```

### 生产环境输出格式

生产环境使用 JSON 格式，便于日志收集和分析：

```json
{"level":30,"time":"2025-06-23T03:06:45.652Z","name":"svelte-demo","msg":"应用启动"}
{"level":20,"time":"2025-06-23T03:06:45.671Z","module":"performance","msg":"操作完成","operation":"dataLoad","duration":250}
{"level":40,"time":"2025-06-23T03:06:45.676Z","module":"api","msg":"API 响应慢","endpoint":"/api/users","responseTime":1500}
{"level":50,"time":"2025-06-23T03:06:45.682Z","module":"store","msg":"状态更新失败","store":"dashboard","error":"Network timeout"}
```

## 性能考虑

### 日志级别优化

- **开发环境**: 使用 DEBUG 级别，查看详细信息
- **测试环境**: 使用 INFO 级别，关注重要操作
- **生产环境**: 使用 WARN 级别，只记录问题和错误

### 避免性能影响

```typescript
// ❌ 避免在日志中进行复杂计算
logger.debug('复杂数据', { data: expensiveCalculation() });

// ✅ 先检查日志级别
if (logger.getLevel() <= LogLevel.DEBUG) {
  logger.debug('复杂数据', { data: expensiveCalculation() });
}

// ✅ 或者使用函数延迟计算
logger.debug('复杂数据', () => ({ data: expensiveCalculation() }));
```

## 故障排除

### 常见问题

1. **日志不显示**
   - 检查日志级别设置
   - 确认环境变量配置
   - 验证 Logger 导入路径

2. **格式不正确**
   - 检查 `VITE_LOG_PRETTY_PRINT` 设置
   - 确认浏览器环境检测

3. **性能问题**
   - 调整日志级别
   - 减少上下文数据量
   - 使用条件日志记录

### 调试技巧

```typescript
// 临时启用详细日志
logger.setLevel(LogLevel.TRACE);

// 检查 Logger 配置
console.log('Logger level:', logger.getLevel());
console.log('Environment:', { browser, dev });

// 测试日志输出
logger.trace('Trace test');
logger.debug('Debug test');
logger.info('Info test');
logger.warn('Warn test');
logger.error('Error test');
```

## 迁移指南

### 从 console.* 迁移

```typescript
// 旧代码
console.log('用户登录');
console.warn('性能警告:', { duration: 1500 });
console.error('API 错误:', error);

// 新代码
import { logger } from '$lib/utils/logger';

logger.info('用户登录');
logger.warn('性能警告', { duration: 1500 });
logger.error('API 错误', { 
  error: error instanceof Error ? error.message : String(error),
  stack: error instanceof Error ? error.stack : undefined 
});
```

### 批量替换建议

1. 使用 IDE 的查找替换功能
2. 正则表达式: `console\.(log|info|warn|error|debug)`
3. 逐个文件检查和调整
4. 添加适当的上下文信息

## 最佳实践

1. **使用结构化日志**: 总是提供有意义的上下文
2. **选择合适级别**: 根据信息重要性选择日志级别
3. **模块化 Logger**: 为不同模块使用专用 Logger
4. **避免敏感信息**: 不要记录密码、令牌等敏感数据
5. **性能优先**: 在高频路径中谨慎使用日志
6. **错误处理**: 记录完整的错误信息和堆栈跟踪
7. **一致性**: 在整个项目中保持日志格式一致

## 相关文件

- `src/lib/utils/logger.ts` - 核心 Logger 实现
- `src/lib/utils/logger.config.ts` - 日志配置
- `src/lib/utils/logger.test.ts` - 单元测试
- `.env.example` - 环境变量示例
- `docs/logging.md` - 本文档
