<!-- src/lib/components/layout/AppSidebar/NavUser.svelte -->
<script lang="ts">
  // shadcn-svelte 组件导入
  import * as Avatar from '$lib/components/ui/avatar/index.js';
  import * as Sidebar from '$lib/components/ui/sidebar/index.js';

  import type { UserInfo } from './types.js';

  interface Props {
    user: UserInfo;
  }

  const { user }: Props = $props();

  /**
   * 获取用户状态指示器颜色
   */
  function getStatusColor(status?: string): string {
    switch (status) {
      case 'online':
        return 'bg-green-500';
      case 'away':
        return 'bg-yellow-500';
      case 'offline':
        return 'bg-gray-500';
      default:
        return 'bg-gray-500';
    }
  }

  /**
   * 获取用户状态文本
   */
  function getStatusText(status?: string): string {
    switch (status) {
      case 'online':
        return '在线';
      case 'away':
        return '离开';
      case 'offline':
        return '离线';
      default:
        return '未知';
    }
  }

  /**
   * 生成收起状态下的 tooltip 内容
   */
  function getTooltipContent(): string {
    const statusText = user.status ? getStatusText(user.status) : '';
    return `${user.name}\n${user.email}${statusText ? `\n状态: ${statusText}` : ''}`;
  }

  /**
   * 处理用户操作
   */
  function handleUserAction(action: string) {
    switch (action) {
      case 'profile':
        console.log('Navigate to profile');
        // 这里可以添加导航到用户资料页面的逻辑
        break;
      case 'billing':
        console.log('Navigate to billing');
        // 这里可以添加导航到账单页面的逻辑
        break;
      case 'notifications':
        console.log('Navigate to notifications');
        // 这里可以添加导航到通知页面的逻辑
        break;
      case 'logout':
        console.log('User logout');
        // 这里可以添加用户登出逻辑
        break;
      default:
        console.log('Unknown action:', action);
    }
  }
</script>

<Sidebar.Menu>
  <Sidebar.MenuItem>
    <Sidebar.MenuButton
      size="lg"
      onclick={() => handleUserAction('profile')}
      title="用户信息"
      tooltipContent={getTooltipContent()}
    >
      <!-- 收起状态下的布局：仅显示头像和状态指示器 -->
      <div
        class="hidden group-data-[collapsible=icon]:flex group-data-[collapsible=icon]:h-full group-data-[collapsible=icon]:w-full group-data-[collapsible=icon]:items-center group-data-[collapsible=icon]:justify-center"
      >
        <!-- 头像容器，使用相对定位 -->
        <div class="relative">
          <!-- 用户头像 -->
          <Avatar.Root class="h-6 w-6 rounded-lg">
            <Avatar.Image src={user.avatar} alt={user.name} />
            <Avatar.Fallback class="bg-sidebar-primary/10 text-sidebar-primary rounded-lg text-xs">
              {user.name.charAt(0).toUpperCase()}
            </Avatar.Fallback>
          </Avatar.Root>

          <!-- 收起状态下的状态指示器：显示在头像右下角 -->
          {#if user.status}
            <div
              class="border-sidebar-background absolute -right-0.5 -bottom-0.5 h-2.5 w-2.5 rounded-full border-2 {getStatusColor(
                user.status
              )} z-10"
              title={getStatusText(user.status)}
            ></div>
          {/if}
        </div>
      </div>

      <!-- 展开状态下的布局：显示完整信息 -->
      <div class="flex items-center gap-2 group-data-[collapsible=icon]:hidden">
        <!-- 用户头像 -->
        <Avatar.Root class="h-8 w-8 rounded-lg">
          <Avatar.Image src={user.avatar} alt={user.name} />
          <Avatar.Fallback class="bg-sidebar-primary/10 text-sidebar-primary rounded-lg">
            {user.name.charAt(0).toUpperCase()}
          </Avatar.Fallback>
        </Avatar.Root>

        <!-- 用户信息 -->
        <div class="grid flex-1 text-left text-sm leading-tight">
          <span class="truncate font-semibold">{user.name}</span>
          <span class="text-sidebar-muted-foreground truncate text-xs">
            {user.email}
          </span>
        </div>

        <!-- 展开状态下的状态指示器 -->
        {#if user.status}
          <div
            class="h-2 w-2 rounded-full {getStatusColor(user.status)}"
            title={getStatusText(user.status)}
          ></div>
        {/if}
      </div>
    </Sidebar.MenuButton>
  </Sidebar.MenuItem>
</Sidebar.Menu>
