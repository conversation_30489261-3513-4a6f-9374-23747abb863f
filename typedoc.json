{"$schema": "https://typedoc.org/schema.json", "name": "Svelte Demo API Documentation", "entryPoints": ["src/lib/services/index.ts", "src/lib/stores/index.ts", "src/lib/types/index.ts", "src/lib/constants/index.ts"], "out": "docs/api", "plugin": ["typedoc-plugin-markdown"], "theme": "markdown", "readme": "none", "excludePrivate": true, "excludeProtected": true, "excludeInternal": true, "hideGenerator": true, "categorizeByGroup": true, "defaultCategory": "Other", "categoryOrder": ["Services", "Stores", "Types", "Constants", "*"], "validation": {"notExported": false, "invalidLink": false, "notDocumented": false}, "treatWarningsAsErrors": false, "logLevel": "Info"}