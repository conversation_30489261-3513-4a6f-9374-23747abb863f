<script lang="ts">
  import { SystemStatus } from '$lib/components/features';
  import { Button } from '$lib/components/ui/button';
  import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
  } from '$lib/components/ui/card';
  import { Progress } from '$lib/components/ui/progress';
  import { ApiError } from '$lib/services/api/base';
  import { networkStatus } from '$lib/utils/networkMonitor';
  import { Wrench } from '@lucide/svelte';
  import { goto, invalidateAll } from '$app/navigation';

  // Props
  export let error: Error | ApiError;
  export let autoRecovery = true;
  export let maxRetries = 3;
  export let retryDelay = 2000;

  // Events - Svelte 5 style
  export let onRecovered: () => void = () => {};
  export let onFailed: () => void = () => {};
  export let onRetry: (attempt: number) => void = () => {};

  // 恢复状态
  let isRecovering = false;
  let currentAttempt = 0;
  let recoveryProgress = 0;
  let recoveryMessage = '';
  let recoveryTimer: NodeJS.Timeout | null = null;

  // 恢复策略
  $: recoveryStrategies = getRecoveryStrategies(error);

  interface RecoveryStrategy {
    name: string;
    description: string;
    action: () => Promise<boolean>;
    icon: string;
    priority: number;
  }

  function getRecoveryStrategies(err: Error | ApiError): RecoveryStrategy[] {
    const strategies: RecoveryStrategy[] = [];

    if (err instanceof ApiError) {
      if (err.isNetworkError()) {
        strategies.push({
          name: '网络连接检测',
          description: '检查网络连接状态',
          action: checkNetworkConnection,
          icon: '🌐',
          priority: 1,
        });
      }

      if (err.isTimeoutError()) {
        strategies.push({
          name: '重新请求',
          description: '使用更长的超时时间重试',
          action: retryWithLongerTimeout,
          icon: '⏱️',
          priority: 2,
        });
      }

      if (err.statusCode === 401) {
        strategies.push({
          name: '刷新认证',
          description: '尝试刷新用户认证状态',
          action: refreshAuthentication,
          icon: '🔐',
          priority: 1,
        });
      }

      if (err.statusCode === 429) {
        strategies.push({
          name: '等待重试',
          description: '等待一段时间后重试',
          action: waitAndRetry,
          icon: '⏳',
          priority: 3,
        });
      }

      if (err.isServerError()) {
        strategies.push({
          name: '服务状态检查',
          description: '检查服务器状态',
          action: checkServerStatus,
          icon: '🔧',
          priority: 2,
        });
      }
    }

    // 通用恢复策略
    strategies.push({
      name: '页面刷新',
      description: '刷新页面重新加载',
      action: refreshPage,
      icon: '🔄',
      priority: 4,
    });

    strategies.push({
      name: '清除缓存',
      description: '清除浏览器缓存',
      action: clearCache,
      icon: '🧹',
      priority: 5,
    });

    return strategies.sort((a, b) => a.priority - b.priority);
  }

  // 恢复策略实现
  async function checkNetworkConnection(): Promise<boolean> {
    recoveryMessage = '正在检查网络连接...';

    try {
      const response = await fetch('/api/health', {
        method: 'HEAD',
        signal: AbortSignal.timeout(5000),
      });
      return response.ok;
    } catch {
      return false;
    }
  }

  async function retryWithLongerTimeout(): Promise<boolean> {
    recoveryMessage = '使用更长超时时间重试...';

    // 这里应该调用原始请求，但使用更长的超时时间
    // 由于我们不知道原始请求，这里模拟一个延迟
    await new Promise((resolve) => setTimeout(resolve, 1000));
    return Math.random() > 0.3; // 70% 成功率
  }

  async function refreshAuthentication(): Promise<boolean> {
    recoveryMessage = '正在刷新认证状态...';

    try {
      // 这里应该调用认证刷新 API
      const response = await fetch('/api/auth/refresh', { method: 'POST' });
      return response.ok;
    } catch {
      return false;
    }
  }

  async function waitAndRetry(): Promise<boolean> {
    recoveryMessage = '等待后重试...';

    // 等待一段时间
    await new Promise((resolve) => setTimeout(resolve, retryDelay));
    return true; // 等待本身总是成功的
  }

  async function checkServerStatus(): Promise<boolean> {
    recoveryMessage = '正在检查服务器状态...';

    try {
      const response = await fetch('/api/status', {
        method: 'GET',
        signal: AbortSignal.timeout(5000),
      });
      return response.ok;
    } catch {
      return false;
    }
  }

  async function refreshPage(): Promise<boolean> {
    recoveryMessage = '正在重新加载数据...';
    
    try {
      // 使用 SvelteKit 导航，避免页面刷新
      await invalidateAll(); // 重新加载所有数据
      return true;
    } catch {
      // 备用方案：导航到当前页面
      try {
        await goto(window.location.pathname, { 
          replaceState: true,
          invalidateAll: true 
        });
        return true;
      } catch {
        return false;
      }
    }
  }

  async function clearCache(): Promise<boolean> {
    recoveryMessage = '正在清除缓存...';

    try {
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(cacheNames.map((name) => caches.delete(name)));
      }

      // 清除 localStorage
      localStorage.clear();

      return true;
    } catch {
      return false;
    }
  }

  // 开始自动恢复
  async function startAutoRecovery(): Promise<void> {
    if (isRecovering || currentAttempt >= maxRetries) {
      return;
    }

    isRecovering = true;
    currentAttempt++;
    recoveryProgress = 0;

    onRetry(currentAttempt);

    try {
      for (let i = 0; i < recoveryStrategies.length; i++) {
        const strategy = recoveryStrategies[i];
        recoveryMessage = strategy.description;
        recoveryProgress = ((i + 1) / recoveryStrategies.length) * 100;

        const success = await strategy.action();

        if (success) {
          recoveryMessage = '恢复成功！';
          recoveryProgress = 100;

          // 等待一下让用户看到成功消息
          await new Promise((resolve) => setTimeout(resolve, 1000));

          onRecovered();
          return;
        }

        // 策略之间的短暂延迟
        await new Promise((resolve) => setTimeout(resolve, 500));
      }

      // 所有策略都失败了
      if (currentAttempt < maxRetries) {
        recoveryMessage = `第 ${currentAttempt} 次尝试失败，${retryDelay / 1000} 秒后重试...`;

        recoveryTimer = setTimeout(() => {
          startAutoRecovery();
        }, retryDelay);
      } else {
        recoveryMessage = '自动恢复失败';
        onFailed();
      }
    } catch (error) {
      recoveryMessage = '恢复过程中出现错误';
      onFailed();
    } finally {
      isRecovering = false;
    }
  }

  // 手动重试
  function handleManualRetry(): void {
    currentAttempt = 0;
    startAutoRecovery();
  }

  // 停止恢复
  function stopRecovery(): void {
    if (recoveryTimer) {
      clearTimeout(recoveryTimer);
      recoveryTimer = null;
    }
    isRecovering = false;
    recoveryMessage = '';
    recoveryProgress = 0;
  }

  // 自动开始恢复（如果启用）
  $: if (autoRecovery && !isRecovering && currentAttempt === 0) {
    startAutoRecovery();
  }
</script>

<Card class="w-full max-w-md">
  <CardHeader>
    <CardTitle class="flex items-center gap-2">
      <Wrench class="h-5 w-5 animate-spin" />
      错误恢复助手
    </CardTitle>
    <CardDescription>正在尝试自动修复问题...</CardDescription>
  </CardHeader>

  <CardContent class="space-y-4">
    <!-- 恢复进度 -->
    {#if isRecovering}
      <div class="space-y-2">
        <div class="flex justify-between text-sm">
          <span>恢复进度</span>
          <span>{Math.round(recoveryProgress)}%</span>
        </div>
        <Progress value={recoveryProgress} class="h-2" />
        <p class="text-muted-foreground text-sm">{recoveryMessage}</p>
      </div>
    {/if}

    <!-- 尝试次数 -->
    <div class="flex justify-between text-sm">
      <span>尝试次数:</span>
      <span>{currentAttempt} / {maxRetries}</span>
    </div>

    <!-- 网络状态 -->
    <SystemStatus />

    <!-- 恢复策略列表 -->
    <div class="space-y-2">
      <h4 class="text-sm font-medium">恢复策略:</h4>
      <div class="space-y-1">
        {#each recoveryStrategies as strategy, index}
          <div class="flex items-center gap-2 text-sm">
            <span class="text-lg">{strategy.icon}</span>
            <span class="flex-1">{strategy.name}</span>
            {#if index < (recoveryProgress / 100) * recoveryStrategies.length}
              <span class="text-green-500">✓</span>
            {:else if index === Math.floor((recoveryProgress / 100) * recoveryStrategies.length) && isRecovering}
              <div
                class="border-primary h-3 w-3 animate-spin rounded-full border-2 border-t-transparent"
              ></div>
            {:else}
              <span class="text-muted-foreground">○</span>
            {/if}
          </div>
        {/each}
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex gap-2">
      {#if !isRecovering}
        <Button
          onclick={handleManualRetry}
          variant="default"
          size="sm"
          disabled={currentAttempt >= maxRetries}
        >
          {currentAttempt >= maxRetries ? '已达最大重试次数' : '手动重试'}
        </Button>
      {:else}
        <Button onclick={stopRecovery} variant="outline" size="sm">停止恢复</Button>
      {/if}
    </div>
  </CardContent>
</Card>



