/**
 * 布局相关类型定义
 *
 * @category UI Types
 */

/**
 * 仪表板统计数据
 */
export interface DashboardStats {
  /** 总用户数 */
  totalUsers: number;
  /** 月收入（单位：元或美元） */
  monthlyRevenue: number;
  /** 转化率（百分比） */
  conversionRate: number;
  /** 活跃用户数 */
  activeUsers: number;
}

/**
 * 导航菜单项
 */
export interface NavigationItem {
  /** 菜单ID */
  id: string;
  /** 菜单标题 */
  title: string;
  /** 菜单图标 */
  icon?: string;
  /** 路由路径 */
  href?: string;
  /** 子菜单 */
  children?: NavigationItem[];
  /** 是否激活 */
  active?: boolean;
  /** 是否禁用 */
  disabled?: boolean;
  /** 徽章文本 */
  badge?: string;
  /** 徽章类型 */
  badgeType?: 'default' | 'primary' | 'success' | 'warning' | 'error';
}

/**
 * 侧边栏状态
 */
export interface SidebarState {
  /** 是否展开 */
  isExpanded: boolean;
  /** 是否固定 */
  isPinned: boolean;
  /** 当前激活的菜单项 */
  activeItem: string | null;
  /** 展开的菜单项 */
  expandedItems: Set<string>;
}

/**
 * 头部配置
 */
export interface HeaderConfig {
  /** 标题 */
  title: string;
  /** 副标题 */
  subtitle?: string;
  /** 是否显示侧边栏切换按钮 */
  showSidebarToggle?: boolean;
  /** 是否显示用户菜单 */
  showUserMenu?: boolean;
  /** 是否显示通知 */
  showNotifications?: boolean;
  /** 是否显示搜索 */
  showSearch?: boolean;
}

/**
 * 页面布局类型
 */
export type PageLayout =
  | 'default' // 默认布局（侧边栏 + 主内容）
  | 'fullwidth' // 全宽布局
  | 'centered' // 居中布局
  | 'split' // 分栏布局
  | 'dashboard'; // 仪表板布局

/**
 * 响应式断点
 */
export type Breakpoint = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';

/**
 * 布局配置
 */
export interface LayoutConfig {
  /** 页面布局类型 */
  layout: PageLayout;
  /** 侧边栏配置 */
  sidebar?: {
    /** 是否显示 */
    show: boolean;
    /** 默认是否展开 */
    defaultExpanded: boolean;
    /** 是否可折叠 */
    collapsible: boolean;
    /** 宽度 */
    width: string;
    /** 折叠后宽度 */
    collapsedWidth: string;
  };
  /** 头部配置 */
  header?: HeaderConfig;
  /** 是否显示页脚 */
  showFooter?: boolean;
  /** 内容区域内边距 */
  contentPadding?: string;
  /** 最大内容宽度 */
  maxContentWidth?: string;
}

/**
 * 标签页配置
 */
export interface TabConfig {
  /** 标签ID */
  id: string;
  /** 标签标题 */
  title: string;
  /** 标签图标 */
  icon?: string;
  /** 路由路径 */
  href?: string;
  /** 是否可关闭 */
  closable?: boolean;
  /** 是否禁用 */
  disabled?: boolean;
  /** 徽章文本 */
  badge?: string;
}

/**
 * 标签栏状态
 */
export interface TabBarState {
  /** 当前激活的标签 */
  activeTab: string;
  /** 标签列表 */
  tabs: TabConfig[];
  /** 是否显示添加按钮 */
  showAddButton: boolean;
}

/**
 * 面包屑项
 */
export interface BreadcrumbItem {
  /** 项目标题 */
  title: string;
  /** 链接地址 */
  href?: string;
  /** 是否为当前页 */
  current?: boolean;
}

/**
 * 通知项
 */
export interface NotificationItem {
  /** 通知ID */
  id: string;
  /** 通知标题 */
  title: string;
  /** 通知内容 */
  message: string;
  /** 通知类型 */
  type: 'info' | 'success' | 'warning' | 'error';
  /** 创建时间 */
  timestamp: number;
  /** 是否已读 */
  read: boolean;
  /** 是否可关闭 */
  closable?: boolean;
  /** 自动关闭时间（毫秒，0表示不自动关闭） */
  autoClose?: number;
  /** 点击回调 */
  onClick?: () => void;
}

/**
 * 通知状态
 */
export interface NotificationState {
  /** 通知列表 */
  items: NotificationItem[];
  /** 未读数量 */
  unreadCount: number;
  /** 是否显示通知面板 */
  showPanel: boolean;
  /** 最大显示数量 */
  maxItems: number;
}

/**
 * 用户菜单项
 */
export interface UserMenuItem {
  /** 菜单ID */
  id: string;
  /** 菜单标题 */
  title: string;
  /** 菜单图标 */
  icon?: string;
  /** 链接地址 */
  href?: string;
  /** 点击回调 */
  onClick?: () => void;
  /** 是否为分隔符 */
  divider?: boolean;
}

/**
 * 用户信息
 */
export interface UserInfo {
  /** 用户ID */
  id: string;
  /** 用户名 */
  username: string;
  /** 显示名称 */
  displayName: string;
  /** 头像URL */
  avatar?: string;
  /** 邮箱 */
  email?: string;
  /** 角色 */
  role?: string;
}
