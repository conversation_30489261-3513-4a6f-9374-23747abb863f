/**
 * 全局通知系统 - 基于 Sonner Toast
 *
 * @category Stores
 */

import { writable } from 'svelte/store';
import { toast } from 'svelte-sonner';

export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number; // 自动消失时间（毫秒），0表示不自动消失
  timestamp: number;
}

function createNotificationStore() {
  // 创建一个空的 store 以保持向后兼容性
  const { subscribe } = writable<Notification[]>([]);

  const store = {
    subscribe,
    /**
     * 添加通知 - 使用 Sonner Toast
     */
    add: (notification: Omit<Notification, 'id' | 'timestamp'>) => {
      const id = Math.random().toString(36).substr(2, 9);
      const duration = notification.duration ?? 5000; // 默认5秒

      // 使用 Sonner 显示通知
      const toastOptions = {
        duration: duration > 0 ? duration : Infinity,
        description: notification.message,
      };

      switch (notification.type) {
        case 'success':
          toast.success(notification.title, toastOptions);
          break;
        case 'error':
          toast.error(notification.title, toastOptions);
          break;
        case 'warning':
          toast.warning(notification.title, toastOptions);
          break;
        case 'info':
        default:
          toast.info(notification.title, toastOptions);
          break;
      }

      return id;
    },

    /**
     * 移除通知 - Sonner 自动管理
     */
    remove: (id: string) => {
      // Sonner 自动管理通知的移除，这里保持接口兼容性
      toast.dismiss();
    },

    /**
     * 清空所有通知
     */
    clear: () => {
      toast.dismiss();
    },

    /**
     * 便捷方法：成功通知
     */
    success: (title: string, message?: string, duration?: number) => {
      return store.add({ type: 'success', title, message, duration });
    },

    /**
     * 便捷方法：错误通知
     */
    error: (title: string, message?: string, duration?: number) => {
      return store.add({ type: 'error', title, message, duration: duration ?? 0 });
    },

    /**
     * 便捷方法：警告通知
     */
    warning: (title: string, message?: string, duration?: number) => {
      return store.add({ type: 'warning', title, message, duration });
    },

    /**
     * 便捷方法：信息通知
     */
    info: (title: string, message?: string, duration?: number) => {
      return store.add({ type: 'info', title, message, duration });
    },
  };

  return store;
}

export const notifications = createNotificationStore();

// 便捷函数 - 保持向后兼容性
export const {
  add: addNotification,
  remove: removeNotification,
  clear: clearNotifications,
} = notifications;

// 直接导出 Sonner toast 函数供高级用法
export { toast } from 'svelte-sonner';
