/**
 * 图表辅助工具函数
 *
 * 提供图表组件的公共逻辑和数据处理功能
 *
 * @category Utils
 */

import type { EChartsOption } from 'echarts';

import { getChartColorMap, getDefaultColors } from './chartColors';
import { formatDate, formatNumber } from './formatters';

/**
 * 通用图表配置接口
 */
export interface BaseChartConfig {
  title?: string;
  subtitle?: string;
  showLegend?: boolean;
  showTooltip?: boolean;
  showGrid?: boolean;
  enableZoom?: boolean;
  enableBrush?: boolean;
  animation?: boolean;
}

/**
 * 时间序列数据接口
 */
export interface TimeSeriesData {
  datetime: string;
  [key: string]: any;
}

/**
 * 图表系列数据接口
 */
export interface ChartSeries {
  name: string;
  data: any[];
  type?: string;
  color?: string;
}

/**
 * 处理后的多系列数据
 */
export interface ProcessedMultiSeriesData {
  times: string[];
  series: ChartSeries[];
}

/**
 * 生成基础图表配置
 */
export function createBaseChartConfig(config: BaseChartConfig = {}): EChartsOption {
  const {
    title = '',
    subtitle = '',
    showLegend = true,
    showTooltip = true,
    showGrid = true,
    enableZoom = false,
    enableBrush = false,
    animation = true,
  } = config;

  const baseConfig: EChartsOption = {
    title: {
      text: title,
      subtext: subtitle,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
      },
      subtextStyle: {
        fontSize: 12,
        color: '#666',
      },
    },
    animation,
  };

  if (showTooltip) {
    baseConfig.tooltip = {
      trigger: 'axis',
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      borderColor: '#333',
      borderWidth: 1,
      textStyle: {
        color: '#fff',
        fontSize: 12,
      },
      formatter: function (params: any) {
        if (Array.isArray(params)) {
          let result = `${params[0].name}<br/>`;
          params.forEach((param: any) => {
            const value =
              typeof param.value === 'number' ? formatNumber(param.value, 2) : param.value;
            result += `${param.marker} ${param.seriesName}: ${value}<br/>`;
          });
          return result;
        }
        return `${params.name}: ${params.value}`;
      },
    };
  }

  if (showLegend) {
    baseConfig.legend = {
      bottom: '0%',
      left: 'center',
      textStyle: {
        fontSize: 12,
      },
      itemGap: 20,
    };
  }

  if (showGrid) {
    baseConfig.grid = {
      left: '3%',
      right: '4%',
      bottom: showLegend ? '8%' : '3%',
      top: title ? '15%' : '3%',
      containLabel: true,
    };
  }

  if (enableZoom) {
    baseConfig.dataZoom = [
      {
        type: 'inside',
        start: 0,
        end: 100,
      },
      {
        type: 'slider',
        start: 0,
        end: 100,
        height: 20,
        bottom: 10,
      },
    ];
  }

  if (enableBrush) {
    baseConfig.brush = {
      toolbox: ['rect', 'polygon', 'lineX', 'lineY', 'keep', 'clear'],
      xAxisIndex: 0,
    };
  }

  return baseConfig;
}

/**
 * 创建轴配置
 */
export function createAxisConfig(type: 'category' | 'value' | 'time', options: any = {}): any {
  const baseAxis = {
    type,
    axisLine: {
      lineStyle: {
        color: '#e0e0e0',
      },
    },
    axisTick: {
      lineStyle: {
        color: '#e0e0e0',
      },
    },
    axisLabel: {
      color: '#666',
      fontSize: 11,
    },
    splitLine: {
      lineStyle: {
        color: '#f0f0f0',
        type: 'dashed',
      },
    },
    ...options,
  };

  if (type === 'value') {
    baseAxis.axisLabel.formatter = function (value: number) {
      if (value >= 1000000) {
        return (value / 1000000).toFixed(1) + 'M';
      } else if (value >= 1000) {
        return (value / 1000).toFixed(0) + 'K';
      }
      return value.toString();
    };
  }

  if (type === 'time') {
    baseAxis.axisLabel.formatter = function (value: any) {
      return formatDate(new Date(value), 'datetime');
    };
  }

  return baseAxis;
}

/**
 * 根据时间粒度生成时间键
 */
export function getTimeKey(datetime: string, timeFrame: string): string {
  const date = new Date(datetime);

  switch (timeFrame) {
    case '1m':
      return date.toISOString().slice(0, 16); // YYYY-MM-DDTHH:MM
    case '5m':
      const minutes5 = Math.floor(date.getMinutes() / 5) * 5;
      return `${date.toISOString().slice(0, 14)}${minutes5.toString().padStart(2, '0')}`;
    case '15m':
      const minutes15 = Math.floor(date.getMinutes() / 15) * 15;
      return `${date.toISOString().slice(0, 14)}${minutes15.toString().padStart(2, '0')}`;
    case '1h':
      return date.toISOString().slice(0, 13); // YYYY-MM-DDTHH
    case '4h':
      const hours4 = Math.floor(date.getHours() / 4) * 4;
      return `${date.toISOString().slice(0, 11)}${hours4.toString().padStart(2, '0')}`;
    case '1d':
      return date.toISOString().slice(0, 10); // YYYY-MM-DD
    case '1w':
      const startOfWeek = new Date(date);
      startOfWeek.setDate(date.getDate() - date.getDay());
      return startOfWeek.toISOString().slice(0, 10);
    case '1M':
      return date.toISOString().slice(0, 7); // YYYY-MM
    default:
      return date.toISOString().slice(0, 10);
  }
}

/**
 * 处理多系列时间序列数据
 */
export function processMultiSeriesData<T extends TimeSeriesData>(
  data: T[],
  groupBy: string,
  timeFrame: string = '1d'
): ProcessedMultiSeriesData {
  if (!data || data.length === 0) {
    return { times: [], series: [] };
  }

  // 按时间和分组字段聚合数据
  const groupedData: { [timeKey: string]: { [groupKey: string]: number } } = {};
  const groupKeys = new Set<string>();

  data.forEach((item) => {
    const timeKey = getTimeKey(item.datetime, timeFrame);
    const groupKey = item[groupBy] || 'Unknown';

    groupKeys.add(groupKey);

    if (!groupedData[timeKey]) {
      groupedData[timeKey] = {};
    }

    if (!groupedData[timeKey][groupKey]) {
      groupedData[timeKey][groupKey] = 0;
    }

    // 假设有一个 amount 字段用于聚合
    groupedData[timeKey][groupKey] += item.amount || item.value || 1;
  });

  // 生成时间序列
  const times = Object.keys(groupedData).sort();

  // 生成系列数据
  const colorMap = getChartColorMap();
  const defaultColors = getDefaultColors();

  const series: ChartSeries[] = Array.from(groupKeys).map((groupKey, index) => ({
    name: groupKey,
    data: times.map((time) => groupedData[time][groupKey] || 0),
    color: colorMap[groupKey] || defaultColors[index % defaultColors.length],
  }));

  return { times, series };
}

/**
 * 处理饼图数据
 */
export function processPieChartData(
  data: any[],
  nameField: string = 'name',
  valueField: string = 'value'
): Array<{ name: string; value: number }> {
  if (!data || data.length === 0) {
    return [];
  }

  return data.map((item) => ({
    name: item[nameField] || 'Unknown',
    value: Number(item[valueField]) || 0,
  }));
}

/**
 * 处理散点图数据
 */
export function processScatterChartData(
  data: any[],
  xField: string = 'x',
  yField: string = 'y',
  nameField: string = 'name'
): Array<{ name: string; value: [number, number] }> {
  if (!data || data.length === 0) {
    return [];
  }

  return data.map((item) => ({
    name: item[nameField] || 'Point',
    value: [Number(item[xField]) || 0, Number(item[yField]) || 0],
  }));
}

/**
 * 计算百分比堆叠数据
 */
export function calculatePercentageStack(series: ChartSeries[]): ChartSeries[] {
  if (!series || series.length === 0) {
    return [];
  }

  const dataLength = series[0].data.length;

  // 计算每个时间点的总和
  const totals: number[] = [];
  for (let i = 0; i < dataLength; i++) {
    totals[i] = series.reduce((sum, s) => sum + (Number(s.data[i]) || 0), 0);
  }

  // 转换为百分比
  return series.map((s) => ({
    ...s,
    data: s.data.map((value, index) => {
      const total = totals[index];
      return total > 0 ? ((Number(value) || 0) / total) * 100 : 0;
    }),
  }));
}

/**
 * 生成图表颜色数组
 */
export function generateChartColors(count: number): string[] {
  const colorMap = getChartColorMap();
  const defaultColors = getDefaultColors();
  const colors: string[] = [];

  // 优先使用预定义的颜色映射
  const predefinedColors = Object.values(colorMap);

  for (let i = 0; i < count; i++) {
    if (i < predefinedColors.length) {
      colors.push(predefinedColors[i]);
    } else {
      colors.push(defaultColors[i % defaultColors.length]);
    }
  }

  return colors;
}

/**
 * 创建响应式图表配置
 */
export function createResponsiveConfig(baseConfig: EChartsOption): EChartsOption {
  return {
    ...baseConfig,
    media: [
      {
        query: { maxWidth: 768 },
        option: {
          title: {
            textStyle: { fontSize: 14 },
            subtextStyle: { fontSize: 10 },
          },
          legend: {
            textStyle: { fontSize: 10 },
            itemGap: 10,
          },
          grid: {
            left: '5%',
            right: '5%',
            bottom: '10%',
            top: '20%',
          },
        },
      },
      {
        query: { minWidth: 769 },
        option: baseConfig,
      },
    ],
  };
}

/**
 * 验证图表数据
 */
export function validateChartData(data: any, chartType: string): boolean {
  if (!data || (Array.isArray(data) && data.length === 0)) {
    console.warn(`${chartType} chart: No data provided`);
    return false;
  }

  if (!Array.isArray(data)) {
    console.warn(`${chartType} chart: Data must be an array`);
    return false;
  }

  return true;
}

/**
 * 深度合并图表配置
 */
export function mergeChartConfigs(base: EChartsOption, override: EChartsOption): EChartsOption {
  const merged = { ...base };

  Object.keys(override).forEach((key) => {
    const baseValue = base[key as keyof EChartsOption];
    const overrideValue = override[key as keyof EChartsOption];

    if (
      typeof overrideValue === 'object' &&
      overrideValue !== null &&
      !Array.isArray(overrideValue)
    ) {
      merged[key as keyof EChartsOption] = {
        ...(baseValue as object),
        ...(overrideValue as object),
      } as any;
    } else {
      merged[key as keyof EChartsOption] = overrideValue as any;
    }
  });

  return merged;
}
