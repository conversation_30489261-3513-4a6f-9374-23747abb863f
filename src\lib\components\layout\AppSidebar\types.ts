// src/lib/components/layout/AppSidebar/types.ts

import type { Component } from 'svelte';

/**
 * 侧边栏导航子项接口
 */
export interface NavSubItem {
  /** 子项标题 */
  title: string;
  /** 子项路由 */
  url: string;
  /** 是否为当前活跃页面 */
  isActive?: boolean;
  /** 子项描述 */
  description?: string;
}

/**
 * 侧边栏主导航项接口
 */
export interface NavItem {
  /** 导航项标题 */
  title: string;
  /** 导航项路由（可选，用于父级菜单） */
  url?: string;
  /** 导航项图标组件 */
  icon?: Component;
  /** 是否为当前活跃页面 */
  isActive?: boolean;
  /** 徽章内容（数字或文本） */
  badge?: string | number;
  /** 子导航项列表 */
  items?: NavSubItem[];
  /** 是否默认展开（用于有子项的导航） */
  defaultOpen?: boolean;
}

/**
 * 项目快捷访问项接口
 */
export interface ProjectItem {
  /** 项目名称 */
  name: string;
  /** 项目路由 */
  url: string;
  /** 项目图标组件 */
  icon?: Component;
  /** 项目描述 */
  description?: string;
  /** 项目状态 */
  status?: 'active' | 'inactive' | 'development';
}

/**
 * 用户信息接口
 */
export interface UserInfo {
  /** 用户姓名 */
  name: string;
  /** 用户邮箱 */
  email: string;
  /** 用户头像 URL */
  avatar?: string;
  /** 用户角色 */
  role?: string;
  /** 用户状态 */
  status?: 'online' | 'offline' | 'away';
}

/**
 * 团队信息接口
 */
export interface TeamInfo {
  /** 团队名称 */
  name: string;
  /** 团队 Logo 组件 */
  logo?: Component;
  /** 团队计划类型 */
  plan?: string;
  /** 团队描述 */
  description?: string;
}

/**
 * 侧边栏配置接口
 */
export interface SidebarConfig {
  /** 用户信息 */
  user: UserInfo;
  /** 团队列表 */
  teams?: TeamInfo[];
  /** 主导航项列表 */
  navMain: NavItem[];
  /** 项目快捷访问列表 */
  projects?: ProjectItem[];
  /** 是否显示系统状态 */
  showSystemStatus?: boolean;
  /** 是否显示主题切换器 */
  showThemeToggle?: boolean;
}

/**
 * 标签页信息接口（与现有 TabBar 系统兼容）
 */
export interface TabInfo {
  /** 标签页路由 */
  href: string;
  /** 标签页标题 */
  label: string;
  /** 标签页图标组件 */
  icon?: Component;
}

/**
 * 导航事件处理器类型
 */
export type NavigationHandler = (url: string) => Promise<void> | void;

/**
 * 用户操作类型
 */
export type UserAction = 'profile' | 'billing' | 'notifications' | 'logout';

/**
 * 用户操作事件处理器类型
 */
export type UserActionHandler = (action: UserAction) => void;

/**
 * 团队切换事件处理器类型
 */
export type TeamSwitchHandler = (team: TeamInfo) => void;

/**
 * 项目操作事件处理器类型
 */
export type ProjectActionHandler = (project: ProjectItem, action: string) => void;

/**
 * 侧边栏状态类型
 */
export type SidebarState = 'expanded' | 'collapsed' | 'icon';

/**
 * 侧边栏主题类型
 */
export type SidebarTheme = 'default' | 'inset' | 'sidebar';

/**
 * 组件 Props 类型定义
 */
export interface AppSidebarProps {
  /** 侧边栏配置 */
  config?: SidebarConfig;
  /** 自定义类名 */
  class?: string;
}

export interface NavMainProps {
  /** 导航项列表 */
  items: NavItem[];
  /** 导航事件处理器 */
  onNavigate?: NavigationHandler;
}

export interface NavProjectsProps {
  /** 项目列表 */
  projects: ProjectItem[];
  /** 项目导航事件处理器 */
  onNavigate?: NavigationHandler;
  /** 项目操作事件处理器 */
  onAction?: ProjectActionHandler;
}

export interface NavUserProps {
  /** 用户信息 */
  user: UserInfo;
  /** 用户操作事件处理器 */
  onAction?: UserActionHandler;
}

export interface TeamSwitcherProps {
  /** 团队列表 */
  teams: TeamInfo[];
  /** 当前活跃团队 */
  activeTeam?: TeamInfo;
  /** 团队切换事件处理器 */
  onSwitch?: TeamSwitchHandler;
  /** 添加团队事件处理器 */
  onAddTeam?: () => void;
}
