import { createModuleLogger } from './logger';

const formattersLogger = createModuleLogger('formatters');

/**
 * 格式化数字为易读格式，带千位分隔符和小数点
 * @param value 要格式化的数值
 * @param decimals 小数位数
 * @returns 格式化后的字符串
 */
export function formatNumber(value: number, decimals: number = 2): string {
  if (value === null || value === undefined || isNaN(value)) {
    return '0';
  }

  // 四舍五入到指定小数位
  const roundedValue =
    decimals === 0
      ? Math.round(value)
      : Math.round(value * Math.pow(10, decimals)) / Math.pow(10, decimals);

  // 转换为千位分隔的格式
  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(roundedValue);
}

/**
 * 格式化货币金额，自动选择合适的单位
 * @param value 要格式化的金额
 * @param currency 货币符号，默认为 '$'
 * @returns 格式化后的货币字符串
 */
export function formatCurrency(value: number, currency: string = '$'): string {
  if (value === null || value === undefined || isNaN(value)) {
    return `${currency}0`;
  }

  const absValue = Math.abs(value);
  const sign = value < 0 ? '-' : '';

  if (absValue >= 1000000000) {
    return `${sign}${currency}${formatNumber(absValue / 1000000000, 2)}B`;
  } else if (absValue >= 1000000) {
    return `${sign}${currency}${formatNumber(absValue / 1000000, 2)}M`;
  } else if (absValue >= 1000) {
    return `${sign}${currency}${formatNumber(absValue / 1000, 2)}K`;
  } else {
    return `${sign}${currency}${formatNumber(absValue, 2)}`;
  }
}

/**
 * 格式化字节大小为易读格式
 * @param bytes 字节数
 * @param decimals 小数位数，默认为 2
 * @returns 格式化后的字符串
 */
export function formatBytes(bytes: number, decimals: number = 2): string {
  if (bytes === 0) return '0 B';
  if (bytes < 0) return '-' + formatBytes(-bytes, decimals);

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));
  const size = parseFloat((bytes / Math.pow(k, i)).toFixed(dm));

  return `${formatNumber(size, dm)} ${sizes[i]}`;
}

/**
 * 格式化日期时间为易读格式
 * @param dateString 日期字符串或Date对象
 * @param format 格式化类型 'date' | 'time' | 'datetime'
 * @param timezone 时区，如果不提供则使用当前选中的时区
 * @returns 格式化后的日期字符串
 */
export function formatDate(
  dateString: string | Date,
  format: 'date' | 'time' | 'datetime' = 'datetime',
  timezone?: string
): string {
  if (!dateString) {
    return '';
  }

  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;

  if (isNaN(date.getTime())) {
    return '';
  }

  // 如果提供了时区参数，使用时区感知的格式化
  if (timezone) {
    try {
      // 使用同步导入避免 async/await 问题
      const timezoneUtils = require('./timezone');
      const { formatDateInTimezone } = timezoneUtils;
      const options: Intl.DateTimeFormatOptions = {};

      if (format === 'date' || format === 'datetime') {
        options.year = 'numeric';
        options.month = '2-digit';
        options.day = '2-digit';
      }

      if (format === 'time' || format === 'datetime') {
        options.hour = '2-digit';
        options.minute = '2-digit';
        options.second = '2-digit';
        options.hour12 = false;
      }

      return formatDateInTimezone(date, options, timezone);
    } catch (error) {
      formattersLogger.warn('Failed to format date with timezone', {
        error: error instanceof Error ? error.message : String(error),
        format,
        timezone,
        dateInput: typeof dateString === 'string' ? dateString : dateString.toISOString(),
        stack: error instanceof Error ? error.stack : undefined,
      });
      // 降级到默认格式化
    }
  }

  // 默认格式化（不考虑时区）
  const options: Intl.DateTimeFormatOptions = {};

  if (format === 'date' || format === 'datetime') {
    options.year = 'numeric';
    options.month = '2-digit';
    options.day = '2-digit';
  }

  if (format === 'time' || format === 'datetime') {
    options.hour = '2-digit';
    options.minute = '2-digit';
    options.second = '2-digit';
    options.hour12 = false;
  }

  return new Intl.DateTimeFormat('zh-CN', options).format(date);
}

/**
 * 格式化日期时间为易读格式（时区感知版本）
 * 这是一个同步版本，直接使用当前选中的时区
 * @param dateString 日期字符串或Date对象
 * @param format 格式化类型 'date' | 'time' | 'datetime'
 * @returns 格式化后的日期字符串
 */
export function formatDateWithTimezone(
  dateString: string | Date,
  format: 'date' | 'time' | 'datetime' = 'datetime'
): string {
  if (!dateString) {
    return '';
  }

  const date = typeof dateString === 'string' ? new Date(dateString) : dateString;

  if (isNaN(date.getTime())) {
    return '';
  }

  try {
    // 动态导入时区工具函数
    if (typeof window !== 'undefined') {
      // 在浏览器环境中，我们可以直接使用时区工具
      const timezoneUtils = require('./timezone');
      const { formatDateInTimezone } = timezoneUtils;
      const options: Intl.DateTimeFormatOptions = {};

      if (format === 'date' || format === 'datetime') {
        options.year = 'numeric';
        options.month = '2-digit';
        options.day = '2-digit';
      }

      if (format === 'time' || format === 'datetime') {
        options.hour = '2-digit';
        options.minute = '2-digit';
        options.second = '2-digit';
        options.hour12 = false;
      }

      return formatDateInTimezone(date, options);
    }
  } catch (error) {
    formattersLogger.warn('Failed to format date with timezone (timezone-aware)', {
      error: error instanceof Error ? error.message : String(error),
      format,
      dateInput: typeof dateString === 'string' ? dateString : dateString.toISOString(),
      stack: error instanceof Error ? error.stack : undefined,
    });
  }

  // 降级到默认格式化
  return formatDate(dateString, format);
}
