# API 端点快速参考

> 📅 生成时间：2025-06-15  
> 🎯 用途：快速查找 API 端点信息和使用示例

## 📋 端点总览

| 端点 | 方法 | 功能 | 状态 | 组件使用 |
|------|------|------|------|----------|
| `/api/dashboard/stats` | POST | 仪表板统计 | ✅ 活跃 | Dashboard |
| `/api/dashboard/overview` | GET | 仪表板概览 | ✅ 活跃 | Dashboard |
| `/api/charts/all` | POST | 所有图表数据 | ✅ 活跃 | Dashboard |
| `/api/charts/bar` | GET | 柱状图数据 | ✅ 活跃 | ChartPanel |
| `/api/charts/line` | GET | 折线图数据 | ✅ 活跃 | ChartPanel |
| `/api/charts/pie` | GET | 饼图数据 | ✅ 活跃 | ChartPanel |
| `/api/charts/scatter` | GET | 散点图数据 | ✅ 活跃 | ChartPanel |
| `/api/data/query` | POST | 数据查询 | ✅ 活跃 | DataQuery |
| `/api/data/export` | GET | 数据导出 | 🔄 计划中 | DataQuery |
| `/api/liquidation/snapshot` | POST | 清算快照 | ✅ 活跃 | LiquidationDashboard |
| `/api/liquidation/trend` | GET | 清算趋势 | ✅ 活跃 | LiquidationDashboard |
| `/api/liquidation/trend-detailed` | GET | 详细清算趋势 | ✅ 活跃 | LiquidationDashboard |

## 🚀 清算数据 API

### 获取清算快照数据

```http
POST /api/liquidation/snapshot
Content-Type: application/json

{
  "timeRange": "24h"
}
```

**参数说明：**
- `timeRange`: 时间范围，可选值：`"1h"` | `"4h"` | `"12h"` | `"24h"`

**响应示例：**
```json
{
  "status": "success",
  "data": {
    "data": [
      {
        "datetime": "2025-06-15T10:30:00Z",
        "symbol": "BTCUSDT",
        "side": "long",
        "amount": 50000,
        "price": 67500,
        "coinType": "mainstream"
      }
    ],
    "stats": {
      "totalAmount": 1250000,
      "totalCount": 45,
      "longAmount": 750000,
      "shortAmount": 500000,
      "longShortRatio": 1.5
    }
  }
}
```

### 获取详细清算趋势数据

```http
GET /api/liquidation/trend-detailed?timeFrame=1d&duration=7d
```

**参数说明：**
- `timeFrame`: 时间粒度，可选值：`"1h"` | `"4h"` | `"1d"` | `"1w"`
- `duration`: 总时间范围，可选值：`"7d"` | `"30d"` | `"90d"`

## 📊 仪表板 API

### 获取仪表板统计数据

```http
POST /api/dashboard/stats
Content-Type: application/json

{
  "startTime": "2025-06-01T00:00:00Z",
  "endTime": "2025-06-15T23:59:59Z",
  "selectedTimeZone": "UTC"
}
```

**响应示例：**
```json
{
  "status": "success",
  "data": {
    "totalUsers": 8750,
    "monthlyRevenue": 45000,
    "conversionRate": 12,
    "activeUsers": 5200
  }
}
```

### 获取所有图表数据

```http
POST /api/charts/all
Content-Type: application/json

{
  "startTime": "2025-06-01T00:00:00Z",
  "endTime": "2025-06-15T23:59:59Z",
  "selectedTimeZone": "UTC"
}
```

## 🔍 数据查询 API

### 执行数据查询

```http
POST /api/data/query
Content-Type: application/json

{
  "keyword": "bitcoin",
  "status": "active",
  "dateRange": ["2025-06-01", "2025-06-15"],
  "page": 1,
  "pageSize": 20
}
```

**响应示例：**
```json
{
  "status": "success",
  "data": {
    "items": [
      {
        "id": "1",
        "name": "Bitcoin Transaction",
        "status": "active",
        "date": "2025-06-15",
        "amount": 1.5
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 5,
      "totalItems": 100,
      "itemsPerPage": 20
    }
  }
}
```

## 📈 图表数据 API

### 获取单个图表数据

```http
GET /api/charts/bar
GET /api/charts/line
GET /api/charts/pie
GET /api/charts/scatter
```

**响应格式：**
```json
{
  "status": "success",
  "data": [
    {
      "name": "数据点1",
      "value": 100,
      "category": "分类A"
    }
  ]
}
```

## 🔧 通用响应格式

### 成功响应
```json
{
  "status": "success",
  "data": { /* 具体数据 */ },
  "message": "操作成功" // 可选
}
```

### 错误响应
```json
{
  "status": "error",
  "message": "错误描述",
  "code": "ERROR_CODE", // 可选
  "data": null
}
```

## 🛠️ 使用示例

### JavaScript/TypeScript 调用示例

```typescript
// 获取清算快照数据
async function fetchLiquidationSnapshot(timeRange: string) {
  try {
    const response = await fetch('/api/liquidation/snapshot', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ timeRange }),
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('获取清算数据失败:', error);
    throw error;
  }
}

// 获取仪表板统计数据
async function fetchDashboardStats(startTime: string, endTime: string) {
  const response = await fetch('/api/dashboard/stats', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      startTime,
      endTime,
      selectedTimeZone: 'UTC'
    }),
  });
  
  return response.json();
}

// 执行数据查询
async function queryData(params: QueryParams) {
  const response = await fetch('/api/data/query', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(params),
  });
  
  return response.json();
}
```

### Svelte 组件中的使用

```svelte
<script lang="ts">
  import { onMount } from 'svelte';
  import { liquidationStore } from '$lib/stores/features/liquidation';
  
  // 通过 store 调用 API
  onMount(async () => {
    await liquidationStore.loadSnapshotData('24h');
    await liquidationStore.loadDetailedTrendData('1d', '7d');
  });
  
  // 手动刷新数据
  async function refreshData() {
    await liquidationStore.refreshAllData();
  }
</script>

<button on:click={refreshData}>刷新数据</button>
```

## 🔒 认证和安全

### 请求头
```http
Content-Type: application/json
Authorization: Bearer <token>  // 如果需要认证
X-API-Version: v1              // API版本控制
```

### 错误码
| 状态码 | 含义 | 处理建议 |
|--------|------|----------|
| 200 | 成功 | 正常处理数据 |
| 400 | 请求参数错误 | 检查请求参数格式 |
| 401 | 未授权 | 检查认证信息 |
| 403 | 禁止访问 | 检查权限设置 |
| 404 | 端点不存在 | 检查URL路径 |
| 500 | 服务器错误 | 重试或联系技术支持 |

## 📝 注意事项

1. **时区处理：** 所有时间参数都应使用 ISO 8601 格式，建议使用 UTC 时区
2. **分页查询：** 大数据量查询请使用分页参数避免超时
3. **频率限制：** 清算数据 API 建议最小刷新间隔为 1 分钟
4. **错误处理：** 所有 API 调用都应实现适当的错误处理
5. **缓存策略：** 静态数据可以在客户端缓存，动态数据建议设置合理的缓存时间

## 🔄 更新日志

- **2025-06-15：** 初始版本，包含所有当前活跃的 API 端点
- **计划中：** 数据导出功能 API 端点实现
