/**
 * 错误处理相关的类型定义
 *
 * @category Types
 */

/**
 * 错误信息接口
 */
export interface ErrorInfo {
  /** 组件堆栈信息 */
  componentStack?: string;
  /** 组件属性 */
  props?: Record<string, any>;
  /** 错误类型 */
  type?: 'route-error' | 'component-error' | 'component-fatal-error' | 
         'unhandled-error' | 'unhandled-rejection' | 'api-error';
  /** 是否抑制通知显示 */
  suppressNotification?: boolean;
  /** 路由信息（仅限路由错误） */
  route?: string;
  /** 文件名（仅限 JavaScript 错误） */
  filename?: string;
  /** 行号（仅限 JavaScript 错误） */
  lineno?: number;
  /** 列号（仅限 JavaScript 错误） */
  colno?: number;
  /** Promise 拒绝原因（仅限 Promise 错误） */
  reason?: any;
  /** 错误发生时间 */
  timestamp?: string;
}

/**
 * 错误处理器函数类型
 */
export type ErrorHandler = (error: Error, errorInfo?: ErrorInfo) => void;

/**
 * 错误边界组件属性
 */
export interface ErrorBoundaryProps {
  /** 自定义错误回退组件 */
  fallbackComponent?: any;
  /** 是否显示错误详情 */
  showErrorDetails?: boolean;
  /** 是否启用错误恢复功能 */
  enableRecovery?: boolean;
  /** 错误处理回调函数 */
  onError?: ErrorHandler | null;
}

/**
 * 错误恢复组件属性
 */
export interface ErrorRecoveryProps {
  /** 错误对象 */
  error: Error;
  /** 错误信息 */
  errorInfo?: ErrorInfo;
  /** 重置错误状态的函数 */
  resetError: () => void;
}

/**
 * 错误统计信息
 */
export interface ErrorStats {
  /** 错误ID */
  errorId: string;
  /** 错误发生时间 */
  timestamp: string;
  /** 错误类型 */
  type: string;
  /** 错误消息 */
  message: string;
  /** 用户代理 */
  userAgent: string;
  /** 页面URL */
  url: string;
  /** 是否已解决 */
  resolved: boolean;
}

/**
 * 全局错误管理器配置
 */
export interface ErrorManagerConfig {
  /** 是否启用全局错误捕获 */
  enableGlobalCapture: boolean;
  /** 是否启用错误上报 */
  enableReporting: boolean;
  /** 错误上报端点 */
  reportingEndpoint?: string;
  /** 最大错误缓存数量 */
  maxErrorCache: number;
  /** 是否在开发环境显示详细错误 */
  showDetailsInDev: boolean;
}

/**
 * 错误上报数据
 */
export interface ErrorReport {
  /** 错误ID */
  errorId: string;
  /** 错误消息 */
  message: string;
  /** 错误堆栈 */
  stack?: string;
  /** 错误类型 */
  type: string;
  /** 发生时间 */
  timestamp: string;
  /** 用户代理 */
  userAgent: string;
  /** 页面URL */
  url: string;
  /** 用户ID（如果有） */
  userId?: string;
  /** 会话ID（如果有） */
  sessionId?: string;
  /** 附加上下文 */
  context?: Record<string, any>;
}

/**
 * 路由错误处理器接口
 */
export interface RouteErrorHandler {
  handleRouteError(error: Error, route?: string): void;
  isRouteError(error: Error): boolean;
}
