/**
 * 防抖 Hook
 *
 * 提供防抖功能，延迟执行函数直到停止调用一段时间后
 *
 * @category Hooks
 */

import { onDestroy } from 'svelte';
import { derived, type Readable, type Writable, writable } from 'svelte/store';

import { createModuleLogger } from '$lib/utils/logger';

const debounceLogger = createModuleLogger('use-debounce');

/**
 * 防抖配置
 */
export interface UseDebounceOptions {
  /** 延迟时间（毫秒） */
  delay: number;
  /** 是否立即执行第一次调用 */
  immediate?: boolean;
  /** 最大等待时间（毫秒），超过此时间强制执行 */
  maxWait?: number;
}

/**
 * 防抖值 Hook 返回值
 */
export interface UseDebouncedValueReturn<T> {
  /** 防抖后的值 */
  debouncedValue: Readable<T>;
  /** 是否正在等待 */
  isPending: Readable<boolean>;
  /** 立即更新值 */
  flush: () => void;
  /** 取消防抖 */
  cancel: () => void;
}

/**
 * 防抖函数 Hook 返回值
 */
export interface UseDebouncedFunctionReturn<T extends (...args: any[]) => any> {
  /** 防抖后的函数 */
  debouncedFunction: T;
  /** 是否正在等待 */
  isPending: Readable<boolean>;
  /** 立即执行 */
  flush: () => void;
  /** 取消执行 */
  cancel: () => void;
}

/**
 * 防抖值 Hook
 */
export function useDebouncedValue<T>(
  value: Writable<T>,
  options: UseDebounceOptions
): UseDebouncedValueReturn<T> {
  const { delay, immediate = false, maxWait } = options;

  let timeoutId: NodeJS.Timeout | null = null;
  let maxTimeoutId: NodeJS.Timeout | null = null;
  let lastCallTime = 0;

  const isPending = writable(false);
  const debouncedValue = writable<T>();

  // 初始化防抖值
  value.subscribe((currentValue) => {
    debouncedValue.set(currentValue);
  });

  const debouncedStore = derived<[Writable<T>, Writable<boolean>], T>(
    [value, isPending],
    ([currentValue, pending], set) => {
      const now = Date.now();
      lastCallTime = now;

      // 清除之前的定时器
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      // 如果是立即执行且是第一次调用
      if (immediate && !pending) {
        set(currentValue);
        isPending.set(true);

        debounceLogger.debug('Immediate execution', { delay, value: currentValue });

        timeoutId = setTimeout(() => {
          isPending.set(false);
          timeoutId = null;
        }, delay);

        return;
      }

      isPending.set(true);

      // 设置最大等待时间
      if (maxWait && !maxTimeoutId) {
        maxTimeoutId = setTimeout(() => {
          if (timeoutId) {
            clearTimeout(timeoutId);
          }
          set(currentValue);
          isPending.set(false);
          timeoutId = null;
          maxTimeoutId = null;

          debounceLogger.debug('Max wait reached', { maxWait, value: currentValue });
        }, maxWait);
      }

      // 设置防抖定时器
      timeoutId = setTimeout(() => {
        // 检查是否在延迟期间有新的调用
        if (Date.now() - lastCallTime >= delay) {
          set(currentValue);
          isPending.set(false);

          if (maxTimeoutId) {
            clearTimeout(maxTimeoutId);
            maxTimeoutId = null;
          }

          timeoutId = null;

          debounceLogger.debug('Debounced execution', { delay, value: currentValue });
        }
      }, delay);
    }
  );

  // 立即更新值
  function flush(): void {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }

    if (maxTimeoutId) {
      clearTimeout(maxTimeoutId);
      maxTimeoutId = null;
    }

    value.subscribe((currentValue) => {
      debouncedValue.set(currentValue);
      isPending.set(false);

      debounceLogger.debug('Flushed', { value: currentValue });
    })();
  }

  // 取消防抖
  function cancel(): void {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }

    if (maxTimeoutId) {
      clearTimeout(maxTimeoutId);
      maxTimeoutId = null;
    }

    isPending.set(false);

    debounceLogger.debug('Cancelled');
  }

  // 清理函数
  onDestroy(() => {
    cancel();
  });

  return {
    debouncedValue: debouncedStore,
    isPending,
    flush,
    cancel,
  };
}

/**
 * 防抖函数 Hook
 */
export function useDebouncedFunction<T extends (...args: any[]) => any>(
  fn: T,
  options: UseDebounceOptions
): UseDebouncedFunctionReturn<T> {
  const { delay, immediate = false, maxWait } = options;

  let timeoutId: NodeJS.Timeout | null = null;
  let maxTimeoutId: NodeJS.Timeout | null = null;
  let lastArgs: Parameters<T> | null = null;
  let lastCallTime = 0;

  const isPending = writable(false);

  function debouncedFunction(...args: Parameters<T>): void {
    const now = Date.now();
    lastArgs = args;
    lastCallTime = now;

    // 清除之前的定时器
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    // 如果是立即执行且是第一次调用
    if (immediate && !timeoutId) {
      fn(...args);
      isPending.set(true);

      debounceLogger.debug('Immediate function execution', { delay, args });

      timeoutId = setTimeout(() => {
        isPending.set(false);
        timeoutId = null;
      }, delay);

      return;
    }

    isPending.set(true);

    // 设置最大等待时间
    if (maxWait && !maxTimeoutId) {
      maxTimeoutId = setTimeout(() => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        if (lastArgs) {
          fn(...lastArgs);
        }
        isPending.set(false);
        timeoutId = null;
        maxTimeoutId = null;

        debounceLogger.debug('Max wait function execution', { maxWait, args: lastArgs });
      }, maxWait);
    }

    // 设置防抖定时器
    timeoutId = setTimeout(() => {
      // 检查是否在延迟期间有新的调用
      if (Date.now() - lastCallTime >= delay && lastArgs) {
        fn(...lastArgs);
        isPending.set(false);

        if (maxTimeoutId) {
          clearTimeout(maxTimeoutId);
          maxTimeoutId = null;
        }

        timeoutId = null;

        debounceLogger.debug('Debounced function execution', { delay, args: lastArgs });
      }
    }, delay);
  }

  // 立即执行
  function flush(): void {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }

    if (maxTimeoutId) {
      clearTimeout(maxTimeoutId);
      maxTimeoutId = null;
    }

    if (lastArgs) {
      fn(...lastArgs);
      isPending.set(false);

      debounceLogger.debug('Function flushed', { args: lastArgs });
    }
  }

  // 取消执行
  function cancel(): void {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }

    if (maxTimeoutId) {
      clearTimeout(maxTimeoutId);
      maxTimeoutId = null;
    }

    isPending.set(false);
    lastArgs = null;

    debounceLogger.debug('Function cancelled');
  }

  // 清理函数
  onDestroy(() => {
    cancel();
  });

  return {
    debouncedFunction: debouncedFunction as T,
    isPending,
    flush,
    cancel,
  };
}

/**
 * 简化的防抖值 Hook
 */
export function useSimpleDebounce<T>(value: Writable<T>, delay: number): Readable<T> {
  const { debouncedValue } = useDebouncedValue(value, { delay });
  return debouncedValue;
}

/**
 * 简化的防抖函数 Hook
 */
export function useSimpleDebouncedFunction<T extends (...args: any[]) => any>(
  fn: T,
  delay: number
): T {
  const { debouncedFunction } = useDebouncedFunction(fn, { delay });
  return debouncedFunction;
}
