// 图表相关常量 - 使用 CSS 变量以支持主题切换
export const CHART_COLORS = {
  // 基础颜色 - 使用 CSS 变量
  PRIMARY: 'hsl(var(--chart-1))',
  SUCCESS: 'hsl(var(--chart-2))',
  WARNING: 'hsl(var(--chart-3))',
  ERROR: 'hsl(var(--chart-4))',

  // 多空颜色
  LONG: 'hsl(var(--chart-2))', // 绿色系 - 多头
  SHORT: 'hsl(var(--chart-4))', // 红色系 - 空头

  // 币种类型颜色
  MAJOR: 'hsl(var(--chart-1))', // 蓝色系 - 主流币
  ALTCOIN: 'hsl(var(--chart-5))', // 紫色系 - 山寨币

  // 组合颜色
  MAJOR_LONG: 'hsl(var(--chart-1))', // 主流币-多头
  MAJOR_SHORT: 'hsl(var(--chart-5))', // 主流币-空头
  ALTCOIN_LONG: 'hsl(var(--chart-2))', // 山寨币-多头
  ALTCOIN_SHORT: 'hsl(var(--chart-4))', // 山寨币-空头

  // 调色板 - 使用 CSS 变量
  PALETTE: [
    'hsl(var(--chart-1))',
    'hsl(var(--chart-2))',
    'hsl(var(--chart-3))',
    'hsl(var(--chart-4))',
    'hsl(var(--chart-5))',
    'hsl(var(--primary))',
    'hsl(var(--secondary))',
    'hsl(var(--accent))',
    'hsl(var(--muted))',
    'hsl(var(--ring))',
  ],
} as const;

// 获取 CSS 变量颜色的辅助函数
export function getCSSColor(variable: string): string {
  if (typeof window !== 'undefined') {
    return getComputedStyle(document.documentElement).getPropertyValue(variable).trim();
  }
  return '';
}

export const CHART_CONFIG = {
  DEFAULT_HEIGHT: 400,
  MIN_HEIGHT: 200,
  MAX_HEIGHT: 800,
  ANIMATION_DURATION: 1000,
  TOOLTIP_TRIGGER_DELAY: 100,
} as const;

export const CHART_TYPES = {
  BAR: 'bar',
  LINE: 'line',
  PIE: 'pie',
  SCATTER: 'scatter',
  AREA: 'area',
  CANDLESTICK: 'candlestick',
} as const;

export const CHART_THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
} as const;

export const LIQUIDATION_CHART_CONFIG = {
  // 时间粒度选项
  TIME_FRAMES: [
    { value: '1h', label: '1小时' },
    { value: '4h', label: '4小时' },
    { value: '1d', label: '1天' },
    { value: '1w', label: '1周' },
  ],

  // 时间范围选项
  TIME_RANGES: [
    { value: '7d', label: '7天' },
    { value: '30d', label: '30天' },
    { value: '90d', label: '90天' },
  ],

  // 快照时间范围选项
  SNAPSHOT_RANGES: [
    { value: '1h', label: '1小时' },
    { value: '4h', label: '4小时' },
    { value: '12h', label: '12小时' },
    { value: '24h', label: '24小时' },
  ],

  // 视图模式选项
  VIEW_MODES: [
    { value: 'overall', label: '全市场' },
    { value: 'comparison', label: '主流/山寨对比' },
  ],
} as const;
