<!-- src/lib/components/features/liquidation/KpiPanel.svelte -->
<script lang="ts">
  import { Card, CardContent } from '$lib/components/ui/card';
  import { Skeleton } from '$lib/components/ui/skeleton';
  import { KpiCard } from './charts';
  
  // Props
  export let data: {
    totalAmount: number;
    totalCount: number;
    longAmount: number;
    shortAmount: number;
    longShortRatio: number;
  } | null = null;
  export let isLoading: boolean = false;
  export let timeRangeLabel: string = '';
  export let title: string = '核心清算指标概览';
  
  // 事件回调
  export let onKpiClick: (params: { type: string; value: number }) => void = () => {};
  
  // 计算动态标题
  $: formattedTitle = timeRangeLabel ? `[${timeRangeLabel}] ${title}` : title;
  
  // 处理 KPI 点击事件
  function handleKpiClick(params: { type: string; value: number }) {
    onKpiClick(params);
  }
</script>

<div class="mb-3" role="region" aria-label={`${formattedTitle} KPI panel`}>
  {#if isLoading}
    <!-- KPI 加载状态骨架屏 -->
    <div class="grid w-full grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
      {#each Array(5) as _}
        <Card>
          <CardContent class="p-4">
            <Skeleton class="mb-2 h-4 w-20" />
            <Skeleton class="mb-1 h-8 w-24" />
            <Skeleton class="h-3 w-12" />
          </CardContent>
        </Card>
      {/each}
    </div>
  {:else}
    <!-- KPI 数据展示 -->
    <KpiCard {data} onKpiClick={handleKpiClick} />
  {/if}
</div>
