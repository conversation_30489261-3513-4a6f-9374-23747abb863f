/**
 * 历史数据查询服务
 *
 * 提供历史数据查询、处理和格式化功能
 *
 * @category Services
 */

import type {
  ApiResponse,
  DirectionalData,
  HistoricalDataApiResponse,
  HistoricalDataItem,
  HistoricalDataStats,
  HistoricalQueryParams,
  HistoricalQueryResponse,
  HistoricalTableRow,
  MarketEventType,
  StatisticsApiResponse,
  StatisticsDataItem,
  StatisticsQueryParams,
} from '$lib/types';
import { isDirectionalEventType } from '$lib/types/business/market';
import { formatCurrency, formatNumber } from '$lib/utils/formatters';
import { createModuleLogger } from '$lib/utils/logger';
import { formatDateInTimezone } from '$lib/utils/timezone';

const historicalLogger = createModuleLogger('historical-service');

/**
 * 历史数据查询服务类
 *
 * 提供历史数据的查询、处理和格式化功能
 */
export class HistoricalDataService {
  private baseURL: string;

  constructor() {
    this.baseURL = import.meta.env.VITE_API_BASE_URL || '';
  }

  /**
   * 查询历史数据（使用统计 API 规范）
   * @param params 查询参数
   * @returns 历史数据查询响应
   */
  async queryHistoricalData(params: HistoricalQueryParams): Promise<HistoricalDataApiResponse> {
    historicalLogger.info('Querying historical data with statistics API', { params });

    try {
      // 转换为统计 API 参数格式
      const statisticsParams: StatisticsQueryParams = {
        base: params.selectedCoins.join(','),
        startDate: params.startDate,
        endDate: params.endDate,
        type: params.dataTypes === 'TWAP' ? 'TWAP' : 'LIQUIDATION_ORDER',
        threshold: params.threshold,
      };

      // 构建查询参数
      const queryParams = new URLSearchParams();
      queryParams.append('base', statisticsParams.base);
      queryParams.append('startDate', statisticsParams.startDate);
      queryParams.append('endDate', statisticsParams.endDate);
      queryParams.append('type', statisticsParams.type);
      if (statisticsParams.threshold !== undefined) {
        queryParams.append('threshold', String(statisticsParams.threshold));
      }

      const response = await fetch(`${this.baseURL}/historical/query?${queryParams.toString()}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const rawData: StatisticsApiResponse = await response.json();

      if (rawData.code !== 1000) {
        throw new Error(`API Error ${rawData.code}: ${rawData.message}`);
      }

      // 处理和格式化数据
      const processedData = this.processStatisticsData(rawData.data, params);

      historicalLogger.info('Historical data query completed', {
        totalRecords: processedData.total,
        tableRows: processedData.tableRows.length,
      });

      return {
        status: 'success',
        data: processedData,
      };
    } catch (error) {
      historicalLogger.error('Failed to query historical data', {
        error: error instanceof Error ? error.message : String(error),
        params,
      });

      throw error;
    }
  }

  /**
   * 处理统计 API 数据
   * @param rawData 统计 API 原始数据
   * @param params 查询参数
   * @returns 处理后的数据
   */
  private processStatisticsData(
    rawData: StatisticsDataItem[],
    params: HistoricalQueryParams
  ): HistoricalQueryResponse {
    // 将统计 API 数据转换为 HistoricalDataItem 格式
    const items: HistoricalDataItem[] = this.convertStatisticsToHistoricalData(rawData, params);

    // 按日期分组数据
    const groupedByDate = this.groupDataByDate(items);

    // 生成表格行数据，传递数据类型以支持方向性数据
    const tableRows = this.generateTableRows(groupedByDate, params.selectedCoins, params.dataTypes);

    // 排序表格行
    const sortedTableRows = this.sortTableRows(tableRows, params.sortBy, params.sortOrder);

    return {
      items,
      tableRows: sortedTableRows,
      total: items.length,
      queryParams: params,
    };
  }

  /**
   * 将统计 API 数据转换为历史数据项格式
   * @param statisticsData 统计数据
   * @param params 查询参数
   * @returns 历史数据项数组
   */
  private convertStatisticsToHistoricalData(
    statisticsData: StatisticsDataItem[],
    params: HistoricalQueryParams
  ): HistoricalDataItem[] {
    const items: HistoricalDataItem[] = [];

    statisticsData.forEach((dayData) => {
      Object.entries(dayData.amount).forEach(([coinSymbol, coinData]) => {
        const typedCoinData = coinData as { long: number; short: number };

        // 为多头数据创建一个项目
        if (typedCoinData.long > 0) {
          items.push({
            id: `${dayData.date}-${coinSymbol}-long`,
            date: dayData.date,
            symbol: coinSymbol,
            name: coinSymbol,
            type: params.dataTypes,
            amount: typedCoinData.long,
            quantity: 1,
            price: typedCoinData.long,
            changePercent: 0,
            volume: typedCoinData.long,
            marketCap: 0,
            timestamp: new Date(dayData.date).getTime() / 1000,
            metadata: {
              side: 1, // 多头/买入
            },
          });
        }

        // 为空头数据创建一个项目
        if (typedCoinData.short > 0) {
          items.push({
            id: `${dayData.date}-${coinSymbol}-short`,
            date: dayData.date,
            symbol: coinSymbol,
            name: coinSymbol,
            type: params.dataTypes,
            amount: typedCoinData.short,
            quantity: 1,
            price: typedCoinData.short,
            changePercent: 0,
            volume: typedCoinData.short,
            marketCap: 0,
            timestamp: new Date(dayData.date).getTime() / 1000,
            metadata: {
              side: 0, // 空头/卖出
            },
          });
        }
      });
    });

    return items;
  }

  /**
   * 处理和格式化历史数据（保持向后兼容）
   * @param rawData 原始数据
   * @param params 查询参数
   * @returns 处理后的数据
   */
  private processHistoricalData(
    rawData: any,
    params: HistoricalQueryParams
  ): HistoricalQueryResponse {
    const items: HistoricalDataItem[] = rawData.items || [];

    // 按日期分组数据
    const groupedByDate = this.groupDataByDate(items);

    // 生成表格行数据，传递数据类型以支持方向性数据
    const tableRows = this.generateTableRows(groupedByDate, params.selectedCoins, params.dataTypes);

    // 排序表格行
    const sortedTableRows = this.sortTableRows(tableRows, params.sortBy, params.sortOrder);

    return {
      items,
      tableRows: sortedTableRows,
      total: rawData.total || items.length,
      queryParams: params,
    };
  }

  /**
   * 按日期分组数据
   * @param items 数据项数组
   * @returns 按日期分组的数据
   */
  private groupDataByDate(items: HistoricalDataItem[]): Record<string, HistoricalDataItem[]> {
    return items.reduce(
      (groups, item) => {
        const date = item.date;
        if (!groups[date]) {
          groups[date] = [];
        }
        groups[date].push(item);
        return groups;
      },
      {} as Record<string, HistoricalDataItem[]>
    );
  }

  /**
   * 生成表格行数据
   * @param groupedData 按日期分组的数据
   * @param selectedCoins 选中的币种
   * @param dataType 数据类型
   * @returns 表格行数据数组
   */
  private generateTableRows(
    groupedData: Record<string, HistoricalDataItem[]>,
    selectedCoins: string[],
    dataType?: MarketEventType
  ): HistoricalTableRow[] {
    const isDirectional = dataType && isDirectionalEventType(dataType);

    return Object.entries(groupedData).map(([date, items]) => {
      const coinData: Record<string, any> = {};
      let totalAmount = 0;
      let totalLongAmount = 0;
      let totalShortAmount = 0;
      let totalLongCount = 0;
      let totalShortCount = 0;

      // 为每个选中的币种生成数据
      selectedCoins.forEach((coin) => {
        const coinItems = items.filter((item) => item.symbol === coin);
        const totalCoinAmount = coinItems.reduce((sum, item) => sum + item.amount, 0);
        const totalCoinVolume = coinItems.reduce((sum, item) => sum + item.volume, 0);
        const avgChangePercent =
          coinItems.length > 0
            ? coinItems.reduce((sum, item) => sum + item.changePercent, 0) / coinItems.length
            : 0;

        const coinDataEntry: any = {
          amount: totalCoinAmount,
          formattedAmount: formatCurrency(totalCoinAmount),
          changePercent: avgChangePercent,
          volume: totalCoinVolume,
          formattedVolume: formatCurrency(totalCoinVolume),
        };

        // 如果是方向性数据，计算多空分解
        if (isDirectional) {
          const directionalData = this.calculateDirectionalData(coinItems);
          coinDataEntry.directional = directionalData;

          totalLongAmount += directionalData.longAmount;
          totalShortAmount += directionalData.shortAmount;
          totalLongCount += directionalData.longCount;
          totalShortCount += directionalData.shortCount;
        }

        coinData[coin] = coinDataEntry;
        totalAmount += totalCoinAmount;
      });

      const tableRow: HistoricalTableRow = {
        date,
        dateDisplay: formatDateInTimezone(new Date(date), {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
        }),
        coinData,
        totalAmount,
        formattedTotalAmount: formatCurrency(totalAmount),
      };

      // 如果是方向性数据，添加总方向性数据
      if (isDirectional) {
        const longShortRatio = totalShortAmount > 0 ? totalLongAmount / totalShortAmount : 0;
        tableRow.totalDirectional = {
          longAmount: totalLongAmount,
          formattedLongAmount: formatCurrency(totalLongAmount),
          shortAmount: totalShortAmount,
          formattedShortAmount: formatCurrency(totalShortAmount),
          longCount: totalLongCount,
          shortCount: totalShortCount,
          longShortRatio,
        };
      }

      return tableRow;
    });
  }

  /**
   * 计算方向性数据
   * @param items 数据项数组
   * @returns 方向性数据
   */
  private calculateDirectionalData(items: HistoricalDataItem[]): DirectionalData {
    let longAmount = 0;
    let shortAmount = 0;
    let longCount = 0;
    let shortCount = 0;

    items.forEach((item) => {
      // 从 metadata 中获取方向信息，如果没有则尝试从其他字段推断
      const side = this.extractSideFromItem(item);

      if (side === 1) {
        // 多头/买入
        longAmount += item.amount;
        longCount += 1;
      } else if (side === 0) {
        // 空头/卖出
        shortAmount += item.amount;
        shortCount += 1;
      } else {
        // 如果无法确定方向，平均分配
        longAmount += item.amount / 2;
        shortAmount += item.amount / 2;
        longCount += 0.5;
        shortCount += 0.5;
      }
    });

    const longShortRatio = shortAmount > 0 ? longAmount / shortAmount : 0;

    return {
      longAmount,
      formattedLongAmount: formatCurrency(longAmount),
      shortAmount,
      formattedShortAmount: formatCurrency(shortAmount),
      longCount: Math.round(longCount),
      shortCount: Math.round(shortCount),
      longShortRatio,
    };
  }

  /**
   * 从数据项中提取方向信息
   * @param item 数据项
   * @returns 方向 (1=多头/买入, 0=空头/卖出, -1=未知)
   */
  private extractSideFromItem(item: HistoricalDataItem): number {
    // 优先从 metadata 中获取
    if (item.metadata?.side !== undefined) {
      return item.metadata.side;
    }

    // 如果是 MarketEvent 格式的数据，从 parameters 中提取
    if ((item as any).parameters) {
      const parameters = (item as any).parameters;
      const sideParam = parameters.find((p: any) => p.parameterId === 'side');
      if (sideParam && typeof sideParam.value === 'number') {
        return sideParam.value;
      }
    }

    // 如果没有明确的方向信息，返回未知
    return -1;
  }

  /**
   * 从 MarketEvent 参数中提取数值
   * @param parameters 参数数组
   * @param parameterId 参数ID
   * @returns 参数值或默认值
   */
  private extractParameterValue(
    parameters: any[],
    parameterId: string,
    defaultValue: any = 0
  ): any {
    const param = parameters.find((p: any) => p.parameterId === parameterId);
    return param ? param.value : defaultValue;
  }

  /**
   * 排序表格行
   * @param rows 表格行数据
   * @param sortBy 排序字段
   * @param sortOrder 排序方向
   * @returns 排序后的表格行数据
   */
  private sortTableRows(
    rows: HistoricalTableRow[],
    sortBy: string = 'date',
    sortOrder: string = 'desc'
  ): HistoricalTableRow[] {
    return rows.sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (sortBy) {
        case 'date':
          aValue = new Date(a.date).getTime();
          bValue = new Date(b.date).getTime();
          break;
        case 'amount':
          aValue = a.totalAmount;
          bValue = b.totalAmount;
          break;
        default:
          aValue = a.date;
          bValue = b.date;
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
  }

  /**
   * 验证查询参数
   * @param params 查询参数
   * @returns 验证结果
   */
  validateQueryParams(params: HistoricalQueryParams): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!params.selectedCoins || params.selectedCoins.length === 0) {
      errors.push('请至少选择一个币种');
    }

    if (!params.startDate) {
      errors.push('请选择开始日期');
    }

    if (!params.endDate) {
      errors.push('请选择结束日期');
    }

    if (params.startDate && params.endDate) {
      const startDate = new Date(params.startDate);
      const endDate = new Date(params.endDate);

      if (startDate > endDate) {
        errors.push('开始日期不能晚于结束日期');
      }

      // 检查日期范围是否过大（例如不超过1年）
      const daysDiff = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24);
      if (daysDiff > 365) {
        errors.push('查询日期范围不能超过1年');
      }
    }

    if (!params.dataTypes) {
      errors.push('请选择一种数据类型');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

// 创建服务实例
export const historicalDataService = new HistoricalDataService();
