// MSW 管理工具
// 基于官方最佳实践的简化实现

import type { SetupWorker } from 'msw/browser';

class MSWManagerClass {
  private worker: SetupWorker | null = null;
  private isRunning = false;

  /**
   * 初始化 MSW - 使用官方推荐的简化配置
   */
  async initialize(): Promise<void> {
    if (this.isRunning) {
      console.warn('MSW: 已经在运行中');
      return;
    }

    try {
      const { worker } = await import('./browser');
      this.worker = worker;

      await worker.start({
        onUnhandledRequest: 'warn', // 官方推荐默认值
        quiet: false, // 开发环境显示日志
      });

      this.isRunning = true;
      console.log('MSW: 模拟API服务初始化成功');
    } catch (error) {
      console.error('MSW: 初始化失败:', error);
      throw error;
    }
  }

  /**
   * 停止 MSW - 简化版本
   */
  stop(): void {
    if (!this.isRunning || !this.worker) {
      return;
    }

    try {
      this.worker.stop();
      this.isRunning = false;
      this.worker = null;
      console.log('MSW: 模拟服务已停止');
    } catch (error) {
      console.error('MSW: 停止服务时出错:', error);
    }
  }

  /**
   * 重启 MSW
   */
  async restart(): Promise<void> {
    this.stop();
    await this.initialize();
  }

  /**
   * 获取 MSW 状态
   */
  getStatus(): { isRunning: boolean; hasWorker: boolean } {
    return {
      isRunning: this.isRunning,
      hasWorker: this.worker !== null,
    };
  }
}

// 导出单例实例
export const mswManager = new MSWManagerClass();

// 开发环境调试工具（简化版）
if (import.meta.env.DEV) {
  (window as any).__MSW__ = {
    stop: () => mswManager.stop(),
    restart: () => mswManager.restart(),
    status: () => mswManager.getStatus(),
  };

  console.log('MSW: 调试工具已添加到 window.__MSW__');
}
