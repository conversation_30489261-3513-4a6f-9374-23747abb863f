/**
 * 全局错误管理器
 *
 * 统一处理应用中的所有错误，包括：
 * - JavaScript 运行时错误
 * - Promise 拒绝错误
 * - API 请求错误
 * - 组件错误
 *
 * @category Utils
 */

import { browser } from '$app/environment';
import type {
  ErrorHandler,
  ErrorInfo,
  ErrorManagerConfig,
  ErrorReport,
  ErrorStats,
} from '$lib/components/features/error-boundary/types';
import { notifications } from '$lib/stores/ui/notifications';

import { errorReporting } from './errorReporting';
import {
  createModuleLogger,
  type ErrorLogContext,
  logApiError,
  logComponentError,
  logError,
} from './logger';

const errorLogger = createModuleLogger('error-manager');

/**
 * 全局错误管理器类
 */
export class ErrorManager {
  private config: ErrorManagerConfig;
  private errorCache: Map<string, ErrorStats> = new Map();
  private errorHandlers: Set<ErrorHandler> = new Set();
  private isInitialized = false;

  constructor(config: Partial<ErrorManagerConfig> = {}) {
    this.config = {
      enableGlobalCapture: true,
      enableReporting: false,
      maxErrorCache: 100,
      showDetailsInDev: true,
      ...config,
    };
  }

  /**
   * 初始化错误管理器
   */
  initialize(): void {
    if (this.isInitialized || !browser) {
      return;
    }

    if (this.config.enableGlobalCapture) {
      this.setupGlobalErrorHandlers();
    }

    this.isInitialized = true;
    errorLogger.info('ErrorManager initialized', { config: this.config });
  }

  /**
   * 销毁错误管理器
   */
  destroy(): void {
    if (!this.isInitialized || !browser) {
      return;
    }

    this.removeGlobalErrorHandlers();
    this.errorCache.clear();
    this.errorHandlers.clear();
    this.isInitialized = false;

    errorLogger.info('ErrorManager destroyed');
  }

  /**
   * 设置全局错误处理器
   */
  private setupGlobalErrorHandlers(): void {
    // JavaScript 错误
    window.addEventListener('error', this.handleGlobalError);

    // Promise 拒绝错误
    window.addEventListener('unhandledrejection', this.handleUnhandledRejection);

    errorLogger.debug('Global error handlers attached');
  }

  /**
   * 移除全局错误处理器
   */
  private removeGlobalErrorHandlers(): void {
    window.removeEventListener('error', this.handleGlobalError);
    window.removeEventListener('unhandledrejection', this.handleUnhandledRejection);

    errorLogger.debug('Global error handlers removed');
  }

  /**
   * 处理全局 JavaScript 错误
   */
  private handleGlobalError = (event: ErrorEvent): void => {
    const error = new Error(event.message);
    error.stack = `${event.filename}:${event.lineno}:${event.colno}`;

    const errorInfo: ErrorInfo = {
      type: 'unhandled-error',
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
    };

    this.captureError(error, errorInfo);
  };

  /**
   * 处理未捕获的 Promise 拒绝
   */
  private handleUnhandledRejection = (event: PromiseRejectionEvent): void => {
    const error = event.reason instanceof Error ? event.reason : new Error(String(event.reason));

    const errorInfo: ErrorInfo = {
      type: 'unhandled-rejection',
      reason: event.reason,
    };

    this.captureError(error, errorInfo);
  };

  /**
   * 捕获并处理错误
   */
  captureError(error: Error, errorInfo?: ErrorInfo): string {
    const errorId = this.generateErrorId();
    const timestamp = new Date().toISOString();

    // 创建错误统计信息
    const errorStats: ErrorStats = {
      errorId,
      timestamp,
      type: errorInfo?.type || 'component-error',
      message: error.message,
      userAgent: browser ? navigator.userAgent : 'server',
      url: browser ? window.location.href : 'server',
      resolved: false,
    };

    // 缓存错误信息
    this.cacheError(errorStats);

    // 使用统一的错误日志记录
    const logContext: ErrorLogContext = {
      errorId,
      errorType: errorInfo?.type || 'component-error',
      url: browser ? window.location.href : undefined,
      userAgent: browser ? navigator.userAgent : undefined,
      component: errorInfo?.componentStack ? 'unknown-component' : undefined,
      context: errorInfo,
    };

    logError(error, logContext, errorLogger);

    // 显示用户通知
    this.showUserNotification(error, errorInfo);

    // 调用注册的错误处理器
    this.notifyErrorHandlers(error, errorInfo);

    // 使用错误报告服务上报错误
    if (this.config.enableReporting) {
      errorReporting.reportError(error, logContext, errorId);
    }

    return errorId;
  }

  /**
   * 生成错误ID
   */
  private generateErrorId(): string {
    return `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 缓存错误信息
   */
  private cacheError(errorStats: ErrorStats): void {
    // 如果缓存已满，删除最旧的错误
    if (this.errorCache.size >= this.config.maxErrorCache) {
      const oldestKey = this.errorCache.keys().next().value;
      if (oldestKey) {
        this.errorCache.delete(oldestKey);
      }
    }

    this.errorCache.set(errorStats.errorId, errorStats);
  }

  /**
   * 显示用户通知
   */
  private showUserNotification(error: Error, errorInfo?: ErrorInfo): void {
    const isDevMode = import.meta.env.DEV;

    if (errorInfo?.type === 'unhandled-rejection') {
      notifications.error(
        '异步操作失败',
        isDevMode ? error.message : '系统遇到了一个异步操作错误，请稍后重试。',
        5000
      );
    } else {
      notifications.error(
        '系统错误',
        isDevMode ? error.message : '系统遇到了一个错误，我们已经记录了这个问题。',
        5000
      );
    }
  }

  /**
   * 通知注册的错误处理器
   */
  private notifyErrorHandlers(error: Error, errorInfo?: ErrorInfo): void {
    this.errorHandlers.forEach((handler) => {
      try {
        handler(error, errorInfo);
      } catch (handlerError) {
        errorLogger.error('Error in error handler', {
          originalError: error.message,
          handlerError: handlerError instanceof Error ? handlerError.message : String(handlerError),
        });
      }
    });
  }

  /**
   * 注册错误处理器
   */
  addErrorHandler(handler: ErrorHandler): void {
    this.errorHandlers.add(handler);
  }

  /**
   * 移除错误处理器
   */
  removeErrorHandler(handler: ErrorHandler): void {
    this.errorHandlers.delete(handler);
  }

  /**
   * 获取错误统计信息
   */
  getErrorStats(): ErrorStats[] {
    return Array.from(this.errorCache.values());
  }

  /**
   * 标记错误为已解决
   */
  markErrorAsResolved(errorId: string): void {
    const errorStats = this.errorCache.get(errorId);
    if (errorStats) {
      errorStats.resolved = true;
      this.errorCache.set(errorId, errorStats);
      errorLogger.info('Error marked as resolved', { errorId });
    }
  }

  /**
   * 清除错误缓存
   */
  clearErrorCache(): void {
    this.errorCache.clear();
    errorLogger.info('Error cache cleared');
  }

  /**
   * 获取配置
   */
  getConfig(): ErrorManagerConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<ErrorManagerConfig>): void {
    this.config = { ...this.config, ...newConfig };
    errorLogger.info('ErrorManager config updated', { config: this.config });
  }
}

// 创建全局实例
export const errorManager = new ErrorManager({
  enableGlobalCapture: true,
  enableReporting: false, // 可以通过环境变量控制
  showDetailsInDev: import.meta.env.DEV,
});

// 在浏览器环境中自动初始化
if (browser) {
  errorManager.initialize();
}
