/**
 * 性能监控工具
 * 用于诊断和监控应用性能问题
 */

import { performanceLogger } from './logger';

interface PerformanceEntry {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
}

class PerformanceMonitor {
  private entries: Map<string, PerformanceEntry> = new Map();
  private isEnabled = true;

  /**
   * 启用或禁用性能监控
   */
  setEnabled(enabled: boolean) {
    this.isEnabled = enabled;
  }

  /**
   * 开始监控一个操作
   */
  start(name: string) {
    if (!this.isEnabled) return;

    this.entries.set(name, {
      name,
      startTime: performance.now(),
    });
  }

  /**
   * 结束监控一个操作
   */
  end(name: string) {
    if (!this.isEnabled) return;

    const entry = this.entries.get(name);
    if (entry) {
      const endTime = performance.now();
      const duration = endTime - entry.startTime;

      entry.endTime = endTime;
      entry.duration = duration;

      // 如果操作耗时超过阈值，输出警告
      if (duration > 100) {
        performanceLogger.warn(`Slow operation detected: ${name} took ${duration.toFixed(2)}ms`, {
          operation: name,
          duration: duration,
          threshold: 100,
        });
      } else {
        performanceLogger.debug(`Operation completed: ${name} in ${duration.toFixed(2)}ms`, {
          operation: name,
          duration: duration,
        });
      }
    }
  }

  /**
   * 监控函数执行时间
   */
  async measure<T>(name: string, fn: () => T | Promise<T>): Promise<T> {
    if (!this.isEnabled) {
      return await fn();
    }

    this.start(name);
    try {
      const result = await fn();
      this.end(name);
      return result;
    } catch (error) {
      this.end(name);
      performanceLogger.error(`Error in operation: ${name}`, {
        operation: name,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      });
      throw error;
    }
  }

  /**
   * 获取所有性能记录
   */
  getEntries(): PerformanceEntry[] {
    return Array.from(this.entries.values()).filter((entry) => entry.duration !== undefined);
  }

  /**
   * 清除所有记录
   */
  clear() {
    this.entries.clear();
  }

  /**
   * 获取性能统计
   */
  getStats() {
    const entries = this.getEntries();
    if (entries.length === 0) return null;

    const durations = entries.map((e) => e.duration!);
    const total = durations.reduce((sum, d) => sum + d, 0);
    const avg = total / durations.length;
    const max = Math.max(...durations);
    const min = Math.min(...durations);

    return {
      count: entries.length,
      total: total.toFixed(2),
      average: avg.toFixed(2),
      max: max.toFixed(2),
      min: min.toFixed(2),
    };
  }

  /**
   * 监控内存使用情况
   */
  logMemoryUsage() {
    if (!this.isEnabled || typeof window === 'undefined') return;

    // @ts-ignore - performance.memory 可能不存在
    const memory = (performance as any).memory;
    if (memory) {
      const memoryInfo = {
        used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
        total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
        limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`,
        usedBytes: memory.usedJSHeapSize,
        totalBytes: memory.totalJSHeapSize,
        limitBytes: memory.jsHeapSizeLimit,
      };

      performanceLogger.debug('Memory usage report', memoryInfo);
    }
  }

  /**
   * 监控 CPU 使用情况（简单的检测方法）
   */
  async checkCPUUsage(): Promise<number> {
    if (!this.isEnabled) return 0;

    const start = performance.now();
    const iterations = 100000;

    // 执行一些计算密集的操作
    let sum = 0;
    for (let i = 0; i < iterations; i++) {
      sum += Math.random();
    }

    const end = performance.now();
    const duration = end - start;

    // 正常情况下这个操作应该很快，如果很慢说明 CPU 负载高
    const expectedDuration = 10; // 预期 10ms 内完成
    const cpuLoad = Math.min(100, (duration / expectedDuration) * 100);

    performanceLogger.debug('CPU load estimate', {
      cpuLoad: `${cpuLoad.toFixed(1)}%`,
      duration: `${duration.toFixed(2)}ms`,
      iterations,
      expectedDuration,
      actualDuration: duration,
    });

    return cpuLoad;
  }

  /**
   * 开始持续监控
   */
  startContinuousMonitoring(interval: number = 5000) {
    if (!this.isEnabled) return;

    const monitor = () => {
      this.logMemoryUsage();
      this.checkCPUUsage();

      const stats = this.getStats();
      if (stats) {
        performanceLogger.debug('Performance statistics', stats);
      }
    };

    // 立即执行一次
    monitor();

    // 定期监控
    const intervalId = setInterval(monitor, interval);

    return () => clearInterval(intervalId);
  }
}

// 创建全局实例
export const performanceMonitor = new PerformanceMonitor();

// 在开发环境中启用详细监控
if (typeof window !== 'undefined' && import.meta.env.DEV) {
  performanceMonitor.setEnabled(true);
  performanceLogger.debug('Performance monitoring enabled in development mode', {
    environment: 'development',
    monitoringInterval: 10000,
  });

  // 开始持续监控
  performanceMonitor.startContinuousMonitoring(10000); // 每10秒监控一次
}
