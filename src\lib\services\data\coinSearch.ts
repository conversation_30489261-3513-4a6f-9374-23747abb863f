// src/lib/services/data/coinSearch.ts
import type { CoinInfo, CoinOption, CoinSearchResponse } from '$lib/types';

import { coinSearchApiService } from '../api/coinSearch';

/**
 * 币种搜索数据服务
 * 提供币种搜索和候选列表功能
 */
export class CoinSearchDataService {
  private cache = new Map<string, { data: CoinOption[]; timestamp: number }>();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存

  /**
   * 搜索币种并返回候选选项
   * @param query 搜索关键词
   * @param limit 返回结果数量限制
   * @returns 币种候选选项列表
   */
  async searchCoins(query: string, limit: number = 10): Promise<CoinOption[]> {
    try {
      // 检查缓存
      const cacheKey = `${query.toLowerCase()}_${limit}`;
      const cached = this.cache.get(cacheKey);

      if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
        return cached.data;
      }

      let response: CoinSearchResponse;

      if (query.trim() === '') {
        // 无搜索关键词时获取热门币种
        response = await coinSearchApiService.getPopularCoins(limit);
      } else {
        // 有搜索关键词时进行搜索
        response = await coinSearchApiService.searchCoins(query, limit);
      }

      // 转换为候选选项格式
      const options = this.transformToOptions(response.items);

      // 更新缓存
      this.cache.set(cacheKey, {
        data: options,
        timestamp: Date.now(),
      });

      return options;
    } catch (error) {
      console.error('币种搜索失败:', error);
      // 返回空数组而不是抛出错误，保证用户体验
      return [];
    }
  }

  /**
   * 获取热门币种列表
   * @param limit 返回结果数量限制
   * @returns 热门币种候选选项列表
   */
  async getPopularCoins(limit: number = 10): Promise<CoinOption[]> {
    return this.searchCoins('', limit);
  }

  /**
   * 将币种信息转换为候选选项格式
   * @param coins 币种信息列表
   * @returns 候选选项列表
   */
  private transformToOptions(coins: CoinInfo[]): CoinOption[] {
    return coins.map((coin) => ({
      value: coin.symbol.toUpperCase(),
      label: coin.name,
      symbol: coin.symbol.toUpperCase(),
      name: coin.name,
      image: coin.image,
      isMainstream: coin.rank ? parseInt(coin.rank) <= 20 : false,
    }));
  }

  /**
   * 根据符号获取币种详细信息
   * @param symbol 币种符号
   * @returns 币种详细信息，如果未找到返回 null
   */
  async getCoinBySymbol(symbol: string): Promise<CoinInfo | null> {
    try {
      const response = await coinSearchApiService.searchCoins(symbol, 50);
      const coin = response.items.find(
        (item: any) => item.symbol.toLowerCase() === symbol.toLowerCase()
      );
      return coin || null;
    } catch (error) {
      console.error('获取币种信息失败:', error);
      return null;
    }
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * 清除过期缓存
   */
  clearExpiredCache(): void {
    const now = Date.now();
    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp >= this.CACHE_DURATION) {
        this.cache.delete(key);
      }
    }
  }
}

// 创建并导出服务实例
export const coinSearchDataService = new CoinSearchDataService();
