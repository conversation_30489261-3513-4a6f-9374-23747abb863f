{"name": "svelte-demo", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write . && eslint . --fix", "lint": "prettier --check . && eslint .", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "docs:api": "typedoc", "docs:api:watch": "typedoc --watch", "docs:serve": "npx http-server docs/api -p 8080 -o"}, "devDependencies": {"@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@fontsource/fira-mono": "^5.0.0", "@internationalized/date": "^3.8.2", "@neoconfetti/svelte": "^2.0.0", "@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@sveltejs/vite-plugin-svelte-inspector": "^4.0.1", "@tailwindcss/vite": "^4.1.8", "@testing-library/jest-dom": "^6.6.3", "@testing-library/svelte": "^5.2.8", "@testing-library/user-event": "^14.6.1", "@types/node": "^24.0.1", "@types/pino": "^7.0.4", "bits-ui": "^2.8.6", "clsx": "^2.1.1", "code-inspector-plugin": "^0.20.12", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-svelte": "^3.0.0", "globals": "^16.0.0", "jsdom": "^26.1.0", "msw": "^2.10.2", "paneforge": "^1.0.2", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "svelte": "^5.25.0", "svelte-check": "^4.0.0", "svelte-sonner": "^1.0.5", "tailwind-merge": "^3.3.1", "tailwind-scrollbar": "^4.0.2", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.1.8", "tw-animate-css": "^1.3.4", "typedoc": "^0.28.5", "typedoc-plugin-markdown": "^4.6.4", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vite": "^6.2.6", "vite-plugin-devtools-json": "^0.2.0", "vitest": "^3.2.3"}, "dependencies": {"@lucide/svelte": "^0.515.0", "echarts": "^5.6.0", "mode-watcher": "^1.0.8", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "svelte-echarts": "^1.0.0"}, "msw": {"workerDirectory": ["static"]}}