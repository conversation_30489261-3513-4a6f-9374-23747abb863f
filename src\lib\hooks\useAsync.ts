/**
 * 异步操作 Hook
 *
 * 提供异步操作的状态管理和错误处理
 *
 * @category Hooks
 */

import { onDestroy } from 'svelte';
import { derived, type Readable, type Writable, writable } from 'svelte/store';

import { createModuleLogger } from '$lib/utils/logger';

const asyncLogger = createModuleLogger('use-async');

/**
 * 异步状态
 */
export interface AsyncState<T> {
  /** 数据 */
  data: T | null;
  /** 是否正在加载 */
  loading: boolean;
  /** 错误信息 */
  error: Error | null;
  /** 是否已完成（成功或失败） */
  finished: boolean;
}

/**
 * 异步配置
 */
export interface UseAsyncOptions<T> {
  /** 初始数据 */
  initialData?: T;
  /** 是否立即执行 */
  immediate?: boolean;
  /** 重试次数 */
  retryCount?: number;
  /** 重试延迟（毫秒） */
  retryDelay?: number;
  /** 超时时间（毫秒） */
  timeout?: number;
  /** 错误处理函数 */
  onError?: (error: Error) => void;
  /** 成功处理函数 */
  onSuccess?: (data: T) => void;
  /** 完成处理函数 */
  onFinally?: () => void;
}

/**
 * 异步 Hook 返回值
 */
export interface UseAsyncReturn<T, P extends any[] = any[]> {
  /** 异步状态 */
  state: Readable<AsyncState<T>>;
  /** 数据 */
  data: Readable<T | null>;
  /** 是否正在加载 */
  loading: Readable<boolean>;
  /** 错误信息 */
  error: Readable<Error | null>;
  /** 是否已完成 */
  finished: Readable<boolean>;
  /** 执行异步操作 */
  execute: (...params: P) => Promise<T>;
  /** 重试 */
  retry: () => Promise<T>;
  /** 取消操作 */
  cancel: () => void;
  /** 重置状态 */
  reset: () => void;
  /** 手动设置数据 */
  setData: (data: T) => void;
  /** 手动设置错误 */
  setError: (error: Error) => void;
}

/**
 * 异步操作 Hook
 */
export function useAsync<T, P extends any[] = any[]>(
  asyncFunction: (...params: P) => Promise<T>,
  options: UseAsyncOptions<T> = {}
): UseAsyncReturn<T, P> {
  const {
    initialData = null,
    immediate = false,
    retryCount = 0,
    retryDelay = 1000,
    timeout,
    onError,
    onSuccess,
    onFinally,
  } = options;

  let abortController: AbortController | null = null;
  let currentRetryCount = 0;
  let lastParams: P | null = null;

  // 状态管理
  const state = writable<AsyncState<T>>({
    data: initialData,
    loading: false,
    error: null,
    finished: false,
  });

  // 派生状态
  const data = derived(state, ($state) => $state.data);
  const loading = derived(state, ($state) => $state.loading);
  const error = derived(state, ($state) => $state.error);
  const finished = derived(state, ($state) => $state.finished);

  /**
   * 执行异步操作
   */
  async function execute(...params: P): Promise<T> {
    // 取消之前的操作
    cancel();

    // 重置重试计数
    currentRetryCount = 0;
    lastParams = params;

    return executeWithRetry(...params);
  }

  /**
   * 带重试的执行
   */
  async function executeWithRetry(...params: P): Promise<T> {
    // 创建新的 AbortController
    abortController = new AbortController();

    // 更新状态为加载中
    state.update((current) => ({
      ...current,
      loading: true,
      error: null,
      finished: false,
    }));

    asyncLogger.debug('Async operation started', {
      params,
      retryAttempt: currentRetryCount,
    });

    try {
      let promise = asyncFunction(...params);

      // 添加超时支持
      if (timeout) {
        promise = Promise.race([
          promise,
          new Promise<never>((_, reject) => {
            setTimeout(() => {
              reject(new Error(`Operation timed out after ${timeout}ms`));
            }, timeout);
          }),
        ]);
      }

      // 添加取消支持
      if (abortController) {
        promise = Promise.race([
          promise,
          new Promise<never>((_, reject) => {
            abortController!.signal.addEventListener('abort', () => {
              reject(new Error('Operation was cancelled'));
            });
          }),
        ]);
      }

      const result = await promise;

      // 检查是否已被取消
      if (abortController?.signal.aborted) {
        throw new Error('Operation was cancelled');
      }

      // 更新状态为成功
      state.update((current) => ({
        ...current,
        data: result,
        loading: false,
        error: null,
        finished: true,
      }));

      asyncLogger.debug('Async operation succeeded', {
        params,
        result,
        retryAttempt: currentRetryCount,
      });

      // 调用成功回调
      if (onSuccess) {
        try {
          onSuccess(result);
        } catch (callbackError) {
          asyncLogger.error('Error in onSuccess callback', {
            error: callbackError instanceof Error ? callbackError.message : String(callbackError),
          });
        }
      }

      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));

      asyncLogger.error('Async operation failed', {
        params,
        error: error.message,
        retryAttempt: currentRetryCount,
      });

      // 检查是否需要重试
      if (currentRetryCount < retryCount && !abortController?.signal.aborted) {
        currentRetryCount++;

        asyncLogger.info('Retrying async operation', {
          retryAttempt: currentRetryCount,
          maxRetries: retryCount,
          delay: retryDelay,
        });

        // 等待重试延迟
        await new Promise((resolve) => setTimeout(resolve, retryDelay));

        // 递归重试
        return executeWithRetry(...params);
      }

      // 更新状态为失败
      state.update((current) => ({
        ...current,
        loading: false,
        error,
        finished: true,
      }));

      // 调用错误回调
      if (onError) {
        try {
          onError(error);
        } catch (callbackError) {
          asyncLogger.error('Error in onError callback', {
            error: callbackError instanceof Error ? callbackError.message : String(callbackError),
          });
        }
      }

      throw error;
    } finally {
      // 调用完成回调
      if (onFinally) {
        try {
          onFinally();
        } catch (callbackError) {
          asyncLogger.error('Error in onFinally callback', {
            error: callbackError instanceof Error ? callbackError.message : String(callbackError),
          });
        }
      }

      abortController = null;
    }
  }

  /**
   * 重试操作
   */
  async function retry(): Promise<T> {
    if (!lastParams) {
      throw new Error('No previous operation to retry');
    }

    return execute(...lastParams);
  }

  /**
   * 取消操作
   */
  function cancel(): void {
    if (abortController) {
      abortController.abort();
      abortController = null;
    }

    state.update((current) => ({
      ...current,
      loading: false,
    }));

    asyncLogger.debug('Async operation cancelled');
  }

  /**
   * 重置状态
   */
  function reset(): void {
    cancel();

    state.set({
      data: initialData,
      loading: false,
      error: null,
      finished: false,
    });

    currentRetryCount = 0;
    lastParams = null;

    asyncLogger.debug('Async state reset');
  }

  /**
   * 手动设置数据
   */
  function setData(newData: T): void {
    state.update((current) => ({
      ...current,
      data: newData,
      error: null,
    }));

    asyncLogger.debug('Data set manually', { data: newData });
  }

  /**
   * 手动设置错误
   */
  function setError(newError: Error): void {
    state.update((current) => ({
      ...current,
      error: newError,
      loading: false,
      finished: true,
    }));

    asyncLogger.debug('Error set manually', { error: newError.message });
  }

  // 立即执行（如果配置了）
  if (immediate && lastParams === null) {
    // 使用空参数立即执行
    execute(...([] as unknown as P));
  }

  // 清理函数
  onDestroy(() => {
    cancel();
  });

  return {
    state,
    data,
    loading,
    error,
    finished,
    execute,
    retry,
    cancel,
    reset,
    setData,
    setError,
  };
}

/**
 * 简化的异步 Hook
 */
export function useSimpleAsync<T>(
  asyncFunction: () => Promise<T>,
  immediate: boolean = false
): UseAsyncReturn<T, []> {
  return useAsync(asyncFunction, { immediate });
}

/**
 * 异步数据获取 Hook
 */
export function useAsyncData<T>(
  key: string,
  fetcher: () => Promise<T>,
  options: UseAsyncOptions<T> & {
    refreshInterval?: number;
    staleTime?: number;
  } = {}
): UseAsyncReturn<T, []> & {
  refresh: () => Promise<T>;
  isStale: Readable<boolean>;
} {
  const { refreshInterval, staleTime = 5 * 60 * 1000, ...asyncOptions } = options;

  const asyncResult = useAsync(fetcher, asyncOptions);
  const lastFetchTime = writable<number>(0);

  // 计算是否过期
  const isStale = derived([lastFetchTime], ([lastFetch]) => {
    if (lastFetch === 0) return true;
    return Date.now() - lastFetch > staleTime;
  });

  // 刷新数据
  async function refresh(): Promise<T> {
    const result = await asyncResult.execute();
    lastFetchTime.set(Date.now());
    return result;
  }

  // 自动刷新
  let refreshTimer: NodeJS.Timeout | null = null;
  if (refreshInterval) {
    refreshTimer = setInterval(() => {
      if (!asyncResult.loading) {
        refresh();
      }
    }, refreshInterval);
  }

  // 清理定时器
  onDestroy(() => {
    if (refreshTimer) {
      clearInterval(refreshTimer);
    }
  });

  return {
    ...asyncResult,
    refresh,
    isStale,
  };
}
