<!-- src/lib/components/layout/AppSidebar/AppSidebarProvider.svelte -->
<script lang="ts">
  import type { ComponentProps } from 'svelte';

  import * as Sidebar from '$lib/components/ui/sidebar/index.js';

  interface Props extends ComponentProps<typeof Sidebar.Provider> {
    children?: any;
  }

  const { children, ...restProps }: Props = $props();
</script>

<Sidebar.Provider {...restProps}>
  {@render children?.()}
</Sidebar.Provider>
