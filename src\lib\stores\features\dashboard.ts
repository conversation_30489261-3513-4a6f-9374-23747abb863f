// src/lib/stores/features/dashboard.ts
import { derived, writable } from 'svelte/store';

import { <PERSON><PERSON>hart, LineChart, Pie<PERSON>hart, ScatterChart } from '$lib/components/charts';
import { chartsApi, dashboardApi } from '$lib/services/api';
import type { ChartConfig, ChartData, DashboardStats, TimeRange } from '$lib/types';
import { storeLogger } from '$lib/utils/logger';

// 初始图表配置
const initialChartConfigs: ChartConfig[] = [
  {
    id: 'bar',
    component: BarChart,
    visible: true,
    order: 0,
    title: 'Monthly Sales Data',
    subTitle: '柱状图',
  },
  {
    id: 'line',
    component: LineChart,
    visible: true,
    order: 1,
    title: 'User Growth Trend',
    subTitle: '折线图',
  },
  {
    id: 'pie',
    component: PieChart,
    visible: true,
    order: 2,
    title: 'Product Category Distribution',
    subTitle: '饼图',
  },
  {
    id: 'scatter',
    component: ScatterChart,
    visible: true,
    order: 3,
    title: 'User Behavior Analysis',
    subTitle: '散点图',
  },
];

// 初始状态
const initialStats: DashboardStats = {
  totalUsers: 0,
  monthlyRevenue: 0,
  conversionRate: 0,
  activeUsers: 0,
};

const initialChartData: ChartData = {
  barChartData: [],
  lineChartData: [],
  pieChartData: [],
  scatterChartData: [],
};

/**
 * 仪表板状态管理
 *
 * 管理仪表板的图表配置、数据、加载状态、时间范围等所有相关状态。
 * 提供数据加载、刷新、配置管理等功能。
 *
 * @example
 * ```typescript
 * import { dashboardStore } from '$lib/stores/features/dashboard';
 *
 * // 订阅状态变化
 * dashboardStore.subscribe(state => {
 *   storeLogger.debug('仪表板状态更新', { state });
 * });
 *
 * // 加载数据
 * await dashboardStore.loadDashboardData();
 *
 * // 设置时间范围
 * dashboardStore.setTimeRange({
 *   selectedTimeRange: 'month',
 *   selectedTimeZone: 'UTC'
 * });
 *
 * // 切换图表可见性
 * dashboardStore.toggleChartVisibility('chart-1');
 * ```
 *
 * @category Stores
 */
function createDashboardStore() {
  const { subscribe, update, set } = writable({
    chartConfigs: initialChartConfigs,
    stats: initialStats,
    chartData: initialChartData,
    selectedTimeRange: 'month' as TimeRange,
    customStartTime: '',
    customEndTime: '',
    selectedTimeZone: 'UTC',
    isLoading: false,
    error: null as string | null,
    refreshInterval: null as null | number,
  });

  // 派生 stores
  const charts = derived({ subscribe }, ($state) => $state.chartConfigs);
  const stats = derived({ subscribe }, ($state) => $state.stats);
  const chartData = derived({ subscribe }, ($state) => $state.chartData);
  const loading = derived({ subscribe }, ($state) => $state.isLoading);
  const error = derived({ subscribe }, ($state) => $state.error);

  return {
    subscribe,
    charts,
    stats,
    chartData,
    loading,
    error,

    // 加载仪表板数据
    async loadDashboardData() {
      update((state) => ({ ...state, isLoading: true, error: null }));

      try {
        // 并行加载统计数据和图表数据
        const [statsResponse, chartDataResponse] = await Promise.all([
          dashboardApi.getStats(),
          chartsApi.getAllChartData(),
        ]);

        update((state) => ({
          ...state,
          stats: statsResponse.data,
          chartData: chartDataResponse.data,
          isLoading: false,
        }));
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '加载数据失败';
        update((state) => ({
          ...state,
          isLoading: false,
          error: errorMessage,
        }));
      }
    },

    // 刷新统计数据
    async refreshStats() {
      try {
        const response = await dashboardApi.getStats();
        update((state) => ({ ...state, stats: response.data }));
        storeLogger.debug('Dashboard stats refreshed successfully');
      } catch (error) {
        storeLogger.error('Failed to refresh dashboard stats', {
          error: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
        });
      }
    },

    // 刷新图表数据
    async refreshChartData() {
      try {
        const response = await chartsApi.getAllChartData();
        update((state) => ({ ...state, chartData: response.data }));
        storeLogger.debug('Dashboard chart data refreshed successfully');
      } catch (error) {
        storeLogger.error('Failed to refresh dashboard chart data', {
          error: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
        });
      }
    },

    // 设置加载状态
    setLoading: (isLoading: boolean) => {
      update((state) => ({ ...state, isLoading }));
    },

    // 清除错误
    clearError: () => {
      update((state) => ({ ...state, error: null }));
    },

    // 设置时间范围
    setTimeRange: ({
      selectedTimeRange,
      customStartTime = '',
      customEndTime = '',
      selectedTimeZone = 'UTC',
    }: {
      selectedTimeRange: TimeRange | string;
      customStartTime?: string;
      customEndTime?: string;
      selectedTimeZone?: string;
    }) => {
      update((state) => ({
        ...state,
        selectedTimeRange: selectedTimeRange as TimeRange,
        customStartTime,
        customEndTime,
        selectedTimeZone,
      }));
    },

    // 切换图表可见性
    toggleChartVisibility: (chartId: string) => {
      update((state) => {
        const chartConfigs = state.chartConfigs.map((config) => {
          if (config.id === chartId) {
            return { ...config, visible: !config.visible };
          }
          return config;
        });
        return { ...state, chartConfigs };
      });
    },

    // 重新排序图表
    reorderCharts: (newOrder: string[]) => {
      update((state) => {
        const orderMap = new Map<string, number>();
        newOrder.forEach((id, index) => {
          orderMap.set(id, index);
        });

        const chartConfigs = state.chartConfigs.map((config) => {
          if (orderMap.has(config.id)) {
            return { ...config, order: orderMap.get(config.id)! };
          }
          return config;
        });

        chartConfigs.sort((a, b) => a.order - b.order);
        return { ...state, chartConfigs };
      });
    },

    // 设置刷新间隔
    setRefreshInterval: (interval: number | null) => {
      update((state) => ({ ...state, refreshInterval: interval }));
    },

    // 重置配置
    reset: () => {
      set({
        chartConfigs: initialChartConfigs,
        stats: initialStats,
        chartData: initialChartData,
        selectedTimeRange: 'month',
        customStartTime: '',
        customEndTime: '',
        selectedTimeZone: 'UTC',
        isLoading: false,
        error: null,
        refreshInterval: null,
      });
    },
  };
}

// 导出单例 store
export const dashboardStore = createDashboardStore();
