<script lang="ts">
  // shadcn-svelte 组件导入
  import { Badge } from '$lib/components/ui/badge';
  import { Card, CardContent, CardTitle } from '$lib/components/ui/card';

  export let title: string;
  export let value: number;
  export let icon: string;
  export let color: string = 'blue';
  export let prefix: string = '';
  export let suffix: string = '';

  // 格式化值
  $: formattedValue = value.toLocaleString();

  // 颜色映射到 shadcn-svelte 的变体
  $: badgeVariant = (
    color === 'blue'
      ? 'default'
      : color === 'green'
        ? 'secondary'
        : color === 'purple'
          ? 'outline'
          : color === 'orange'
            ? 'destructive'
            : 'default'
  ) as 'default' | 'secondary' | 'outline' | 'destructive';
</script>

<Card class="hover:shadow-lg">
  <CardContent class="flex items-center justify-between p-4">
    <div class="space-y-1">
      <CardTitle class="text-muted-foreground text-xs font-medium">
        {title}
      </CardTitle>
      <p class="text-foreground text-xl font-semibold">
        {prefix}{formattedValue}{suffix}
      </p>
    </div>
    <Badge variant={badgeVariant} class="rounded-full p-2 text-lg">
      {icon}
    </Badge>
  </CardContent>
</Card>
