/**
 * 图表更新管理器
 * 负责管理图表的更新队列、性能监控和批量更新
 */

import { createModuleLogger } from './logger';

const updateLogger = createModuleLogger('chart-update-manager');

interface UpdateTask {
  chartId: string;
  updateFn: () => void;
  priority: number;
  timestamp: number;
}

interface PerformanceStats {
  chartId: string;
  updateCount: number;
  totalUpdateTime: number;
  averageUpdateTime: number;
  lastUpdateTime: number;
  errors: number;
}

class ChartUpdateManager {
  private updateQueue: UpdateTask[] = [];
  private isProcessing = false;
  private performanceStats = new Map<string, PerformanceStats>();
  private updateTimers = new Map<string, NodeJS.Timeout>();
  private batchUpdateDelay = 16; // 16ms for 60fps

  /**
   * 添加更新任务到队列
   */
  scheduleUpdate(chartId: string, updateFn: () => void, priority = 0, delay?: number): void {
    // 清除已存在的定时器
    const existingTimer = this.updateTimers.get(chartId);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // 移除队列中相同chartId的任务
    this.updateQueue = this.updateQueue.filter((task) => task.chartId !== chartId);

    // 添加新任务
    const task: UpdateTask = {
      chartId,
      updateFn,
      priority,
      timestamp: Date.now(),
    };

    this.updateQueue.push(task);
    this.updateQueue.sort((a, b) => b.priority - a.priority);

    // 设置批量更新定时器
    const updateDelay = delay ?? this.batchUpdateDelay;
    const timer = setTimeout(() => {
      this.processQueue();
    }, updateDelay);

    this.updateTimers.set(chartId, timer);

    updateLogger.debug('Update scheduled', {
      chartId,
      priority,
      queueLength: this.updateQueue.length,
    });
  }

  /**
   * 强制立即更新指定图表
   */
  forceUpdate(chartId: string): void {
    const task = this.updateQueue.find((t) => t.chartId === chartId);
    if (task) {
      this.executeUpdate(task);
      this.updateQueue = this.updateQueue.filter((t) => t.chartId !== chartId);
    }
  }

  /**
   * 取消指定图表的更新
   */
  cancelUpdate(chartId: string): void {
    const timer = this.updateTimers.get(chartId);
    if (timer) {
      clearTimeout(timer);
      this.updateTimers.delete(chartId);
    }

    this.updateQueue = this.updateQueue.filter((task) => task.chartId !== chartId);
    updateLogger.debug('Update cancelled', { chartId });
  }

  /**
   * 处理更新队列
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.updateQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    try {
      // 批量处理更新
      const tasksToProcess = [...this.updateQueue];
      this.updateQueue = [];

      for (const task of tasksToProcess) {
        await this.executeUpdate(task);
      }
    } catch (error) {
      updateLogger.error('Queue processing failed', {
        error: error instanceof Error ? error.message : String(error),
      });
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * 执行单个更新任务
   */
  private async executeUpdate(task: UpdateTask): Promise<void> {
    const startTime = performance.now();

    try {
      task.updateFn();

      const endTime = performance.now();
      const updateTime = endTime - startTime;

      this.recordPerformance(task.chartId, updateTime, false);

      updateLogger.debug('Update completed', {
        chartId: task.chartId,
        updateTime: Math.round(updateTime),
      });
    } catch (error) {
      this.recordPerformance(task.chartId, 0, true);

      updateLogger.error('Update failed', {
        chartId: task.chartId,
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  /**
   * 记录性能统计
   */
  private recordPerformance(chartId: string, updateTime: number, isError: boolean): void {
    const existing = this.performanceStats.get(chartId);

    if (existing) {
      const newStats: PerformanceStats = {
        ...existing,
        updateCount: existing.updateCount + 1,
        totalUpdateTime: existing.totalUpdateTime + updateTime,
        averageUpdateTime: (existing.totalUpdateTime + updateTime) / (existing.updateCount + 1),
        lastUpdateTime: updateTime,
        errors: existing.errors + (isError ? 1 : 0),
      };
      this.performanceStats.set(chartId, newStats);
    } else {
      this.performanceStats.set(chartId, {
        chartId,
        updateCount: 1,
        totalUpdateTime: updateTime,
        averageUpdateTime: updateTime,
        lastUpdateTime: updateTime,
        errors: isError ? 1 : 0,
      });
    }
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats(chartId?: string): PerformanceStats | PerformanceStats[] {
    if (chartId) {
      return (
        this.performanceStats.get(chartId) || {
          chartId,
          updateCount: 0,
          totalUpdateTime: 0,
          averageUpdateTime: 0,
          lastUpdateTime: 0,
          errors: 0,
        }
      );
    }

    return Array.from(this.performanceStats.values());
  }

  /**
   * 清除性能统计
   */
  clearPerformanceStats(chartId?: string): void {
    if (chartId) {
      this.performanceStats.delete(chartId);
    } else {
      this.performanceStats.clear();
    }
  }

  /**
   * 获取队列状态
   */
  getQueueStatus(): {
    queueLength: number;
    isProcessing: boolean;
    pendingCharts: string[];
  } {
    return {
      queueLength: this.updateQueue.length,
      isProcessing: this.isProcessing,
      pendingCharts: this.updateQueue.map((task) => task.chartId),
    };
  }

  /**
   * 设置批量更新延迟
   */
  setBatchUpdateDelay(delay: number): void {
    this.batchUpdateDelay = Math.max(1, delay);
    updateLogger.info('Batch update delay changed', { delay: this.batchUpdateDelay });
  }

  /**
   * 清理所有资源
   */
  cleanup(): void {
    // 清除所有定时器
    for (const timer of this.updateTimers.values()) {
      clearTimeout(timer);
    }
    this.updateTimers.clear();

    // 清空队列
    this.updateQueue = [];
    this.isProcessing = false;

    // 清除性能统计
    this.performanceStats.clear();

    updateLogger.info('ChartUpdateManager cleaned up');
  }
}

// 创建全局实例
export const chartUpdateManager = new ChartUpdateManager();

// 在浏览器环境中，页面卸载时清理资源
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    chartUpdateManager.cleanup();
  });
}
