<!-- src/routes/test/cache/+page.svelte -->
<script lang="ts">
  import { Database, RefreshCw, TestTube, Zap } from '@lucide/svelte';
  import { onMount } from 'svelte';

  import { Button } from '$lib/components/ui/button';
  import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
  import type { CacheStats } from '$lib/services/cache/currencyPersistentCache';
  import { currencyCacheStore } from '$lib/stores/features/currencyCache';
  import { notifications } from '$lib/stores/ui/notifications';
  import type { CoinInfo } from '$lib/types';
  import { formatBytes, formatNumber } from '$lib/utils/formatters';

  // 测试用的货币ID列表
  const testCurrencyIds = [
    '01JP2G8RNR4JBJP81JS5JNJQ89', // STRX
    '01HEPMMG27T0J8Y1RKW74XHXTM', // BTC
    '01HAF64YMSYF7BPH8JJRWVATT5', // ETH
    '01HAF64YNKTZJ3EEF6EHFAHVFF', // XRP
    '01HAF64YN0PNRDNMNB2K0AH6DY', // USDT
  ];

  // 状态
  let isLoading = $state(false);
  let testResults: Array<{
    currencyId: string;
    coinInfo: CoinInfo | null;
    loadTime: number;
    fromCache: boolean;
  }> = $state([]);
  let stats: CacheStats = $state({
    totalItems: 0,
    sizeInBytes: 0,
    hitCount: 0,
    missCount: 0,
    hitRate: 0,
    lastCleanupAt: Date.now(),
  });

  /**
   * 刷新统计信息
   */
  function refreshStats(): void {
    stats = currencyCacheStore.getStats();
  }

  /**
   * 测试单个货币信息获取
   */
  async function testSingleCurrency(
    currencyId: string,
    forceRefresh: boolean = false
  ): Promise<{
    currencyId: string;
    coinInfo: CoinInfo | null;
    loadTime: number;
    fromCache: boolean;
  }> {
    const startTime = performance.now();
    const wasInCache = currencyCacheStore.getCachedCurrency(currencyId) !== null;

    const coinInfo = await currencyCacheStore.getCurrency(currencyId, forceRefresh);
    const endTime = performance.now();

    return {
      currencyId,
      coinInfo,
      loadTime: endTime - startTime,
      fromCache: wasInCache && !forceRefresh,
    };
  }

  /**
   * 测试缓存性能
   */
  async function testCachePerformance(): Promise<void> {
    if (isLoading) return;

    try {
      isLoading = true;
      testResults = [];

      notifications.add({
        type: 'info',
        title: '开始缓存测试',
        message: '正在测试缓存系统性能...',
        duration: 3000,
      });

      // 第一轮：从 API 获取（应该较慢）
      console.log('第一轮测试：从 API 获取数据');
      for (const currencyId of testCurrencyIds) {
        const result = await testSingleCurrency(currencyId, true); // 强制刷新
        testResults.push(result);
        console.log(`${currencyId}: ${result.loadTime.toFixed(2)}ms (API)`);
      }

      // 等待一秒
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // 第二轮：从缓存获取（应该很快）
      console.log('第二轮测试：从缓存获取数据');
      for (const currencyId of testCurrencyIds) {
        const result = await testSingleCurrency(currencyId, false); // 使用缓存
        testResults.push(result);
        console.log(`${currencyId}: ${result.loadTime.toFixed(2)}ms (Cache)`);
      }

      refreshStats();

      notifications.add({
        type: 'success',
        title: '缓存测试完成',
        message: `测试了 ${testCurrencyIds.length * 2} 次请求，查看结果详情`,
        duration: 5000,
      });
    } catch (error) {
      notifications.add({
        type: 'error',
        title: '缓存测试失败',
        message: error instanceof Error ? error.message : '未知错误',
        duration: 5000,
      });
    } finally {
      isLoading = false;
    }
  }

  /**
   * 清除缓存
   */
  async function clearCache(): Promise<void> {
    try {
      currencyCacheStore.clearCache();
      testResults = [];
      refreshStats();

      notifications.add({
        type: 'success',
        title: '缓存已清除',
        message: '所有缓存数据已清除',
        duration: 3000,
      });
    } catch (error) {
      notifications.add({
        type: 'error',
        title: '清除缓存失败',
        message: error instanceof Error ? error.message : '未知错误',
        duration: 5000,
      });
    }
  }

  /**
   * 计算平均加载时间
   */
  function getAverageLoadTime(fromCache: boolean): number {
    const filteredResults = testResults.filter((r) => r.fromCache === fromCache);
    if (filteredResults.length === 0) return 0;

    const totalTime = filteredResults.reduce((sum, r) => sum + r.loadTime, 0);
    return totalTime / filteredResults.length;
  }

  // 组件挂载时刷新统计信息
  onMount(() => {
    refreshStats();
  });
</script>

<svelte:head>
  <title>缓存系统测试 - Svelte Demo</title>
</svelte:head>

<div class="container mx-auto space-y-6 p-6">
  <!-- 页面标题 -->
  <div class="flex items-center gap-3">
    <TestTube class="text-muted-foreground h-8 w-8" />
    <div>
      <h1 class="text-3xl font-bold tracking-tight">缓存系统测试</h1>
      <p class="text-muted-foreground">测试智能货币信息缓存系统的性能和功能</p>
    </div>
  </div>

  <!-- 缓存统计 -->
  <Card>
    <CardHeader>
      <CardTitle class="flex items-center gap-2">
        <Database class="h-5 w-5" />
        缓存统计
      </CardTitle>
    </CardHeader>
    <CardContent>
      <div class="grid grid-cols-2 gap-4 text-sm md:grid-cols-4">
        <div class="space-y-1">
          <div class="text-muted-foreground">缓存项数</div>
          <div class="text-2xl font-bold">{formatNumber(stats.totalItems)}</div>
        </div>
        <div class="space-y-1">
          <div class="text-muted-foreground">缓存大小</div>
          <div class="text-2xl font-bold">{formatBytes(stats.sizeInBytes)}</div>
        </div>
        <div class="space-y-1">
          <div class="text-muted-foreground">命中率</div>
          <div class="text-2xl font-bold text-green-600">{(stats.hitRate * 100).toFixed(1)}%</div>
        </div>
        <div class="space-y-1">
          <div class="text-muted-foreground">总请求</div>
          <div class="text-2xl font-bold">{formatNumber(stats.hitCount + stats.missCount)}</div>
        </div>
      </div>
    </CardContent>
  </Card>

  <!-- 测试控制 -->
  <Card>
    <CardHeader>
      <CardTitle class="flex items-center gap-2">
        <Zap class="h-5 w-5" />
        性能测试
      </CardTitle>
    </CardHeader>
    <CardContent class="space-y-4">
      <div class="flex gap-3">
        <Button onclick={testCachePerformance} disabled={isLoading} class="flex items-center gap-2">
          <TestTube class="h-4 w-4" />
          {isLoading ? '测试中...' : '开始性能测试'}
        </Button>

        <Button variant="outline" onclick={refreshStats} class="flex items-center gap-2">
          <RefreshCw class="h-4 w-4" />
          刷新统计
        </Button>

        <Button variant="destructive" onclick={clearCache} class="flex items-center gap-2">
          <Database class="h-4 w-4" />
          清除缓存
        </Button>
      </div>

      {#if testResults.length > 0}
        <div class="space-y-3">
          <h4 class="font-medium">测试结果</h4>

          <!-- 性能摘要 -->
          <div class="bg-muted/50 grid grid-cols-2 gap-4 rounded-lg p-4">
            <div>
              <div class="text-muted-foreground text-sm">API 平均响应时间</div>
              <div class="text-lg font-bold text-red-600">
                {getAverageLoadTime(false).toFixed(2)}ms
              </div>
            </div>
            <div>
              <div class="text-muted-foreground text-sm">缓存平均响应时间</div>
              <div class="text-lg font-bold text-green-600">
                {getAverageLoadTime(true).toFixed(2)}ms
              </div>
            </div>
          </div>

          <!-- 详细结果 -->
          <div class="space-y-2">
            {#each testResults as result}
              <div class="flex items-center justify-between rounded-lg border p-3">
                <div class="flex items-center gap-3">
                  <div class="font-mono text-sm">{result.currencyId}</div>
                  <div class="text-sm">
                    {result.coinInfo?.symbol || 'Unknown'} - {result.coinInfo?.name || 'Unknown'}
                  </div>
                </div>
                <div class="flex items-center gap-3">
                  <div class="text-sm {result.fromCache ? 'text-green-600' : 'text-red-600'}">
                    {result.fromCache ? '缓存' : 'API'}
                  </div>
                  <div class="font-mono text-sm">
                    {result.loadTime.toFixed(2)}ms
                  </div>
                </div>
              </div>
            {/each}
          </div>
        </div>
      {/if}
    </CardContent>
  </Card>
</div>
