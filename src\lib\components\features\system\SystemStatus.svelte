<!-- src/lib/components/features/system/SystemStatus.svelte -->
<script lang="ts">
  import { Badge } from '$lib/components/ui/badge';
  import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
  } from '$lib/components/ui/tooltip';
  import type { NetworkStatus } from '$lib/utils/networkMonitor';
  import {
    destroyNetworkMonitor,
    initializeNetworkMonitor,
    isOnline,
    networkQuality,
    networkStatus,
  } from '$lib/utils/networkMonitor';

  // Svelte 5 响应式状态
  let currentTime = $state(new Date());
  let updateInterval: NodeJS.Timeout | null = null;
  let isInitializing = $state(true);

  // 订阅网络状态
  const status = $derived($networkStatus);
  const quality = $derived($networkQuality);
  const online = $derived($isOnline);

  // 检测是否完成初始化
  $effect(() => {
    // 网络监控初始化后，给一个短暂的时间进行首次检测
    const initTimeout = setTimeout(() => {
      if (status.latency !== undefined || !online) {
        // 如果有延迟数据或者明确离线，说明已完成初始检测
        isInitializing = false;
      } else {
        // 即使没有延迟数据，也在合理时间后停止初始化状态
        isInitializing = false;
      }
    }, 1500); // 缩短等待时间到1.5秒

    return () => clearTimeout(initTimeout);
  });

  // 生命周期管理
  $effect(() => {
    // 初始化网络监控
    initializeNetworkMonitor();

    // 启动时间更新定时器
    updateInterval = setInterval(() => {
      currentTime = new Date();
    }, 1000);

    // 清理函数
    return () => {
      if (updateInterval) {
        clearInterval(updateInterval);
        updateInterval = null;
      }
      // 注意：不在这里销毁 NetworkMonitor，因为可能有其他组件在使用
      // destroyNetworkMonitor();
    };
  });

  // 格式化时间
  function formatTime(date: Date): string {
    return date.toLocaleTimeString('zh-CN', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  }

  // 获取 Badge 背景色样式类
  function getBadgeBackgroundClass(
    quality: NetworkStatus['quality'],
    initializing: boolean = false
  ): string {
    if (initializing) {
      return 'bg-gray-400/80 text-gray-100 border-gray-400/50 dark:bg-gray-600/80 dark:text-gray-200 dark:border-gray-600/50';
    }

    switch (quality) {
      case 'excellent':
        return 'bg-green-600/90 text-white border-green-600/50 dark:bg-green-700/90 dark:border-green-700/50';
      case 'good':
        return 'bg-blue-500/90 text-white border-blue-500/50 dark:bg-blue-700/90 dark:border-blue-700/50';
      case 'poor':
        return 'bg-yellow-500/90 text-gray-900 border-yellow-500/50 dark:bg-yellow-600/90 dark:text-gray-100 dark:border-yellow-600/50';
      case 'offline':
        return 'bg-red-500/90 text-white border-red-500/50 dark:bg-red-700/90 dark:border-red-700/50';
      default:
        return 'bg-gray-500/90 text-white border-gray-500/50 dark:bg-gray-700/90 dark:border-gray-700/50';
    }
  }

  // 获取网络质量文本
  function getQualityText(quality: NetworkStatus['quality']): string {
    switch (quality) {
      case 'excellent':
        return '优秀';
      case 'good':
        return '良好';
      case 'poor':
        return '较差';
      case 'offline':
        return '离线';
      default:
        return '未知';
    }
  }

  // 获取网络质量图标
  function getQualityIcon(
    quality: NetworkStatus['quality'],
    initializing: boolean = false
  ): string {
    if (initializing) {
      return '⚪';
    }

    switch (quality) {
      case 'excellent':
        return '🟢';
      case 'good':
        return '🔵';
      case 'poor':
        return '🟡';
      case 'offline':
        return '🔴';
      default:
        return '⚪';
    }
  }

  // 获取连接类型显示文本
  function getConnectionTypeText(connectionType?: string): string {
    if (!connectionType) return '';

    switch (connectionType) {
      case '4g':
        return '4G';
      case '3g':
        return '3G';
      case '2g':
        return '2G';
      case 'slow-2g':
        return '2G慢速';
      case 'wifi':
        return 'WiFi';
      case 'ethernet':
        return '以太网';
      default:
        return connectionType.toUpperCase();
    }
  }

  // 格式化延迟显示
  function formatLatency(latency?: number): string {
    if (latency === undefined) return '';
    return `${latency}ms`;
  }
</script>

<div class="flex items-center gap-2 text-xs">
  <!-- 网络状态和质量 -->
  <TooltipProvider>
    <Tooltip>
      <TooltipTrigger>
        <Badge variant="outline" class="text-xs {getBadgeBackgroundClass(quality, isInitializing)}">
          <div class="flex items-center gap-1.5">
            <!-- 网络状态文本 -->
            <span class="font-medium">
              {#if isInitializing}
                检测中
              {:else}
                {online ? '在线' : '离线'}
              {/if}
            </span>
            <!-- 网络质量文本 -->
            {#if !isInitializing && online && quality !== 'offline'}
              <span class="text-xs opacity-75">({getQualityText(quality)})</span>
            {/if}
          </div>
        </Badge>
      </TooltipTrigger>
      <TooltipContent>
        <div class="space-y-2 text-sm">
          <div class="border-b pb-1 font-medium">网络状态详情</div>
          <div class="space-y-1">
            <div class="flex justify-between">
              <span class="text-muted-foreground">连接状态:</span>
              <span class="font-medium">{online ? '正常' : '断开'}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-muted-foreground">网络质量:</span>
              <span class="font-medium">{getQualityText(quality)}</span>
            </div>
            {#if status.latency !== undefined}
              <div class="flex justify-between">
                <span class="text-muted-foreground">延迟:</span>
                <span class="font-medium">{formatLatency(status.latency)}</span>
              </div>
            {/if}
            {#if status.connectionType}
              <div class="flex justify-between">
                <span class="text-muted-foreground">连接类型:</span>
                <span class="font-medium">{getConnectionTypeText(status.connectionType)}</span>
              </div>
            {/if}
            {#if status.downloadSpeed !== undefined}
              <div class="flex justify-between">
                <span class="text-muted-foreground">下载速度:</span>
                <span class="font-medium">{status.downloadSpeed.toFixed(1)} Mbps</span>
              </div>
            {/if}
            <div class="flex justify-between border-t pt-1">
              <span class="text-muted-foreground">最后检测:</span>
              <span class="font-medium">{formatTime(status.lastChecked)}</span>
            </div>
          </div>
        </div>
      </TooltipContent>
    </Tooltip>
  </TooltipProvider>
</div>
