<script lang="ts">
  import { goto } from '$app/navigation';
  import { Badge } from '$lib/components/ui/badge';
  // shadcn-svelte 组件导入
  import { Button } from '$lib/components/ui/button';
  import { Separator } from '$lib/components/ui/separator';
  import { activeTabId, closeTab, setActiveTab, tabs } from '$lib/stores/ui';
  import { cn } from '$lib/utils';

  // 从上下文获取图标组件，如果需要的话
  // const Icon = getContext('icon') as ComponentType;

  async function handleTabClick(tabId: string) {
    setActiveTab(tabId);
    // 导航到对应的页面
    const tab = $tabs.find((t) => t.id === tabId);
    if (tab) {
      await goto(tab.href);
    }
  }

  async function handleCloseClick(tabId: string, event: MouseEvent) {
    event.stopPropagation(); // 阻止事件冒泡到标签页点击事件

    // 获取当前活动标签页
    const currentActiveTabId = $activeTabId;

    // 关闭标签页
    closeTab(tabId);

    // 如果关闭的是当前活动标签页，需要导航到新的活动标签页
    if (currentActiveTabId === tabId && $activeTabId && $activeTabId !== tabId) {
      const newActiveTab = $tabs.find((t) => t.id === $activeTabId);
      if (newActiveTab) {
        await goto(newActiveTab.href);
      }
    }
  }
</script>

<div class={cn('flex h-9 items-center overflow-x-auto bg-transparent', 'scrollbar-none')}>
  {#each $tabs as tab, index (tab.id)}
    <div
      role="tab"
      tabindex="0"
      class={cn(
        'group relative flex cursor-pointer items-center gap-2 rounded-md px-3 py-1.5 text-sm font-medium transition-all duration-200',
        'focus-visible:ring-ring focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none',
        tab.id === $activeTabId
          ? 'bg-sidebar-accent text-sidebar-accent-foreground shadow-sm'
          : 'text-sidebar-foreground/70 hover:bg-sidebar-accent/50 hover:text-sidebar-accent-foreground'
      )}
      on:click={() => handleTabClick(tab.id)}
      on:keydown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') handleTabClick(tab.id);
      }}
      aria-selected={tab.id === $activeTabId}
      aria-controls={`panel-${tab.id}`}
      aria-label="切换到 {tab.label} 标签页"
    >
      {#if tab.icon}
        <svelte:component this={tab.icon} class="h-4 w-4 shrink-0" />
      {/if}
      <span class="truncate">{tab.label}</span>
      {#if tab.badge && tab.badge > 0}
        <Badge variant="destructive" class="h-5 min-w-5 px-1.5 text-xs font-medium">
          {tab.badge > 99 ? '99+' : tab.badge}
        </Badge>
      {/if}
      {#if tab.closeable}
        <Button
          variant="ghost"
          size="sm"
          class={cn(
            'h-5 w-5 p-0 opacity-0 transition-opacity duration-200',
            'hover:bg-destructive/10 hover:text-destructive group-hover:opacity-100',
            'focus-visible:opacity-100'
          )}
          onclick={(e) => handleCloseClick(tab.id, e)}
          aria-label="关闭 {tab.label} 标签页"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-3 w-3"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            stroke-width="2"
          >
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </Button>
      {/if}
    </div>

    <!-- 在标签页之间添加分隔线，但不在最后一个标签页后添加 -->
    {#if index < $tabs.length - 1}
      <Separator
        orientation="vertical"
        class="bg-sidebar-border mx-2 opacity-80 data-[orientation=vertical]:h-5 data-[orientation=vertical]:w-px"
      />
    {/if}
  {/each}
</div>

<style lang="postcss">
  /* 隐藏滚动条但保持滚动功能 */
  .scrollbar-none {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
  .scrollbar-none::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }
</style>
