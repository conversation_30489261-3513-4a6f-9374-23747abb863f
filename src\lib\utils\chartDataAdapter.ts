import type { LiquidationData } from '$lib/types';

// 饼图数据项接口
export interface PieChartDataItem {
  name: string;
  value: number;
}

/**
 * 将爆仓数据转换为多空饼图数据
 * @param data 爆仓数据
 * @returns 饼图数据
 */
export function convertToLongShortPieData(data: LiquidationData[]): PieChartDataItem[] {
  if (!data || data.length === 0) {
    return [
      { name: '多头', value: 0 },
      { name: '空头', value: 0 },
    ];
  }

  // 按多空分组统计金额
  const longData = data.filter((item) => item.side === 1);
  const shortData = data.filter((item) => item.side === 0);

  const longAmount = longData.reduce((sum, item) => sum + item.amountUsd, 0);
  const shortAmount = shortData.reduce((sum, item) => sum + item.amountUsd, 0);

  return [
    { name: '多头', value: longAmount },
    { name: '空头', value: shortAmount },
  ];
}

/**
 * 将爆仓数据转换为订单规模饼图数据
 * @param data 爆仓数据
 * @returns 饼图数据
 */
export function convertToThresholdPieData(data: LiquidationData[]): PieChartDataItem[] {
  // 定义 threshold 到标签的映射
  const thresholdLabels: Record<string, string> = {
    '10000': '小订单',
    '50000': '中订单',
    '100000': '大订单',
    '500000': '超大订单',
    '1000000': '超大订单',
  };

  if (!data || data.length === 0) {
    return [
      { name: '小订单', value: 0 },
      { name: '中订单', value: 0 },
      { name: '大订单', value: 0 },
      { name: '超大订单', value: 0 },
    ];
  }

  // 按订单规模分组计数
  const countByThreshold = new Map<string, number>();

  data.forEach((item) => {
    const threshold = item.threshold;
    const label = thresholdLabels[threshold] || '其他订单';
    countByThreshold.set(label, (countByThreshold.get(label) || 0) + 1);
  });

  // 转换为饼图数据，确保顺序一致
  const orderedLabels = ['小订单', '中订单', '大订单', '超大订单'];
  const result: PieChartDataItem[] = [];

  orderedLabels.forEach((label) => {
    const count = countByThreshold.get(label) || 0;
    if (count > 0) {
      // 只包含有数据的项
      result.push({ name: label, value: count });
    }
  });

  // 添加其他未分类的订单（如果有）
  countByThreshold.forEach((count, label) => {
    if (!orderedLabels.includes(label) && count > 0) {
      result.push({ name: label, value: count });
    }
  });

  return result;
}

/**
 * 将爆仓数据转换为币种类型饼图数据
 * @param data 爆仓数据
 * @returns 饼图数据
 */
export function convertToCoinTypePieData(data: LiquidationData[]): PieChartDataItem[] {
  if (!data || data.length === 0) {
    return [
      { name: 'Major', value: 0 },
      { name: 'Altcoin', value: 0 },
    ];
  }

  // 按币种类型分组统计金额
  const majorData = data.filter((item) => item.coinType === 'Major');
  const altcoinData = data.filter((item) => item.coinType === 'Altcoin');

  const majorAmount = majorData.reduce((sum, item) => sum + item.amountUsd, 0);
  const altcoinAmount = altcoinData.reduce((sum, item) => sum + item.amountUsd, 0);

  return [
    { name: '主流币', value: majorAmount },
    { name: '山寨币', value: altcoinAmount },
  ];
}
