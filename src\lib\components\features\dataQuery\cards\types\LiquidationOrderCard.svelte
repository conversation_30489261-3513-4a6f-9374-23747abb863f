<script lang="ts">
  import { CurrencyIcon } from '$lib/components/features/ui';
  import { Badge } from '$lib/components/ui/badge';
  import { Card, CardContent } from '$lib/components/ui/card';

  import { type BaseEventCardProps,formatPrice, formatTimeDiff, formatUsdAmount } from '../utils';

  type Props = BaseEventCardProps

  const { event, isHighlighted = false, onCardClick }: Props = $props();

  // 本地实现 getParameterValue 函数
  function getParameterValue(
    event: any,
    parameterId: string
  ): string | number | boolean | undefined {
    const parameter = event.parameters.find((p: any) => p.parameterId === parameterId);
    return parameter?.value;
  }

  // 提取清算订单相关参数
  const side = getParameterValue(event, 'side') as number;
  const exchangeLabel = getParameterValue(event, 'exchangeLabel') as string;
  const base = getParameterValue(event, 'base') as string;
  const pair = getParameterValue(event, 'pair') as string;
  const amountUsd = getParameterValue(event, 'amountUsd') as number;
  const priceUsd = getParameterValue(event, 'priceUsd') as number;

  // 获取方向文本和样式
  // side=1 是空单爆仓（绿色），side=2 是多单爆仓（红色）
  const isShortLiquidation = side === 1; // 空单爆仓
  const sideText = isShortLiquidation ? 'short' : 'long';

  // 卡片样式 - 清算订单 (LIQUIDATION_ORDER): 16rem - 中等复杂度
  const cardClasses = `
    min-w-36 transition-all duration-200 w-full
    ${
      isHighlighted
        ? 'ring-primary border-primary/50 shadow-lg ring-2'
        : 'hover:shadow-md hover:bg-muted/50'
    }
    ${onCardClick ? 'cursor-pointer' : ''}
  `;

  // 处理卡片点击
  function handleCardClick() {
    onCardClick?.(event);
  }
</script>

<Card
  class={cardClasses}
  onclick={onCardClick ? handleCardClick : undefined}
  role={onCardClick ? 'button' : undefined}
  tabindex={onCardClick ? 0 : undefined}
>
  <CardContent class="p-3">
    <!-- 主要信息行 - 横向布局 -->
    <div class="flex flex-wrap items-start space-x-3">
      <!-- 左侧：加密货币图标 -->
      <div class="flex-shrink-0">
        <CurrencyIcon
          currencyId={event.currency || ''}
          symbol={base}
          size="size-8"
          class="mt-0.5"
        />
      </div>

      <!-- 中间：主要内容 -->
      <div class="flex-1">
        <!-- 第一行：交易所、交易对和时间 -->
        <div class="mb-1 flex items-center space-x-2">
          <span class="text-muted-foreground text-sm">
            {exchangeLabel || '未知'}
          </span>
          {#if pair}
            <span class="text-muted-foreground text-sm">
              {pair}
            </span>
          {/if}
          <span class="text-muted-foreground text-xs">
            • {formatTimeDiff(event.date)}
          </span>
        </div>

        <!-- 第二行：清算描述 -->
        <div class="text-foreground flex flex-wrap items-center gap-1 text-sm leading-relaxed">
          <span>A {sideText} of</span>
          <Badge
            variant={isShortLiquidation ? 'outline' : 'destructive'}
            class={`px-1.5 py-0.5 text-xs ${isShortLiquidation ? 'border-green-600 bg-green-600 text-white dark:border-green-600 dark:bg-green-600 dark:text-white' : ''}`}
          >
            {formatUsdAmount(amountUsd)}
          </Badge>
          <span>has been liquidated at {formatPrice(priceUsd)}</span>
        </div>
      </div>
    </div>
  </CardContent>
</Card>
