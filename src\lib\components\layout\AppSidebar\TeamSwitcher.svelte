<!-- src/lib/components/layout/AppSidebar/TeamSwitcher.svelte -->
<script lang="ts">
  // shadcn-svelte 组件导入
  import * as Sidebar from '$lib/components/ui/sidebar/index.js';

  import type { TeamInfo } from './types.js';

  interface Props {
    teams: TeamInfo[];
    activeTeam?: TeamInfo;
  }

  const { teams, activeTeam = teams[0] }: Props = $props();

  /**
   * 处理团队切换
   */
  function handleTeamSwitch(team: TeamInfo) {
    console.log('Switching to team:', team.name);
    // 这里可以添加团队切换逻辑
    // 例如：更新全局状态、重新加载数据等
  }

  /**
   * 获取团队计划的显示样式
   */
  function getPlanClass(plan?: string): string {
    switch (plan?.toLowerCase()) {
      case 'enterprise':
      case '企业版':
        return 'text-purple-600 dark:text-purple-400';
      case 'professional':
      case '专业版':
        return 'text-blue-600 dark:text-blue-400';
      case 'startup':
      case '创业版':
        return 'text-green-600 dark:text-green-400';
      case 'free':
      case '免费版':
        return 'text-gray-600 dark:text-gray-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  }
</script>

{#if teams && teams.length > 0}
  <Sidebar.Menu>
    <Sidebar.MenuItem>
      <Sidebar.MenuButton
        size="lg"
        onclick={() => handleTeamSwitch(activeTeam || teams[0])}
        title={activeTeam?.description || '团队信息'}
      >
        <div
          class="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg"
        >
          {#if activeTeam?.logo}
            {@const LogoComponent = activeTeam.logo}
            <LogoComponent class="size-4" />
          {:else}
            <span class="text-sm font-semibold">
              {activeTeam?.name.charAt(0).toUpperCase() || 'T'}
            </span>
          {/if}
        </div>
        <div
          class="grid flex-1 text-left text-sm leading-tight group-data-[collapsible=icon]:hidden"
        >
          <span class="truncate font-semibold">
            {activeTeam?.name || '选择团队'}
          </span>
          {#if activeTeam?.plan}
            <span class="truncate text-xs {getPlanClass(activeTeam.plan)}">
              {activeTeam.plan}
            </span>
          {/if}
        </div>
      </Sidebar.MenuButton>
    </Sidebar.MenuItem>
  </Sidebar.Menu>
{/if}
