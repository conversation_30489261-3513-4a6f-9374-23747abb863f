# Svelte Demo 项目

> 基于 SvelteKit 构建的现代化企业级数据可视化和管理平台

## 📖 项目概览

Svelte Demo 是一个功能丰富的数据可视化平台，专为现代化企业应用而设计。项目采用 SvelteKit 框架，集成了多种图表类型、实时数据处理、用户权限管理等企业级功能。

### 🎯 核心特性

- **📊 多样化图表系统** - 支持柱状图、折线图、饼图、散点图等多种图表类型
- **⚡ 实时数据更新** - 基于 WebSocket 的实时消息推送和数据同步
- **🎨 现代化 UI** - 基于 shadcn/ui 的组件系统，支持暗色/浅色主题切换
- **📱 响应式设计** - 完美适配桌面端、平板和移动设备
- **🔄 状态管理** - 基于 Svelte stores 的响应式状态管理
- **🛡️ 错误处理** - 完善的错误边界和错误恢复机制
- **🚀 性能监控** - 内置性能监控工具，实时追踪应用性能
- **🌐 国际化支持** - 多语言和时区支持
- **🧪 测试覆盖** - 完整的单元测试和集成测试

### 🏗️ 技术栈

- **前端框架**: SvelteKit 2.x + Svelte 5
- **UI 组件库**: shadcn/ui + bits-ui
- **样式系统**: TailwindCSS 4.x
- **图表库**: ECharts + svelte-echarts
- **状态管理**: Svelte stores
- **类型检查**: TypeScript
- **测试框架**: Vitest + Testing Library
- **构建工具**: Vite
- **代码质量**: ESLint + Prettier

## 🚀 快速开始

### 环境要求

- Node.js >= 18.0.0
- npm >= 9.0.0

### 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd svelte-demo

# 安装依赖
npm install

# 复制环境配置文件
cp .env.example .env.local
```

### 配置环境变量

编辑 `.env.local` 文件，配置必要的环境变量：

```env
# API 配置
PUBLIC_API_BASE_URL=https://api.example.com
PUBLIC_WS_URL=wss://ws.example.com

# 功能开关
PUBLIC_ENABLE_MOCK_DATA=true
PUBLIC_ENABLE_REAL_TIME=false

# 性能监控
PUBLIC_ENABLE_PERFORMANCE_MONITOR=true
```

### 启动开发服务器

```bash
# 启动开发环境
npm run dev

# 在浏览器中打开 http://localhost:5173
```

### 构建生产版本

```bash
# 构建生产版本
npm run build

# 预览生产版本
npm run preview
```

## 📁 项目结构

```
svelte-demo/
├── src/
│   ├── routes/                 # 页面路由
│   │   ├── dashboard/         # 仪表板页面
│   │   ├── liquidation/       # 清算功能
│   │   ├── query/            # 数据查询
│   │   └── settings/         # 系统设置
│   ├── lib/
│   │   ├── components/       # 组件库
│   │   │   ├── charts/      # 图表组件
│   │   │   ├── features/    # 功能组件
│   │   │   ├── layout/      # 布局组件
│   │   │   └── ui/          # UI 基础组件
│   │   ├── services/        # API 服务层
│   │   ├── stores/          # 状态管理
│   │   ├── utils/           # 工具函数
│   │   ├── types/           # TypeScript 类型定义
│   │   └── constants/       # 常量定义
│   ├── mocks/               # 模拟数据
│   ├── tests/               # 测试文件
│   └── app.css              # 全局样式
├── static/                  # 静态资源
├── docs/                    # 项目文档
├── tests/                   # E2E 测试
└── 配置文件
```

## 🎨 主要功能

### 仪表板系统

- 实时数据统计展示
- 多种图表类型支持
- 图表拖拽排序
- 自动刷新配置
- 时间范围筛选

### 清算功能

- 清算数据查询和分析
- 清算风险评估
- 清算历史记录
- 实时清算监控

### 数据查询

- 历史数据查询
- 实时消息监听
- 自定义查询条件
- 数据导出功能

### 系统设置

- 主题切换（暗色/浅色模式）
- 语言设置
- 时区配置
- 性能监控设置

## 🛠️ 开发指南

### 开发命令

```bash
# 开发相关
npm run dev              # 启动开发服务器
npm run build           # 构建生产版本
npm run preview         # 预览生产版本

# 代码质量
npm run lint            # 代码检查
npm run format          # 代码格式化
npm run check           # 类型检查

# 测试相关
npm run test            # 运行单元测试
npm run test:run        # 运行一次测试
npm run test:coverage   # 生成测试覆盖率报告
npm run test:ui         # 启动测试 UI

# 文档相关
npm run docs:api        # 生成 API 文档
npm run docs:serve      # 启动文档服务器
```

### 代码规范

项目使用 ESLint 和 Prettier 确保代码质量和风格一致性：

- 使用 TypeScript 进行类型检查
- 遵循 Svelte 官方代码风格指南
- 组件命名采用 PascalCase
- 文件名使用 camelCase
- 导入语句按字母顺序排序

### 组件开发指南

1. **基础组件** (`src/lib/components/ui/`)
   - 基于 shadcn/ui 的可复用基础组件
   - 支持主题系统
   - 完整的 TypeScript 类型定义

2. **图表组件** (`src/lib/components/charts/`)
   - 基于 ECharts 的图表封装
   - 响应式设计
   - 支持实时数据更新

3. **功能组件** (`src/lib/components/features/`)
   - 业务逻辑相关的复合组件
   - 集成状态管理
   - 错误处理机制

## 🧪 测试

项目包含完整的测试体系：

### 单元测试

```bash
# 运行所有单元测试
npm run test

# 运行特定测试文件
npm run test -- --run src/lib/components/charts/BarChart.test.ts

# 生成覆盖率报告
npm run test:coverage
```

### 测试组织

- `src/tests/unit/` - 单元测试
- `src/tests/integration/` - 集成测试
- `tests/` - E2E 测试

## 📊 性能监控

项目内置性能监控系统，可以实时追踪：

- 应用启动时间
- 组件渲染性能
- API 响应时间
- 内存使用情况
- CPU 使用率

在开发环境中，可以通过浏览器控制台查看性能指标。

## 🔧 配置说明

### 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `PUBLIC_API_BASE_URL` | API 基础 URL | - |
| `PUBLIC_WS_URL` | WebSocket URL | - |
| `PUBLIC_ENABLE_MOCK_DATA` | 启用模拟数据 | `false` |
| `PUBLIC_ENABLE_REAL_TIME` | 启用实时功能 | `false` |
| `PUBLIC_ENABLE_PERFORMANCE_MONITOR` | 启用性能监控 | `true` |

### SvelteKit 配置

项目使用 `svelte.config.js` 进行 SvelteKit 配置：

- 自动适配器选择
- Vite 预处理器
- 开发工具集成

### Tailwind 配置

通过 `tailwind.config.ts` 自定义样式系统：

- 自定义颜色主题
- 响应式断点
- 动画效果
- 滚动条样式

## 📚 相关文档

- [架构设计文档](./docs/architecture.md) - 系统架构和设计模式
- [组件开发指南](./docs/components.md) - 组件开发详细说明
- [API 使用指南](./docs/api.md) - API 服务层文档
- [开发最佳实践](./docs/development.md) - 开发流程和最佳实践

## 🤝 贡献指南

欢迎贡献代码和提出建议！请遵循以下步骤：

1. Fork 项目仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 📝 许可证

该项目基于 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 常见问题

### Q: 如何添加新的图表类型？

A: 参考 `src/lib/components/charts/` 目录下的现有图表组件，创建新的图表组件并在 `index.ts` 中导出。

### Q: 如何自定义主题颜色？

A: 修改 `tailwind.config.ts` 中的颜色配置，或在 `app.css` 中添加 CSS 变量。

### Q: 如何启用实时数据功能？

A: 设置环境变量 `PUBLIC_ENABLE_REAL_TIME=true` 并配置正确的 WebSocket URL。

### Q: 性能监控数据在哪里查看？

A: 开发环境下在浏览器控制台查看，生产环境可以集成第三方监控服务。

---

💡 **提示**: 更多详细信息请查看项目文档目录 `docs/` 下的相关文档。